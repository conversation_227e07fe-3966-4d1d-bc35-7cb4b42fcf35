package com.kun.linkage.customer.facade.vo.organization.fee;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 机构手续费明细查询结果VO
 */
public class OrganizationFeeDetailVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id", example = "1")
    private String id;

    /**
     * 机构号
     */
    @Schema(description = "机构号", example = "ORG001")
    private String organizationNo;

    /**
     * 机构名称
     */
    @Schema(description = "机构名称", example = "测试机构")
    private String organizationName;

    /**
     * 手续费类型
     */
    @Schema(description = "手续费类型", example = "01")
    private String feeType;

    /**
     * 手续费币种
     */
    @Schema(description = "手续费币种", example = "USD")
    private String feeCurrencyCode;

    /**
     * 手续费金额
     */
    @Schema(description = "手续费金额", example = "10.00")
    private BigDecimal feeAmount;

    /**
     * 手续费收取方式
     */
    @Schema(description = "手续费收取方式", example = "DEDUCT")
    private String feeCollectionMethod;

    /**
     * 费用日期时间
     */
    @Schema(description = "费用日期时间", example = "2025-07-25 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime feeDatetime;

    /**
     * 手续费收取状态
     */
    @Schema(description = "手续费收取状态(0:未收;1:已收)", example = "1")
    private Integer feeCollectionStatus;

    /**
     * kun币种
     */
    @Schema(description = "kun币种", example = "USDT")
    private String kunCurrencyCode;

    /**
     * kun金额
     */
    @Schema(description = "kun费用金额", example = "10.00")
    private BigDecimal kunAmount;

    /**
     * pxyX币种
     */
    @Schema(description = "pxyX币种", example = "USD")
    private String payXCurrencyCode;

    /**
     * kun金额
     */
    @Schema(description = "kun费用金额", example = "10.00")
    private BigDecimal payXAmount;

    /**
     * 关联交易id
     */
    @Schema(description = "关联交易id", example = "TXN123456")
    private String relatedTransactionId;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "手续费扣除")
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getFeeCurrencyCode() {
        return feeCurrencyCode;
    }

    public void setFeeCurrencyCode(String feeCurrencyCode) {
        this.feeCurrencyCode = feeCurrencyCode;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public String getFeeCollectionMethod() {
        return feeCollectionMethod;
    }

    public void setFeeCollectionMethod(String feeCollectionMethod) {
        this.feeCollectionMethod = feeCollectionMethod;
    }

    public LocalDateTime getFeeDatetime() {
        return feeDatetime;
    }

    public void setFeeDatetime(LocalDateTime feeDatetime) {
        this.feeDatetime = feeDatetime;
    }

    public Integer getFeeCollectionStatus() {
        return feeCollectionStatus;
    }

    public void setFeeCollectionStatus(Integer feeCollectionStatus) {
        this.feeCollectionStatus = feeCollectionStatus;
    }

    public String getKunCurrencyCode() {
        return kunCurrencyCode;
    }

    public void setKunCurrencyCode(String kunCurrencyCode) {
        this.kunCurrencyCode = kunCurrencyCode;
    }

    public BigDecimal getKunAmount() {
        return kunAmount;
    }

    public void setKunAmount(BigDecimal kunAmount) {
        this.kunAmount = kunAmount;
    }

    public String getPayXCurrencyCode() {
        return payXCurrencyCode;
    }

    public void setPayXCurrencyCode(String payXCurrencyCode) {
        this.payXCurrencyCode = payXCurrencyCode;
    }

    public BigDecimal getPayXAmount() {
        return payXAmount;
    }

    public void setPayXAmount(BigDecimal payXAmount) {
        this.payXAmount = payXAmount;
    }

    public String getRelatedTransactionId() {
        return relatedTransactionId;
    }

    public void setRelatedTransactionId(String relatedTransactionId) {
        this.relatedTransactionId = relatedTransactionId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
