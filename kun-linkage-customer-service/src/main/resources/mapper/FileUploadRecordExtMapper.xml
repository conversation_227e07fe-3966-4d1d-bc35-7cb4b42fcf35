<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.customer.ext.mapper.FileUploadRecordExtMapper">

    <select id="selectFileUrlByCustomerIdAndFileId" resultType="java.lang.String">
        SELECT storage_path
        FROM kl_file_upload_record
        WHERE customer_id = #{customerId}
        AND organization_no = #{organizationNo}
        AND file_id IN
        <foreach item="fileId" collection="fileIds" open="(" close=")" separator=",">
            #{fileId}
        </foreach>
    </select>
</mapper>