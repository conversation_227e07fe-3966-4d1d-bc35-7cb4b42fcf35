<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.customer.ext.mapper.OrganizationCustomerCardInfoExtMapper">

    <select id="getOrganizationCustomerCardList" resultType="com.kun.linkage.customer.facade.vo.OrganizationCustomerCardListQueryVO">
        select
        id,
        create_time as openTime,
        organization_no,
        customer_id,
        card_id,
        masked_card_no AS cardNo,
        currency_code as currency,
        card_status,
        card_active_status,
        CONCAT("+",mobile_phone_area," ", mobile_phone) as cardholderMobile,
        email as cardholderEmail
        from kl_organization_customer_card_info
        <where>
            <if test="organizationNo != null and organizationNo != ''">
                and organization_no = #{organizationNo}
            </if>
            <if test="cardId != null and cardId != ''">
                and card_id = #{cardId}
            </if>
            <if test="customerId != null and customerId != ''">
                and customer_id = #{customerId}
            </if>
            <if test="cardStatus != null and cardStatus != ''">
                and card_status = #{cardStatus}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>