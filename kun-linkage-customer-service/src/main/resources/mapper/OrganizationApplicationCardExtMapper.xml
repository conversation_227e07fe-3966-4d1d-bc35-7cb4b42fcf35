<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.customer.ext.mapper.OrganizationApplicationCardExtMapper">

    <select id="listOrganizationApplicationCardByWhere" resultType="com.kun.linkage.customer.facade.vo.organization.OrganizationApplicationCardVO">
        select oac.*, obi.organization_name
        from kl_organization_application_card oac
        left join kl_organization_basic_info obi on oac.organization_no = obi.organization_no
        <where>
            <if test="organizationNo != null and organizationNo != ''">
                and oac.organization_no = #{organizationNo}
            </if>
            <if test="cardProductCode != null and cardProductCode != ''">
                and oac.card_product_code = #{cardProductCode}
            </if>
            <if test="status != null and status != ''">
                and oac.status = #{status}
            </if>
        </where>
        order by oac.last_modify_time desc
    </select>

    <select id="listOrganizationApplicationCardReviewRecordByWhere" resultType="com.kun.linkage.customer.facade.vo.organization.OrganizationApplicationCardReviewRecordVO">
        select oacrr.*, obi.organization_name
        from kl_organization_application_card_review_record oacrr
        left join kl_organization_basic_info obi on oacrr.organization_no = obi.organization_no
        <where>
            <if test="organizationNo != null and organizationNo != ''">
                and oacrr.organization_no = #{organizationNo}
            </if>
            <if test="cardProductCode != null and cardProductCode != ''">
                and oacrr.card_product_code = #{cardProductCode}
            </if>
            <if test="reviewStatus != null and reviewStatus != ''">
                and oacrr.review_status = #{reviewStatus}
            </if>
            <if test="operatorType != null and operatorType != ''">
                and oacrr.operator_type = #{operatorType}
            </if>
        </where>
        order by oacrr.submit_time desc
    </select>
</mapper>