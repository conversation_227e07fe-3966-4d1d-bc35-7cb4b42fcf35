<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.customer.ext.mapper.CardholderFeeExtMapper">

    <select id="listCardholderFeeByWhere" resultType="com.kun.linkage.customer.facade.vo.CardholderFeeVO">
        select kcf.*, kobi.organization_name
        from kl_cardholder_fee kcf
                 left join kl_organization_basic_info kobi on kcf.organization_no = kobi.organization_no
        <where>
            <if test="organizationNo != null and organizationNo != ''">
                and kcf.organization_no = #{organizationNo}
            </if>
            <if test="cardProductCode != null and cardProductCode != ''">
                and kcf.card_product_code = #{cardProductCode}
            </if>
            <if test="status != null and status != ''">
                and kcf.status = #{status}
            </if>
        </where>
        order by kcf.last_modify_time desc
    </select>

    <select id="listCardholderFeeReviewRecordByWhere" resultType="com.kun.linkage.customer.facade.vo.CardholderFeeReviewRecordVO">
        select kcfrr.*, kobi.organization_name
        from kl_cardholder_fee_review_record kcfrr
        left join kl_organization_basic_info kobi on kcfrr.organization_no = kobi.organization_no
        <where>
            <if test="organizationNo != null and organizationNo != ''">
                and kcfrr.organization_no = #{organizationNo}
            </if>
            <if test="cardProductCode != null and cardProductCode != ''">
                and kcfrr.card_product_code = #{cardProductCode}
            </if>
            <if test="status != null and status != ''">
                and kcfrr.status = #{status}
            </if>
            <if test="reviewId != null">
                and kcfrr.review_id = #{reviewId}
            </if>
            <if test="submitStartTime != null">
                and kcfrr.submit_time &gt;= #{submitStartTime}
            </if>
            <if test="submitEndTime != null">
                and kcfrr.submit_time &lt;= #{submitEndTime}
            </if>
            <if test="reviewStartTime != null">
                and kcfrr.review_time &gt;= #{reviewStartTime}
            </if>
            <if test="reviewEndTime != null">
                and kcfrr.review_time &lt;= #{reviewEndTime}
            </if>
            <if test="reviewStatus != null and reviewStatus != ''">
                and kcfrr.review_status = #{reviewStatus}
            </if>
            <if test="operatorType != null and operatorType != ''">
                and kcfrr.operator_type = #{operatorType}
            </if>
        </where>
        order by kcfrr.submit_time desc
    </select>
</mapper>