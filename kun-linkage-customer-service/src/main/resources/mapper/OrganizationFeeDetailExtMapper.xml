<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.customer.ext.mapper.OrganizationFeeDetailExtMapper">

    <select id="queryOrganizationFeeMonthReportData" resultType="com.kun.linkage.customer.facade.vo.report.OrganizationFeeMonthReportVO">
        SELECT kofd.organization_no                                                             AS organizationNo,
               kobi.organization_name                                                           AS organizationName,
               kofd.card_product_code                                                           AS cardProductCode,
               kofd.fee_type                                                                    AS feeType,
               kofd.fee_collection_method                                                       AS feeCollectionMethod,
               kofd.snapshot_min_amount                                                         AS snapshotMinAmount,
               kofd.snapshot_max_amount                                                         AS snapshotMaxAmount,
               kofd.transaction_currency_code                                                   AS transactionCurrencyCode,
               COUNT(*)                                                                         AS totalNum,
               SUM(kofd.transaction_amount)                                                     AS totalTransactionAmount,
               SUM(kofd.fee_amount)                                                             AS totalFeeAmount,
               -- KUN相关统计
               MAX(CASE WHEN kofd.deduct_processor = 'KUN' THEN kofd.deduct_currency_code END)  AS kunCurrencyCode,
               SUM(CASE
                       WHEN kofd.deduct_processor = 'KUN' AND kofd.fee_collection_status = 1 THEN kofd.deduct_fee_amount
                       ELSE 0 END)                                                              AS collectedKunAmount,
               SUM(CASE
                       WHEN kofd.deduct_processor = 'KUN' AND kofd.fee_collection_status = 0 THEN kofd.deduct_fee_amount
                       ELSE 0 END)                                                              AS notCollectedKunAmount,
               -- PayX相关统计
               MAX(CASE WHEN kofd.deduct_processor = 'PAYX' THEN kofd.deduct_currency_code END) AS payXCurrencyCode,
               SUM(CASE
                       WHEN kofd.deduct_processor = 'PAYX' AND kofd.fee_collection_status = 1
                           THEN kofd.deduct_fee_amount
                       ELSE 0 END)                                                              AS collectedPayXAmount,
               SUM(CASE
                       WHEN kofd.deduct_processor = 'PAYX' AND kofd.fee_collection_status = 0
                           THEN kofd.deduct_fee_amount
                       ELSE 0 END)                                                              AS notCollectedPayXAmount
        FROM kl_organization_fee_detail_${tableIndex} kofd
                 LEFT JOIN kl_organization_basic_info kobi ON kofd.organization_no = kobi.organization_no
        GROUP BY kofd.organization_no,
                 kofd.card_product_code,
                 kofd.fee_type,
                 kofd.fee_collection_method,
                 kofd.snapshot_min_amount,
                 kofd.snapshot_max_amount,
                 kofd.transaction_currency_code,
                 kofd.deduct_currency_code
        ORDER BY kofd.organization_no,
                 kofd.card_product_code,
                 kofd.fee_type,
                 kofd.fee_collection_method,
                 kofd.snapshot_min_amount,
                 kofd.snapshot_max_amount,
                 kofd.transaction_currency_code,
                 kofd.deduct_currency_code;

    </select>
</mapper>