<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.customer.ext.mapper.OrganizationBasicInfoExtMapper">

    <select id="listOrganizationBasicInfoByWhere"
            resultType="com.kun.linkage.customer.facade.vo.organization.OrganizationBasicInfoVO">
        select *
        from kl_organization_basic_info
        <where>
            <if test="organizationNo != null and organizationNo != ''">
                and organization_no = #{organizationNo}
            </if>
            <if test="organizationName != null and organizationName != ''">
                and organization_name like concat('%', #{organizationName}, '%')
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
        </where>
        order by last_modify_time desc
    </select>

    <select id="listOrganizationBasicInfoReviewRecordByWhere"
            resultType="com.kun.linkage.customer.facade.vo.organization.OrganizationBasicInfoReviewRecordVO">
        select *
        from kl_organization_basic_info_review_record
        <where>
            <if test="organizationNo != null and organizationNo != ''">
                and organization_no = #{organizationNo}
            </if>
            <if test="organizationName != null and organizationName != ''">
                and organization_name like concat('%', #{organizationName}, '%')
            </if>
            <if test="reviewStatus != null and reviewStatus != ''">
                and review_status = #{reviewStatus}
            </if>
            <if test="operatorType != null and operatorType != ''">
                and operator_type = #{operatorType}
            </if>
        </where>
        order by submit_time desc
    </select>

    <select id="getMaxOrganizationNoByBusinessType" resultType="string">
        select organization_no
        from kl_organization_basic_info
        where business_type = #{businessType}
        order by organization_no desc limit 1
    </select>
</mapper>