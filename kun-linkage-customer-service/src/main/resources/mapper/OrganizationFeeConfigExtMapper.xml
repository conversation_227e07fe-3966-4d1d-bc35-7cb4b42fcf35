<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.customer.ext.mapper.OrganizationFeeConfigExtMapper">

    <select id="listOrganizationFeeConfigByWhere" resultType="com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeConfigVO">
        select kofc.*, koft.template_name, kobi.organization_name
        from kl_organization_fee_config kofc
        left join kl_organization_fee_template koft on koft.template_no = kofc.template_no
        left join kl_organization_basic_info kobi on kobi.organization_no = kofc.organization_no
        <where>
            <if test="organizationNo != null and organizationNo != ''">
                and kofc.organization_no = #{organizationNo}
            </if>
            <if test="status != null and status != ''">
                and kofc.status = #{status}
            </if>
        </where>
        order by kofc.last_modify_time desc
    </select>

    <select id="getOrganizationFeeConfigById" resultType="com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeConfigAndDetailVO">
        select kofc.*, koft.template_name, kobi.organization_name
        from kl_organization_fee_config kofc
        left join kl_organization_fee_template koft on koft.template_no = kofc.template_no
        left join kl_organization_basic_info kobi on kobi.organization_no = kofc.organization_no
        where kofc.id = #{id}
    </select>

    <select id="listOrganizationFeeConfigReviewRecordByWhere" resultType="com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeConfigReviewRecordVO">
        select kofcrr.*, koft.template_name, kobi.organization_name
        from kl_organization_fee_config_review_record kofcrr
        left join kl_organization_fee_template koft on koft.template_no = kofcrr.template_no
        left join kl_organization_basic_info kobi on kobi.organization_no = kofcrr.organization_no
        <where>
            <if test="organizationNo != null and organizationNo != ''">
                and kofcrr.organization_no = #{organizationNo}
            </if>
            <if test="reviewId != null">
                and kofcrr.review_id = #{reviewId}
            </if>
            <if test="reviewStatus != null and reviewStatus != ''">
                and kofcrr.review_status = #{reviewStatus}
            </if>
            <if test="submitStartTime != null">
                and kofcrr.submit_time &gt;= #{submitStartTime}
            </if>
            <if test="submitEndTime != null">
                and kofcrr.submit_time &lt;= #{submitEndTime}
            </if>
            <if test="reviewStartTime != null">
                and kofcrr.review_time &gt;= #{reviewStartTime}
            </if>
            <if test="reviewEndTime != null">
                and kofcrr.review_time &lt;= #{reviewEndTime}
            </if>
            <if test="operatorType != null and operatorType != ''">
                and kofcrr.operator_type = #{operatorType}
            </if>
        </where>
        order by kofcrr.submit_time desc
    </select>

    <select id="getOrganizationFeeConfigReviewRecordByReviewId" resultType="com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeConfigAndDetailReviewRecordVO">
        select kofcrr.*, koft.template_name, kobi.organization_name
        from kl_organization_fee_config_review_record kofcrr
                 left join kl_organization_fee_template koft on koft.template_no = kofcrr.template_no
                 left join kl_organization_basic_info kobi on kobi.organization_no = kofcrr.organization_no
        where kofcrr.review_id = #{reviewId}
    </select>

    <select id="getOrganizationFeeConfigByWhere" resultType="OrganizationFeeConfig">
        select kofc.*
        from kl_organization_fee_config kofc,
        kl_organization_fee_template koft
        where kofc.template_no = koft.template_no
        and kofc.organization_no = #{organizationNo}
        <if test="cardProductCode != null and cardProductCode != ''">
            and kofc.card_product_code = #{cardProductCode}
        </if>
        and kofc.card_product_code = #{cardProductCode}
        and kofc.effective_start_time &lt;= #{currentDate}
        and kofc.effective_end_time &gt;= #{currentDate}
        and kofc.status = #{validStatus}
        and koft.status = #{validStatus}
        limit 1
    </select>
</mapper>