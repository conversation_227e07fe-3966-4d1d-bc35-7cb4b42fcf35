<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.customer.ext.mapper.WalletTransactionDetailExtMapper">

    <select id="listWalletRechargeDetailByWhere" resultType="com.kun.linkage.customer.facade.api.bean.res.PageQueryWalletRechargeDetailRes">
        select
        id as order_no,
        transaction_type,
        transaction_datetime as rechargeDatetime,
        transaction_datetime as acceptanceDatetime,
        digital_amount as rechargeAmount,
        digital_currency_code as rechargeCurrencyCode,
        digital_currency_precision as rechargeCurrencyPrecision,
        fiat_amount as acceptanceAmount,
        fiat_currency_code as acceptanceCurrencyCode,
        fiat_currency_precision as acceptanceCurrencyPrecision
        from kl_wallet_transaction_detail
        <where>
            organization_no = #{organizationNo}
            and customer_id = #{customerId}
            and transaction_datetime between #{startDatetime} and #{endDatetime}
            and transaction_type = 'RECHARGE'
        </where>
        order by transaction_datetime desc
    </select>
</mapper>