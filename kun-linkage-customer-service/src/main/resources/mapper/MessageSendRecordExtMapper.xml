<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.customer.ext.mapper.MessageSendRecordExtMapper">

    <select id="groupCountSMSRecordByWhere" resultType="com.kun.linkage.customer.facade.vo.MessageSendRecordGroupCountVO">
        select organization_no,
               count(1) as num
        from vcc_message_send_record
        where send_status = 'SUCCESS'
          and source_from in ('UP','KL')
          and send_time &gt;= #{startDatetime}
          and send_time &lt; #{endDatetime}
          and message_type = 'SMS'
          and organization_no is not null
          and organization_no != ''
        group by(organization_no)
    </select>
</mapper>