<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.customer.ext.mapper.OrganizationAccountInfoExtMapper">

    <select id="listOrganizationAccountInfoByWhere" resultType="com.kun.linkage.customer.facade.vo.organization.OrganizationAccountInfoVO">
        select oai.*, obi.organization_name
        from kl_organization_account_info oai
                 left join kl_organization_basic_info obi on oai.organization_no = obi.organization_no
        <where>
            <if test="accountNo != null and accountNo != ''">
                and oai.account_no = #{accountNo}
            </if>
            <if test="organizationNo != null and organizationNo != ''">
                and oai.organization_no = #{organizationNo}
            </if>
            <if test="organizationName != null and organizationName != ''">
                and obi.organization_name like concat('%', #{organizationName}, '%')
            </if>
            <if test="accountType != null and accountType != ''">
                and oai.account_type = #{accountType}
            </if>
            <if test="status != null and status != ''">
                and oai.status = #{status}
            </if>
        </where>
        order by oai.last_modify_time desc
    </select>

    <select id="getOrganizationAccountInfoById" resultType="com.kun.linkage.customer.facade.vo.organization.OrganizationAccountDetailVO">
        select oai.*, obi.organization_name
        from kl_organization_account_info oai
        left join kl_organization_basic_info obi on oai.organization_no = obi.organization_no
        where oai.id = #{id}
    </select>

    <select id="listOrganizationAccountInfoReviewRecordByWhere" resultType="com.kun.linkage.customer.facade.vo.organization.OrganizationAccountInfoReviewRecordVO">
        select oairr.*, obi.organization_name
        from kl_organization_account_info_review_record oairr
        left join kl_organization_basic_info obi on oairr.organization_no = obi.organization_no
        <where>
            <if test="organizationNo != null and organizationNo != ''">
                and oairr.organization_no = #{organizationNo}
            </if>
            <if test="organizationName != null and organizationName != ''">
                and obi.organization_name like concat('%', #{organizationName}, '%')
            </if>
            <if test="accountType != null and accountType != ''">
                and oairr.account_type = #{accountType}
            </if>
            <if test="reviewStatus != null and reviewStatus != ''">
                and oairr.review_status = #{reviewStatus}
            </if>
            <if test="operatorType != null and operatorType != ''">
                and oairr.operator_type = #{operatorType}
            </if>
        </where>
        order by oairr.submit_time desc
    </select>
</mapper>