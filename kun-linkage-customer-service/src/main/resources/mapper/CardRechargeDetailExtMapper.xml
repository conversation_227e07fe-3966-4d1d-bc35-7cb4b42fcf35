<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.kun.linkage.customer.ext.mapper.CardRechargeDetailExtMapper">

    <select id="listCardRechargeDetailByWhere" resultType="com.kun.linkage.customer.facade.api.bean.res.PageQueryCardRechargeDetailRes">
        select
        card_id as cardId,
        recharge_datetime as rechargeDatetime,
        recharge_amount as rechargeAmount,
        recharge_currency_code as rechargeCurrencyCode,
        recharge_currency_precision as rechargeCurrencyPrecision,
        deduct_total_amount as deductAmount,
        deduct_currency_code as deductCurrencyCode,
        deduct_currency_precision as deductCurrencyPrecision
        from kl_card_recharge_detail
        <where>
            organization_no = #{organizationNo}
            and customer_id = #{customerId}
            <if test="cardId != null and cardId != ''">
                and card_id = #{cardId}
            </if>
            and recharge_datetime between #{startDatetime} and #{endDatetime}
            and recharge_status = 'SUCCESS'
        </where>
        order by recharge_datetime desc
    </select>
</mapper>