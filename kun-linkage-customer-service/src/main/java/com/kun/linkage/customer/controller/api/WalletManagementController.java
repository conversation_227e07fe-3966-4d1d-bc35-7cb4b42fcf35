package com.kun.linkage.customer.controller.api;

import com.alibaba.fastjson.JSON;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import com.kun.linkage.customer.facade.api.bean.req.CreateWalletReq;
import com.kun.linkage.customer.facade.api.bean.req.PageQueryWalletRechargeDetailReq;
import com.kun.linkage.customer.facade.api.bean.res.CreateWalletRes;
import com.kun.linkage.customer.facade.api.bean.res.PageQueryWalletRechargeDetailRes;
import com.kun.linkage.customer.facade.constants.CustomerLockConstant;
import com.kun.linkage.customer.service.WalletManagementBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 钱包管理接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Tag(name = "钱包管理", description = "钱包管理")
@RestController
@RequestMapping("/api/walletManagement")
public class WalletManagementController {
    protected Logger log = LoggerFactory.getLogger(String.valueOf(this.getClass()));
    @Resource
    private RedissonLockUtil redissonLockUtil;
    @Resource
    private WalletManagementBizService walletManagementBizService;

    /**
     * 创建钱包
     *
     * @param createWalletReq
     * @return
     */
    @Operation(description = "创建钱包", summary = "创建钱包")
    @RequestMapping(value = "/createWallet", method = RequestMethod.POST)
    public Result<CreateWalletRes> createWallet(@RequestBody @Validated CreateWalletReq createWalletReq){
        log.info("[钱包管理-创建钱包]入参:{}", JSON.toJSONString(createWalletReq));
        RLock lock = null;
        try {
            String lockKey = CustomerLockConstant.CREATE_WALLET_LOCK_PREFIX + createWalletReq.getOrganizationNo()
                    + createWalletReq.getCustomerId() + createWalletReq.getWalletNetwork() + createWalletReq.getChainNetwork();
            lock = redissonLockUtil.getLock(lockKey);
            if (lock != null && lock.tryLock()) {
                return walletManagementBizService.createWallet(createWalletReq);
            } else {
                log.error("[卡片管理-开卡]获取锁失败,lockKey:{}", lockKey);
                return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
            }
        } catch (Exception e) {
            log.error("[卡片管理-开卡]处理异常,异常信息:", e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        } finally {
            redissonLockUtil.unlock(lock);
        }
    }

    /**
     * 分页查询钱包充值记录
     * @param pageQueryWalletRechargeDetailReq
     * @return
     */
    @Operation(description = "分页查询钱包充值记录", summary = "分页查询钱包充值记录")
    @RequestMapping(value = "/pageQueryWalletRechargeDetail", method = RequestMethod.POST)
    public Result<PageResult<PageQueryWalletRechargeDetailRes>> pageQueryWalletRechargeDetail(
            @RequestBody @Validated PageQueryWalletRechargeDetailReq pageQueryWalletRechargeDetailReq){
        log.info("[分页查询钱包充值记录]入参:{}", JSON.toJSONString(pageQueryWalletRechargeDetailReq));
        return Result.success(walletManagementBizService.pageQueryWalletRechargeDetail(pageQueryWalletRechargeDetailReq));
    }
}
