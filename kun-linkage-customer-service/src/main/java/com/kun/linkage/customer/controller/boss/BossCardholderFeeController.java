package com.kun.linkage.customer.controller.boss;

import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.dto.ReviewDTO;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.customer.facade.dto.CardholderFeeAddSubmitDTO;
import com.kun.linkage.customer.facade.dto.CardholderFeeModifySubmitDTO;
import com.kun.linkage.customer.facade.dto.CardholderFeePageQueryDTO;
import com.kun.linkage.customer.facade.dto.CardholderFeeReviewRecordPageQueryDTO;
import com.kun.linkage.customer.facade.vo.CardholderFeeAndDetailReviewRecordVO;
import com.kun.linkage.customer.facade.vo.CardholderFeeAndDetailVO;
import com.kun.linkage.customer.facade.vo.CardholderFeeReviewRecordVO;
import com.kun.linkage.customer.facade.vo.CardholderFeeVO;
import com.kun.linkage.customer.service.CardholderFeeBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 持卡人费率管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Tag(name = "持卡人费率管理", description = "持卡人费率管理")
@RestController
@RequestMapping("/boss/cardholderFee")
public class BossCardholderFeeController extends BaseVccBossController {
    @Resource
    private CardholderFeeBizService cardholderFeeBizService;

    /**
     * 分页查询持卡人费率信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询持卡人费率信息", summary = "分页查询持卡人费率信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-cardholderFee"})
    @GetMapping("/pageList")
    public Result<PageResult<CardholderFeeVO>> pageList(CardholderFeePageQueryDTO dto) {
        PageResult<CardholderFeeVO> list = cardholderFeeBizService.pageList(dto);
        return Result.success(list);
    }

    /**
     * 查询持卡人费率明细信息
     *
     * @param feeId
     * @return
     */
    @Operation(description = "查询持卡人费率明细信息", summary = "查询持卡人费率明细信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-cardholderFee"})
    @GetMapping("/detail")
    public Result<CardholderFeeAndDetailVO> detail(@RequestParam(name = "feeId") Long feeId) {
        return cardholderFeeBizService.detail(feeId);
    }


    /**
     * 提交新增持卡人费率信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "提交新增持卡人费率信息", summary = "提交新增持卡人费率信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-cardholderFee"})
    @PostMapping("/addSubmit")
    public Result<Void> addSubmit(@RequestBody @Validated CardholderFeeAddSubmitDTO dto) {
        return cardholderFeeBizService.addSubmit(dto, this.getCurrentBossUserVO());
    }

    /**
     * 提交修改持卡人费率信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "提交修改持卡人费率信息", summary = "提交修改持卡人费率信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-cardholderFee"})
    @PostMapping("/modifySubmit")
    public Result<Void> modifySubmit(@RequestBody @Validated CardholderFeeModifySubmitDTO dto) {
        return cardholderFeeBizService.modifySubmit(dto, this.getCurrentBossUserVO());
    }

    /**
     * 审核数据
     *
     * @param dto
     * @return
     */
    @Operation(description = "审核数据", summary = "审核数据")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-cardholderFee"})
    @PostMapping("/review")
    public Result<Void> review(@RequestBody @Validated ReviewDTO dto) {
        return cardholderFeeBizService.review(dto, this.getCurrentBossUserVO());
    }

    /**
     * 分页查询审核信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询审核信息", summary = "分页查询审核信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-cardholderFee"})
    @GetMapping("/review/pageList")
    public Result<PageResult<CardholderFeeReviewRecordVO>> reviewPageList(CardholderFeeReviewRecordPageQueryDTO dto) {
        PageResult<CardholderFeeReviewRecordVO> list = cardholderFeeBizService.reviewPageList(dto);
        return Result.success(list);
    }

    /**
     * 查询审核持卡人费率明细信息
     *
     * @param reviewId
     * @return
     */
    @Operation(description = "查询审核持卡人费率明细信息", summary = "查询审核持卡人费率明细信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-cardholderFee"})
    @GetMapping("/review/detail")
    public Result<CardholderFeeAndDetailReviewRecordVO> reviewDetail(@RequestParam(name = "reviewId") Long reviewId) {
        return cardholderFeeBizService.reviewDetail(reviewId);
    }
}
