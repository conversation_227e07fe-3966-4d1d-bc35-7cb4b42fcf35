package com.kun.linkage.customer.controller.api;

import com.alibaba.fastjson.JSON;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.external.facade.api.uplus.req.OtpNoticeBaseReq;
import com.kun.linkage.common.external.facade.api.uplus.res.OtpNoticeBaseRes;
import com.kun.linkage.customer.service.OtpNoticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "otp通知接口", description = "otp通知接口")
@RestController
@RequestMapping("/api/otp")
public class OtpNoticeController {

    protected Logger log = LoggerFactory.getLogger(String.valueOf(this.getClass()));

    @Resource
    private OtpNoticeService otpNoticeService;

    /**
     * opt 通知转发
     * @param otpNoticeBaseReq
     * @return
     */
    @Operation(description = "opt 通知转发", summary = "opt 通知")
    @PostMapping("/notice")
    public Result<OtpNoticeBaseRes> optNotice(@RequestBody OtpNoticeBaseReq otpNoticeBaseReq){
        log.info("otp通知入参:{}", JSON.toJSONString(otpNoticeBaseReq));
        return otpNoticeService.optNotice(otpNoticeBaseReq);
    }

}
