package com.kun.linkage.customer.controller.boss.organization;

import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.dto.ReviewDTO;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.customer.facade.dto.organization.basic.OrganizationBasicInfoModifySubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.basic.OrganizationBasicInfoPageQueryDTO;
import com.kun.linkage.customer.facade.dto.organization.basic.OrganizationBasicInfoReviewRecordPageQueryDTO;
import com.kun.linkage.customer.facade.vo.organization.OrganizationBasicInfoReviewRecordVO;
import com.kun.linkage.customer.facade.vo.organization.OrganizationBasicInfoVO;
import com.kun.linkage.customer.service.organization.OrganizationBasicInfoBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 机构基本信息管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Tag(name = "机构基本信息管理", description = "机构基本信息管理")
@RestController
@RequestMapping("/boss/organizationBasicInfo")
public class BossOrganizationBasicInfoController extends BaseVccBossController {
    @Resource
    private OrganizationBasicInfoBizService organizationBasicInfoBizService;

    /**
     * 分页查询机构基本信息
     * @param dto
     * @return
     */
    @Operation(description = "分页查询机构基本信息", summary = "分页查询机构基本信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-basic-info"})
    @GetMapping("/pageList")
    public Result<PageResult<OrganizationBasicInfoVO>> pageList(OrganizationBasicInfoPageQueryDTO dto) {
        PageResult<OrganizationBasicInfoVO> list = organizationBasicInfoBizService.pageList(dto);
        return Result.success(list);
    }

    /**
     * 查询所有机构信息
     * @return
     */
    @Operation(description = "查询所有机构信息", summary = "查询所有机构信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-basic-info"})
    @GetMapping("/allValidList")
    public Result<List<OrganizationBasicInfoVO>> allValidList() {
        List<OrganizationBasicInfoVO> list = organizationBasicInfoBizService.allValidList();
        return Result.success(list);
    }


    /**
     * 提交修改机构基本信息
     * @param dto
     * @return
     */
    @Operation(description = "提交修改机构基本信息", summary = "提交修改机构基本信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-basic-info"})
    @PostMapping("/modifySubmit")
    public Result<Void> modifySubmit(@RequestBody @Validated OrganizationBasicInfoModifySubmitDTO dto) {
        return organizationBasicInfoBizService.modifySubmit(dto, this.getCurrentBossUserVO());
    }

    /**
     * 审核数据
     * @param dto
     * @return
     */
    @Operation(description = "审核数据", summary = "审核数据")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-basic-info"})
    @PostMapping("/review")
    public Result<Void> review(@RequestBody @Validated ReviewDTO dto) {
        return organizationBasicInfoBizService.review(dto, this.getCurrentBossUserVO());
    }

    /**
     * 分页查询审核信息
     * @param dto
     * @return
     */
    @Operation(description = "分页查询审核信息", summary = "分页查询审核信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-basic-info"})
    @GetMapping("/review/pageList")
    public Result<PageResult<OrganizationBasicInfoReviewRecordVO>> reviewPageList(OrganizationBasicInfoReviewRecordPageQueryDTO dto) {
        PageResult<OrganizationBasicInfoReviewRecordVO> list = organizationBasicInfoBizService.reviewPageList(dto);
        return Result.success(list);
    }

}
