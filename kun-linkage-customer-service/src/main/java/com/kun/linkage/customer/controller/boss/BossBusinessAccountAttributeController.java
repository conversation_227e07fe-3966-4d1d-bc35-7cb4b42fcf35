package com.kun.linkage.customer.controller.boss;

import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.dto.ReviewDTO;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.customer.facade.dto.BusinessAccountAttributeModifySubmitDTO;
import com.kun.linkage.customer.facade.dto.BusinessAccountAttributePageQueryDTO;
import com.kun.linkage.customer.facade.dto.BusinessAccountAttributeReviewRecordPageQueryDTO;
import com.kun.linkage.customer.facade.vo.BusinessAccountAttributeReviewRecordVO;
import com.kun.linkage.customer.facade.vo.BusinessAccountAttributeVO;
import com.kun.linkage.customer.service.BusinessAccountAttributeBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 业务账户属性信息管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Tag(name = "业务账户属性信息管理", description = "业务账户属性信息管理")
@RestController
@RequestMapping("/boss/businessAccountAttribute")
public class BossBusinessAccountAttributeController extends BaseVccBossController {
    @Resource
    private BusinessAccountAttributeBizService businessAccountAttributeBizService;

    /**
     * 分页查询业务账户属性信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询业务账户属性信息", summary = "分页查询业务账户属性信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-businessAccountAttribute"})
    @GetMapping("/pageList")
    public Result<PageResult<BusinessAccountAttributeVO>> pageList(BusinessAccountAttributePageQueryDTO dto) {
        PageResult<BusinessAccountAttributeVO> list = businessAccountAttributeBizService.pageList(dto);
        return Result.success(list);
    }

    /**
     * 提交修改业务账户属性信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "提交修改业务账户属性信息", summary = "提交修改业务账户属性信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-businessAccountAttribute"})
    @PostMapping("/modifySubmit")
    public Result<Void> modifySubmit(@RequestBody @Validated BusinessAccountAttributeModifySubmitDTO dto) {
        return businessAccountAttributeBizService.modifySubmit(dto, this.getCurrentBossUserVO());
    }

    /**
     * 审核数据
     *
     * @param dto
     * @return
     */
    @Operation(description = "审核数据", summary = "审核数据")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-businessAccountAttribute"})
    @PostMapping("/review")
    public Result<Void> review(@RequestBody @Validated ReviewDTO dto) {
        return businessAccountAttributeBizService.review(dto, this.getCurrentBossUserVO());
    }

    /**
     * 分页查询审核信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询审核信息", summary = "分页查询审核信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-businessAccountAttribute"})
    @GetMapping("/review/pageList")
    public Result<PageResult<BusinessAccountAttributeReviewRecordVO>> reviewPageList(BusinessAccountAttributeReviewRecordPageQueryDTO dto) {
        PageResult<BusinessAccountAttributeReviewRecordVO> list = businessAccountAttributeBizService.reviewPageList(dto);
        return Result.success(list);
    }

}
