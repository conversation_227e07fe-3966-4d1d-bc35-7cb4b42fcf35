package com.kun.linkage.customer.controller.boss;

import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.customer.facade.dto.OrganizationCustomerCardListQueryDTO;
import com.kun.linkage.customer.facade.vo.OrganizationCustomerCardListQueryVO;
import com.kun.linkage.customer.service.OrganizationCustomerCardInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 卡管理接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Tag(name = "卡管理", description = "卡管理")
@RestController
@RequestMapping("/boss/cardManagement")
public class BossCardManagementController {
    @Resource
    private OrganizationCustomerCardInfoService organizationCustomerCardInfoService;

    /**
     * 分页查询机构客户账户信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询机构客户卡片信息", summary = "分页查询机构客户卡片信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-customer-card"})
    @GetMapping("/pageList")
    public Result<PageResult<OrganizationCustomerCardListQueryVO>> pageList(OrganizationCustomerCardListQueryDTO dto) {
        PageResult<OrganizationCustomerCardListQueryVO> organizationCustomerCardList = organizationCustomerCardInfoService.getOrganizationCustomerCardList(dto);
        return Result.success(organizationCustomerCardList);
    }

}
