package com.kun.linkage.customer.controller.api;

import com.alibaba.fastjson.JSON;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import com.kun.linkage.customer.facade.api.bean.req.*;
import com.kun.linkage.customer.facade.api.bean.res.*;
import com.kun.linkage.customer.facade.constants.CustomerLockConstant;
import com.kun.linkage.customer.service.CardManagementBizService;
import com.kun.linkage.customer.service.CardRechargeBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 卡管理接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Tag(name = "卡管理", description = "卡管理")
@RestController
@RequestMapping("/api/cardManagement")
public class CardManagementController {
    protected Logger log = LoggerFactory.getLogger(String.valueOf(this.getClass()));
    @Resource
    private RedissonLockUtil redissonLockUtil;
    @Resource
    private CardManagementBizService cardManagementBizService;
    @Resource
    private CardRechargeBizService cardRechargeBizService;

    /**
     * 开卡
     *
     * @param openCardReq
     * @return
     */
    @Operation(description = "开卡", summary = "开卡")
    @RequestMapping(value = "/openCard", method = RequestMethod.POST)
    public Result<OpenCardRes> openCard(@RequestBody @Validated OpenCardReq openCardReq){
        log.info("[卡片管理-开卡]入参:{}", JSON.toJSONString(openCardReq));
        if (StringUtils.isNotBlank(openCardReq.getRequestNo())){
            openCardReq.setRequestNo(openCardReq.getRequestNo().length() > 30 ? openCardReq.getRequestNo().substring(0, 30) : openCardReq.getRequestNo());
        }
        RLock lock = null;
        try {
            String lockKey = CustomerLockConstant.OPEN_CARD_LOCK_PREFIX
                    + openCardReq.getOrganizationNo() + openCardReq.getCustomerId() + openCardReq.getCardProductCode();
            lock = redissonLockUtil.getLock(lockKey);
            if (lock != null && lock.tryLock()) {
                return cardManagementBizService.openCard(openCardReq);
            } else {
                log.error("[卡片管理-开卡]获取锁失败,lockKey:{}", lockKey);
                return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
            }
        } catch (Exception e) {
            log.error("[卡片管理-开卡]处理异常,异常信息:", e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        } finally {
            redissonLockUtil.unlock(lock);
        }
    }

    /**
     * 开卡状态查询
     *
     * @param openCardStatusQueryReq
     * @return
     */
    @Operation(description = "开卡状态查询", summary = "开卡状态查询")
    @RequestMapping(value = "/openCardStatusQuery", method = RequestMethod.POST)
    public Result<OpenCardStatusQueryRes> openCardStatusQuery(@RequestBody @Validated OpenCardStatusQueryReq openCardStatusQueryReq){
        log.info("[卡片管理-开卡状态查询]入参:{}", JSON.toJSONString(openCardStatusQueryReq));
        try {
            return cardManagementBizService.openCardStatusQuery(openCardStatusQueryReq);
        } catch (Exception e) {
            log.error("[卡片管理-开卡状态查询]处理异常,异常信息:", e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        }
    }

    /**
     * 卡冻结/解冻
     *
     * @param cardFreezeAndUnfreezeReq
     * @return
     */
    @Operation(description = "卡冻结/解冻", summary = "卡冻结/解冻")
    @RequestMapping(value = "/cardFreezeAndUnFreezes", method = RequestMethod.POST)
    public Result<Void> cardFreezeAndUnFreeze(@RequestBody @Validated CardFreezeAndUnfreezeReq cardFreezeAndUnfreezeReq){
        log.info("[卡片管理-冻结/解冻]入参:{}", JSON.toJSONString(cardFreezeAndUnfreezeReq));
        if (StringUtils.isNotBlank(cardFreezeAndUnfreezeReq.getRequestNo())){
            cardFreezeAndUnfreezeReq.setRequestNo(cardFreezeAndUnfreezeReq.getRequestNo().length() > 30 ? cardFreezeAndUnfreezeReq.getRequestNo().substring(0, 30) : cardFreezeAndUnfreezeReq.getRequestNo());
        }
        RLock lock = null;
        try {
            String lockKey = CustomerLockConstant.CARD_FREEZE_UNFREEZE_LOCK_PREFIX + cardFreezeAndUnfreezeReq.getOrganizationNo() + cardFreezeAndUnfreezeReq.getCardId();
            lock = redissonLockUtil.getLock(lockKey);
            if (lock != null && lock.tryLock()) {
                return cardManagementBizService.cardFreezeAndUnfreeze(cardFreezeAndUnfreezeReq);
            } else {
                log.error("[卡片管理-冻结/解冻]获取锁失败,lockKey:{}", lockKey);
                return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
            }
        } catch (Exception e) {
            log.error("[卡片管理-冻结/解冻]处理异常,异常信息:", e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        } finally {
            redissonLockUtil.unlock(lock);
        }
    }

    /**
     * 销卡
     *
     * @param cancelCardReq
     * @return
     */
    @Operation(description = "销卡", summary = "销卡")
    @RequestMapping(value = "/cancelCard", method = RequestMethod.POST)
    public Result<Void> cancelCard(@RequestBody @Validated CancelCardReq cancelCardReq){
        log.info("[卡片管理-销卡]入参:{}", JSON.toJSONString(cancelCardReq));
        if (StringUtils.isNotBlank(cancelCardReq.getRequestNo())){
            cancelCardReq.setRequestNo(cancelCardReq.getRequestNo().length() > 30 ? cancelCardReq.getRequestNo().substring(0, 30) : cancelCardReq.getRequestNo());
        }
        RLock lock = null;
        try {
            String lockKey = CustomerLockConstant.CANCEL_CARD_LOCK_PREFIX + cancelCardReq.getOrganizationNo() + cancelCardReq.getCardId();
            lock = redissonLockUtil.getLock(lockKey);
            if (lock != null && lock.tryLock()) {
                return cardManagementBizService.cancelCard(cancelCardReq);
            } else {
                log.error("[卡片管理-销卡]获取锁失败,lockKey:{}", lockKey);
                return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
            }
        } catch (Exception e) {
            log.error("[卡片管理-销卡]处理异常,异常信息:", e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        } finally {
            redissonLockUtil.unlock(lock);
        }
    }

    /**
     * 卡激活
     *
     * @param cardActivationReq
     * @return
     */
    @Operation(description = "卡激活", summary = "卡激活")
    @RequestMapping(value = "/cardActivation", method = RequestMethod.POST)
    public Result<Void> cardActivation(@RequestBody @Validated CardActivationReq cardActivationReq){
        log.info("[卡片管理-卡激活]入参:{}", JSON.toJSONString(cardActivationReq));
        if (StringUtils.isNotBlank(cardActivationReq.getRequestNo())){
            cardActivationReq.setRequestNo(cardActivationReq.getRequestNo().length() > 30 ? cardActivationReq.getRequestNo().substring(0, 30) : cardActivationReq.getRequestNo());
        }
        RLock lock = null;
        try {
            String lockKey = CustomerLockConstant.CARD_ACTIVATION_LOCK_PREFIX + cardActivationReq.getOrganizationNo() + cardActivationReq.getCardId();
            lock = redissonLockUtil.getLock(lockKey);
            if (lock != null && lock.tryLock()) {
                return cardManagementBizService.cardActivation(cardActivationReq);
            } else {
                log.error("[卡片管理-卡激活]获取锁失败,lockKey:{}", lockKey);
                return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
            }
        } catch (Exception e) {
            log.error("[卡片管理-卡激活]处理异常,异常信息:", e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        } finally {
            redissonLockUtil.unlock(lock);
        }
    }

    /**
     * 卡信息查询接口
     *
     * @param cancelCardReq
     * @return
     */
    @Operation(description = "卡信息查询接口", summary = "卡信息查询接口")
    @RequestMapping(value = "/cardInfoQuery", method = RequestMethod.POST)
    public Result<CardInfoQueryRes> cardInfoQuery(@RequestBody @Validated CardInfoQueryReq cancelCardReq){
        log.info("[卡片管理-卡信息查询]入参:{}", JSON.toJSONString(cancelCardReq));
        try {
            return cardManagementBizService.cardInfoQuery(cancelCardReq);
        } catch (Exception e) {
            log.error("[卡片管理-卡信息查询]处理异常,异常信息:", e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        }
    }

    /**
     * 卡充值
     *
     * @param cardRechargeReq
     * @return
     */
    @Operation(description = "卡充值", summary = "卡充值")
    @RequestMapping(value = "/cardRecharge", method = RequestMethod.POST)
    public Result<CardRechargeRes> cardRecharge(@RequestBody @Validated CardRechargeReq cardRechargeReq){
        log.info("[卡片管理-卡充值]入参:{}", JSON.toJSONString(cardRechargeReq));
        RLock lock = null;
        try {
            String lockKey = CustomerLockConstant.CARD_RECHARGE_LOCK_PREFIX
                    + cardRechargeReq.getOrganizationNo() + cardRechargeReq.getCustomerId() + cardRechargeReq.getCardId();
            lock = redissonLockUtil.getLock(lockKey);
            if (lock != null && lock.tryLock()) {
                return cardRechargeBizService.cardRecharge(cardRechargeReq);
            } else {
                log.error("[卡片管理-卡充值]获取锁失败,lockKey:{}", lockKey);
                return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
            }
        } catch (Exception e) {
            log.error("[卡片管理-卡充值]处理异常,异常信息:", e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        } finally {
            redissonLockUtil.unlock(lock);
        }
    }

    /**
     * 卡充值状态查询
     *
     * @param cardRechargeStatusQueryReq
     * @return
     */
    @Operation(description = "卡充值状态查询", summary = "卡充值状态查询")
    @RequestMapping(value = "/cardRechargeStatusQuery", method = RequestMethod.POST)
    public Result<CardRechargeStatusQueryRes> cardRechargeStatusQuery(@RequestBody @Validated CardRechargeStatusQueryReq cardRechargeStatusQueryReq){
        log.info("[卡片管理-卡充值状态查询]入参:{}", JSON.toJSONString(cardRechargeStatusQueryReq));
        try {
            return cardRechargeBizService.cardRechargeStatusQuery(cardRechargeStatusQueryReq);
        } catch (Exception e) {
            log.error("[卡片管理-卡充值状态查询]处理异常,异常信息:", e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        }
    }

    /**
     * 分页查询卡充值记录
     * @param pageQueryCardRechargeDetailReq
     * @return
     */
    @Operation(description = "分页查询卡充值记录", summary = "分页查询卡充值记录")
    @RequestMapping(value = "/pageQueryCardRechargeDetail", method = RequestMethod.POST)
    public Result<PageResult<PageQueryCardRechargeDetailRes>> pageQueryCardRechargeDetail(
            @RequestBody @Validated PageQueryCardRechargeDetailReq pageQueryCardRechargeDetailReq){
        log.info("[分页查询卡充值记录]入参:{}", JSON.toJSONString(pageQueryCardRechargeDetailReq));
        return Result.success(cardRechargeBizService.pageQueryCardRechargeDetail(pageQueryCardRechargeDetailReq));
    }
}
