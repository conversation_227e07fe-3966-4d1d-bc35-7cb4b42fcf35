package com.kun.linkage.customer.controller.boss;

import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.CustomerKycAuditRecord;
import com.kun.linkage.customer.facade.dto.CustomerKycAuditDTO;
import com.kun.linkage.customer.facade.dto.CustomerKycAuditRecordPageDTO;
import com.kun.linkage.customer.facade.vo.CustomerKycAuditVO;
import com.kun.linkage.customer.service.BossCustomerKycService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Tag(name = "客户KYC信息审核", description = "客户KYC信息审核")
@RestController
@RequestMapping("/boss/customer/kyc")
public class BossCustomerKycController extends BaseVccBossController {

    @Resource
    private BossCustomerKycService bossCustomerKycService;

    /**
     * 分页查询kyc审核信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询kyc审核信息", summary = "分页查询kyc审核信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-customer-kyc"})
    @GetMapping("/pageList")
    public Result<PageResult<CustomerKycAuditRecord>> pageList(@Validated CustomerKycAuditRecordPageDTO dto) {
        PageResult<CustomerKycAuditRecord> list = bossCustomerKycService.pageList(dto);
        return Result.success(list);
    }

    /**
     * 根据案件号查询kyc审核详情
     *
     * @return
     */
    @Operation(description = "查询kyc审核详情", summary = "查询kyc审核详情")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-customer-kyc"})
    @GetMapping("/detail")
    public Result<CustomerKycAuditVO> detail(@RequestParam(name = "caseNo") String caseNo) {
        return bossCustomerKycService.detail(caseNo);
    }

    @Operation(description = "kyc信息审核", summary = "kyc信息审核")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-customer-kyc"})
    @PostMapping("/audit")
    public Result<Boolean> audit(@RequestBody @Validated CustomerKycAuditDTO  customerKycAuditDTO) {
        return bossCustomerKycService.audit(customerKycAuditDTO, this.getCurrentBossUserVO());
    }



}
