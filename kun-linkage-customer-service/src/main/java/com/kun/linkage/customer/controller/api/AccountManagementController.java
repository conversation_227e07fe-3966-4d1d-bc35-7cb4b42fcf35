package com.kun.linkage.customer.controller.api;

import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.customer.facade.api.bean.req.QueryAccountBalanceReq;
import com.kun.linkage.customer.facade.api.bean.req.QueryBalanceByCardIdReq;
import com.kun.linkage.customer.facade.api.bean.res.QueryAccountBalanceRes;
import com.kun.linkage.customer.facade.api.bean.res.QueryBalanceByCardIdRsp;
import com.kun.linkage.customer.service.OrganizationCustomerAccountInfoBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 账户管理接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Tag(name = "账户管理接口", description = "账户管理接口")
@RestController
@RequestMapping("/api/accountManagement")
public class AccountManagementController extends BaseVccBossController {
    @Resource
    private OrganizationCustomerAccountInfoBizService organizationCustomerAccountInfoBizService;


    /**
     * 根据组织机构号、客户号、币种查询账户余额
     *
     * @param queryAccountBalanceReq
     * @return
     */
    @Operation(description = "根据组织机构号、客户号、币种查询账户余额", summary = "根据组织机构号、客户号、币种查询账户余额")
    @RequestMapping(value = "/queryAccountBalance", method = RequestMethod.POST)
    public Result<List<QueryAccountBalanceRes>> queryAccountBalance(@RequestBody @Validated QueryAccountBalanceReq queryAccountBalanceReq) {
        return organizationCustomerAccountInfoBizService.queryAccountBalance(queryAccountBalanceReq);
    }

    @Operation(description = "根据cardId查询账户余额", summary = "根据cardId查询账户余额")
    @RequestMapping(value = "/queryBalanceByCardId", method = RequestMethod.POST)
    public Result<QueryBalanceByCardIdRsp> queryBalanceByCardId(@RequestBody @Validated QueryBalanceByCardIdReq queryBalanceByCardIdReq) {
        return organizationCustomerAccountInfoBizService.queryBalanceByCardId(queryBalanceByCardIdReq);
    }

}
