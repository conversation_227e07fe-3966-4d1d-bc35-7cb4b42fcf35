package com.kun.linkage.customer.controller.boss.organization;

import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.dto.ReviewDTO;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.customer.facade.dto.organization.account.OrganizationAccountInfoAddSubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.account.OrganizationAccountInfoModifySubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.account.OrganizationAccountInfoPageQueryDTO;
import com.kun.linkage.customer.facade.dto.organization.account.OrganizationAccountInfoReviewRecordPageQueryDTO;
import com.kun.linkage.customer.facade.vo.organization.OrganizationAccountDetailVO;
import com.kun.linkage.customer.facade.vo.organization.OrganizationAccountInfoReviewRecordVO;
import com.kun.linkage.customer.facade.vo.organization.OrganizationAccountInfoVO;
import com.kun.linkage.customer.service.organization.OrganizationAccountInfoBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 机构账户信息管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Tag(name = "机构账户信息管理", description = "机构账户信息管理")
@RestController
@RequestMapping("/boss/organizationAccountInfo")
public class BossOrganizationAccountInfoController extends BaseVccBossController {
    @Resource
    private OrganizationAccountInfoBizService organizationAccountInfoBizService;

    /**
     * 分页查询机构账户信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询机构账户信息", summary = "分页查询机构账户信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-account"})
    @GetMapping("/pageList")
    public Result<PageResult<OrganizationAccountInfoVO>> pageList(OrganizationAccountInfoPageQueryDTO dto) {
        PageResult<OrganizationAccountInfoVO> list = organizationAccountInfoBizService.pageList(dto);
        return Result.success(list);
    }

    /**
     * 查询机构账户明细信息
     *
     * @param organizationAccountInfoId
     * @return
     */
    @Operation(description = "查询机构账户明细信息", summary = "查询机构账户明细信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-account"})
    @GetMapping("/detail")
    public Result<OrganizationAccountDetailVO> detail(@RequestParam(name = "organizationAccountInfoId") Long organizationAccountInfoId) {
        return organizationAccountInfoBizService.detail(organizationAccountInfoId);
    }


    /**
     * 提交新增机构账户信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "提交新增机构账户信息", summary = "提交新增机构账户信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-account"})
    @PostMapping("/addSubmit")
    public Result<Void> addSubmit(@RequestBody @Validated OrganizationAccountInfoAddSubmitDTO dto) {
        return organizationAccountInfoBizService.addSubmit(dto, this.getCurrentBossUserVO());
    }

    /**
     * 提交修改机构账户信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "提交修改机构账户信息", summary = "提交修改机构账户信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-account"})
    @PostMapping("/modifySubmit")
    public Result<Void> modifySubmit(@RequestBody @Validated OrganizationAccountInfoModifySubmitDTO dto) {
        return organizationAccountInfoBizService.modifySubmit(dto, this.getCurrentBossUserVO());
    }

    /**
     * 审核数据
     *
     * @param dto
     * @return
     */
    @Operation(description = "审核数据", summary = "审核数据")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-account"})
    @PostMapping("/review")
    public Result<Void> review(@RequestBody @Validated ReviewDTO dto) {
        return organizationAccountInfoBizService.review(dto, this.getCurrentBossUserVO());
    }

    /**
     * 分页查询审核信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询审核信息", summary = "分页查询审核信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-account"})
    @GetMapping("/review/pageList")
    public Result<PageResult<OrganizationAccountInfoReviewRecordVO>> reviewPageList(OrganizationAccountInfoReviewRecordPageQueryDTO dto) {
        PageResult<OrganizationAccountInfoReviewRecordVO> list = organizationAccountInfoBizService.reviewPageList(dto);
        return Result.success(list);
    }

}
