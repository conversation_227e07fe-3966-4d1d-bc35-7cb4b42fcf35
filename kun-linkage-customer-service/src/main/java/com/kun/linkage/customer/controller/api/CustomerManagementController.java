package com.kun.linkage.customer.controller.api;

import com.alibaba.fastjson.JSON;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import com.kun.linkage.customer.facade.api.bean.req.ModifyCustometLevelReq;
import com.kun.linkage.customer.facade.constants.CustomerLockConstant;
import com.kun.linkage.customer.service.OrganizationCustomerBasicInfoBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 客户管理接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Tag(name = "客户管理接口", description = "客户管理接口")
@RestController
@RequestMapping("/api/customerManagement")
public class CustomerManagementController {
    protected Logger log = LoggerFactory.getLogger(String.valueOf(this.getClass()));
    @Resource
    private RedissonLockUtil redissonLockUtil;
    @Resource
    private OrganizationCustomerBasicInfoBizService organizationCustomerBasicInfoBizService;

    /**
     * 创建钱包
     *
     * @param modifyCustometLevelReq
     * @return
     */
    @Operation(description = "修改客户等级", summary = "修改客户等级")
    @RequestMapping(value = "/modifyCustomerLevel", method = RequestMethod.POST)
    public Result<Void> modifyCustomerLevel(@RequestBody @Validated ModifyCustometLevelReq modifyCustometLevelReq){
        log.info("[客户管理-修改客户等级]入参:{}", JSON.toJSONString(modifyCustometLevelReq));
        RLock lock = null;
        try {
            String lockKey = CustomerLockConstant.MODIFY_CUSTOMER_INFO_LOCK_PREFIX
                    + modifyCustometLevelReq.getOrganizationNo() + modifyCustometLevelReq.getCustomerId();
            lock = redissonLockUtil.getLock(lockKey);
            if (lock != null && lock.tryLock()) {
                return organizationCustomerBasicInfoBizService.modifyCustomerLevel(modifyCustometLevelReq);
            } else {
                log.error("[客户管理-修改客户等级]获取锁失败,lockKey:{}", lockKey);
                return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
            }
        } catch (Exception e) {
            log.error("[客户管理-修改客户等级]处理异常,异常信息:", e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        } finally {
            redissonLockUtil.unlock(lock);
        }
    }
}
