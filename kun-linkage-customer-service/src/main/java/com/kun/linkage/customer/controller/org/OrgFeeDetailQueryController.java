package com.kun.linkage.customer.controller.org;

import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.customer.facade.api.bean.req.OrganizationFeeDetailPageQueryReq;
import com.kun.linkage.customer.facade.api.bean.res.OrganizationFeeDetailRes;
import com.kun.linkage.customer.service.organization.OrganizationFeeDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 手续费明细查询接口
 * </p>
 */
@Tag(name = "手续费明细查询", description = "商户端手续费明细查询")
@RestController
@RequestMapping("/org/feeDetail")
public class OrgFeeDetailQueryController {

    @Resource
    private OrganizationFeeDetailService organizationFeeDetailService;

    /**
     * 分页查询机构手续费明细信息
     *
     * @param req 查询条件
     * @return 分页结果
     */
    @Operation(description = "分页查询机构手续费明细信息", summary = "分页查询机构手续费明细信息")
    @PostMapping("/pageList")
    public Result<PageResult<OrganizationFeeDetailRes>> pageList(@RequestBody @Validated OrganizationFeeDetailPageQueryReq req) {
        PageResult<OrganizationFeeDetailRes> list = organizationFeeDetailService.pageList(req);
        return Result.success(list);
    }
}
