package com.kun.linkage.customer.controller.api;

import com.alibaba.fastjson.JSON;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.customer.facade.api.bean.req.kyc.CustomerKycSubmissionReq;
import com.kun.linkage.customer.facade.api.bean.res.kyc.CustomerKycSubmissionRes;
import com.kun.linkage.customer.facade.api.bean.res.kyc.KycNotifyRes;
import com.kun.linkage.customer.service.kyc.CustomerKycSubmissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@Tag(name = "客户KYC信息报送", description = "客户KYC信息报送")
@RestController
@RequestMapping("/api/customer/kyc")
public class CustomerKycSubmissionController {

    @Resource
    private CustomerKycSubmissionService customerKycSubmissionService;

    /**
     * 客户kyc信息报送
     * @return
     */
    @Operation(summary = "客户kyc信息报送",description = "客户kyc信息报送")
    @PostMapping(value = "/submission")
    public Result<CustomerKycSubmissionRes> kycSubmission(@RequestBody @Validated CustomerKycSubmissionReq customerKycSubmissionReq) {
        log.info("[客户kyc信息报送]入参:{}", JSON.toJSONString(customerKycSubmissionReq));

        return customerKycSubmissionService.kycSubmission(customerKycSubmissionReq);
    }

    /**
     * 客户kyc信息申报查询
     * @return
     */
    @Operation(summary = "客户kyc信息申报查询",description = "客户kyc信息申报查询")
    @GetMapping(value = "/selectCaseResult")
    public Result<KycNotifyRes> selectCaseResult(@RequestParam(name = "caseNo") String caseNo,@RequestParam(name = "customerId") String customerId,
                                                 @RequestParam(name = "organizationNo") String organizationNo) {
        log.info("[客户kyc信息申报查询]入参:caseNo{},customerId{},organizationNo{}", caseNo, customerId, organizationNo );

        return customerKycSubmissionService.selectCaseResult(caseNo,customerId,organizationNo);
    }

}
