package com.kun.linkage.customer.controller.boss.organization;

import com.kun.linkage.common.redis.utils.RedissonCacheUtil;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import org.redisson.api.RLock;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/redisson/test")
public class RedissonTestController {
    @Resource
    private RedissonCacheUtil redissonCacheUtil;
    @Resource
    private RedissonLockUtil redissonLockUtil;

    // 测试缓存操作
    @GetMapping("/cache")
    public String testCache() {
        // 测试 RBucket
        redissonCacheUtil.set("testBucketKey", "testBucketValue", 600, TimeUnit.SECONDS);
        String bucketValue = redissonCacheUtil.get("testBucketKey");

        // 测试 RMap
        redissonCacheUtil.putToMap("testMapKey", "mapKey1", "mapValue1");
        String mapValue = redissonCacheUtil.getFromMap("testMapKey", "mapKey1");
        redissonCacheUtil.expireMap("testMapKey", 60, TimeUnit.SECONDS);

        // 测试 RList
        redissonCacheUtil.addToList("testListKey", "listElement1");
        redissonCacheUtil.addAllToList("testListKey", Arrays.asList("listElement2", "listElement3"));
        String listElement = redissonCacheUtil.getFromList("testListKey", 0);
        redissonCacheUtil.expireList("testListKey", 60, TimeUnit.SECONDS);

        // 测试 RSet
        redissonCacheUtil.addToSet("testSetKey", "setElement1");
        redissonCacheUtil.addAllToSet("testSetKey", Arrays.asList("setElement2", "setElement3"));
        boolean setContains = redissonCacheUtil.containsInSet("testSetKey", "setElement1");
        redissonCacheUtil.expireSet("testSetKey", 60, TimeUnit.SECONDS);

        return "Bucket Value: " + bucketValue +
                ", Map Value: " + mapValue +
                ", List Element: " + listElement +
                ", Set Contains: " + setContains;
    }

    // 测试缓存操作
    @GetMapping("/getCache")
    public String getCache(@RequestParam String key) {
        return "value:" + redissonCacheUtil.get(key);
    }

    // 测试非公平锁操作
    @GetMapping("/nonFairLock")
    public String testNonFairLock() {
        String lockName = "nonFairTestLock";
        RLock nonFairLock = redissonLockUtil.getLock(lockName);
        try {
            if (nonFairLock.tryLock(10, 20, TimeUnit.SECONDS)) {
                System.out.println("获取到非公平锁，执行临界区代码");
                // 模拟业务操作
                Thread.sleep(5000);
                return "成功获取并释放非公平锁";
            } else {
                return "未能获取非公平锁";
            }
        } catch (InterruptedException e) {
            return "获取非公平锁时发生异常";
        } finally {
            redissonLockUtil.unlock(nonFairLock);
        }
    }

    // 测试公平锁操作
    @GetMapping("/fairLock")
    public String testFairLock() {
        String lockName = "fairTestLock";
        RLock fairLock = redissonLockUtil.getFairLock(lockName);
        try {
            if (fairLock.tryLock(10, 20, TimeUnit.SECONDS)) {
                System.out.println("获取到公平锁，执行临界区代码");
                // 模拟业务操作
                Thread.sleep(5000);
                return "成功获取并释放公平锁";
            } else {
                return "未能获取公平锁";
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
            return "获取公平锁时发生异常";
        } finally {
            redissonLockUtil.unlock(fairLock);
        }
    }
}
