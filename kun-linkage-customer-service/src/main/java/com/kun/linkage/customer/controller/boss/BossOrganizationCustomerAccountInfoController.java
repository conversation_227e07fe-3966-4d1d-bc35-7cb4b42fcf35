package com.kun.linkage.customer.controller.boss;

import com.alibaba.fastjson.JSON;
import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import com.kun.linkage.customer.facade.constants.CustomerLockConstant;
import com.kun.linkage.customer.facade.dto.OrgCustomerAccountProcessBuckChargeDTO;
import com.kun.linkage.customer.facade.dto.OrganizationCustomerAccountInfoPageQueryDTO;
import com.kun.linkage.customer.facade.vo.OrganizationCustomerAccountDetailVO;
import com.kun.linkage.customer.facade.vo.OrganizationCustomerAccountInfoVO;
import com.kun.linkage.customer.service.OrganizationCustomerAccountInfoBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 机构客户账户信息管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Slf4j
@Tag(name = "机构客户账户信息管理", description = "机构客户账户信息管理")
@RestController
@RequestMapping("/boss/organizationCustomerAccountInfo")
public class BossOrganizationCustomerAccountInfoController extends BaseVccBossController {
    @Resource
    private OrganizationCustomerAccountInfoBizService organizationCustomerAccountInfoBizService;
    @Resource
    private RedissonLockUtil redissonLockUtil;

    /**
     * 分页查询机构客户账户信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询机构客户账户信息", summary = "分页查询机构客户账户信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-customer-account"})
    @PostMapping("/pageList")
    public Result<PageResult<OrganizationCustomerAccountInfoVO>> pageList(@Validated @RequestBody OrganizationCustomerAccountInfoPageQueryDTO dto) {
        PageResult<OrganizationCustomerAccountInfoVO> list = organizationCustomerAccountInfoBizService.pageList(dto);
        return Result.success(list);
    }

    /**
     * 查询机构客户账户明细信息
     *
     * @param organizationNo
     * @param organizationCustomerAccountInfoId
     * @return
     */
    @Operation(description = "查询机构客户账户明细信息", summary = "查询机构客户账户明细信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-customer-account"})
    @GetMapping("/detail")
    public Result<OrganizationCustomerAccountDetailVO> detail(@RequestParam(name = "organizationNo") String organizationNo,
                                                              @RequestParam(name = "organizationCustomerAccountInfoId") String organizationCustomerAccountInfoId) {
        return organizationCustomerAccountInfoBizService.detail(organizationNo, organizationCustomerAccountInfoId);
    }

    @Operation(description = "补扣账",summary = "补扣账")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-customer-account"})
    @PostMapping("processBackCharge")
    public Result<Boolean> processBackCharge(@Validated @RequestBody OrgCustomerAccountProcessBuckChargeDTO orgCustomerAccountProcessBuckChargeDTO){
        log.info("[机构客户账户-补扣账]入参:{}", JSON.toJSONString(orgCustomerAccountProcessBuckChargeDTO));
        RLock lock = null;
        try {
            String lockKey = CustomerLockConstant.ORG_CUSTOMER_ACCOUNT_PROCESS_BUCK_CHARGE_LOCK_PREFIX
                    + orgCustomerAccountProcessBuckChargeDTO.getOrganizationNo() + orgCustomerAccountProcessBuckChargeDTO.getOrganizationCustomerAccountInfoId();
            lock = redissonLockUtil.getLock(lockKey);
            if(lock != null && lock.tryLock()){
                return organizationCustomerAccountInfoBizService.processBackCharge(orgCustomerAccountProcessBuckChargeDTO);
            }else {
                log.error("[机构客户账户-补扣账]获取锁失败,lockKey:{}", lockKey);
                return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
            }
        }catch (Exception e) {
            log.error("[机构客户账户-补扣账]处理异常,异常信息:", e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        }finally {
            redissonLockUtil.unlock(lock);
        }

    }

}
