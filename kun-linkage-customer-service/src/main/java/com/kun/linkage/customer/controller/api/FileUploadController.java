package com.kun.linkage.customer.controller.api;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.customer.facade.api.bean.req.kyc.UploadBase64FileReq;
import com.kun.linkage.customer.service.FileUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@Slf4j
@Tag(name = "文件上传", description = "文件上传接口")
@RestController
@RequestMapping("/api/fileUpload")
public class FileUploadController {

    @Resource
    private FileUploadService fileUploadService;


    @Operation(summary = "上传文件",description = "上传文件并指定操作类型")
    @PostMapping(value = "/upload",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<String> uploadFile(@Parameter(description = "上传的文件",content = @Content(mediaType = MediaType.APPLICATION_OCTET_STREAM_VALUE))
                             @RequestPart("file") MultipartFile file,
                             @Parameter(description = "客户id") @RequestParam("customerId") String customerId,
                             @Parameter(description = "机构号") @RequestParam("organizationNo") String organizationNo) {
        return fileUploadService.uploadFile(file,customerId,organizationNo);
    }

    @Operation(summary = "上传文件;base64格式",description = "上传文件;base64格式")
    @PostMapping(value = "/uploadBase64")
    public Result<String> uploadBase64(@RequestBody @Validated UploadBase64FileReq uploadBase64FileReq) {
        return fileUploadService.uploadBase64(uploadBase64FileReq);
    }

}
