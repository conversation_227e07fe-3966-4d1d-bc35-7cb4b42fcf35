package com.kun.linkage.customer.controller.boss.organization;

import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.dto.ReviewDTO;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.customer.facade.dto.organization.applicationcard.OrganizationApplicationCardAddSubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.applicationcard.OrganizationApplicationCardModifySubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.applicationcard.OrganizationApplicationCardPageQueryDTO;
import com.kun.linkage.customer.facade.dto.organization.applicationcard.OrganizationApplicationCardReviewRecordPageQueryDTO;
import com.kun.linkage.customer.facade.vo.organization.OrganizationApplicationCardReviewRecordVO;
import com.kun.linkage.customer.facade.vo.organization.OrganizationApplicationCardVO;
import com.kun.linkage.customer.service.organization.OrganizationApplicationCardBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 机构卡产品信息管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
@Tag(name = "机构卡产品信息管理", description = "机构卡产品信息管理")
@RestController
@RequestMapping("/boss/organizationApplicationCard")
public class BossOrganizationApplicationCardController extends BaseVccBossController {
    @Resource
    private OrganizationApplicationCardBizService organizationApplicationCardBizService;

    /**
     * 分页查询机构卡产品信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询机构卡产品信息", summary = "分页查询机构卡产品信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-applicationCard"})
    @GetMapping("/pageList")
    public Result<PageResult<OrganizationApplicationCardVO>> pageList(OrganizationApplicationCardPageQueryDTO dto) {
        PageResult<OrganizationApplicationCardVO> list = organizationApplicationCardBizService.pageList(dto);
        return Result.success(list);
    }

    /**
     * 根据机构号获取有效的卡产品信息
     *
     * @param organizationNo
     * @return
     */
    @Operation(description = "根据机构号获取有效的卡产品信息", summary = "根据机构号获取有效的卡产品信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-applicationCard"})
    @GetMapping("/getValidListByOrganizationNo")
    public Result<List<OrganizationApplicationCardVO>> getValidListByOrganizationNo(@RequestParam(name = "organizationNo") String organizationNo) {
        List<OrganizationApplicationCardVO> list = organizationApplicationCardBizService.getValidListByOrganizationNo(organizationNo);
        return Result.success(list);
    }


    /**
     * 提交新增机构卡产品信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "提交新增机构卡产品信息", summary = "提交新增机构卡产品信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-applicationCard"})
    @PostMapping("/addSubmit")
    public Result<Void> addSubmit(@RequestBody @Validated OrganizationApplicationCardAddSubmitDTO dto) {
        return organizationApplicationCardBizService.addSubmit(dto, this.getCurrentBossUserVO());
    }

    /**
     * 提交修改机构卡产品信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "提交修改机构卡产品信息", summary = "提交修改机构卡产品信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-applicationCard"})
    @PostMapping("/modifySubmit")
    public Result<Void> modifySubmit(@RequestBody @Validated OrganizationApplicationCardModifySubmitDTO dto) {
        return organizationApplicationCardBizService.modifySubmit(dto, this.getCurrentBossUserVO());
    }

    /**
     * 审核数据
     *
     * @param dto
     * @return
     */
    @Operation(description = "审核数据", summary = "审核数据")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-applicationCard"})
    @PostMapping("/review")
    public Result<Void> review(@RequestBody @Validated ReviewDTO dto) {
        return organizationApplicationCardBizService.review(dto, this.getCurrentBossUserVO());
    }

    /**
     * 分页查询审核信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询审核信息", summary = "分页查询审核信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organization-applicationCard"})
    @GetMapping("/review/pageList")
    public Result<PageResult<OrganizationApplicationCardReviewRecordVO>> reviewPageList(OrganizationApplicationCardReviewRecordPageQueryDTO dto) {
        PageResult<OrganizationApplicationCardReviewRecordVO> list = organizationApplicationCardBizService.reviewPageList(dto);
        return Result.success(list);
    }

}
