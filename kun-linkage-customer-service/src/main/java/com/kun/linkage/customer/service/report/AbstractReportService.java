package com.kun.linkage.customer.service.report;

import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.aws.AwsS3Util;
import com.kun.common.util.aws.AwzS3Properties;
import com.kun.common.util.lark.LarkAlarmUtil;
import com.kun.linkage.base.facade.enums.ReportTypeEnum;
import com.kun.linkage.common.base.enums.OperationStatusEnum;
import com.kun.linkage.common.db.entity.ReportRecord;
import com.kun.linkage.common.db.mapper.ReportRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
public abstract class AbstractReportService {
    private static final Logger log = LoggerFactory.getLogger(AbstractReportService.class);
    @Resource
    private ReportRecordMapper reportRecordMapper;
    @Resource
    private AwsS3Util awsS3Util;
    @Resource
    private AwzS3Properties awzS3Properties;
    @Resource
    private LarkAlarmUtil larkAlarmUtil;
    @Value("${kun.aws.s3.fileFolder:}")
    private String rootPath;
    /**
     * 报表文件根目录
     */
    private static final String reportRootPath = "report";

    /**
     * 生成报表
     *
     * @param reportTypeEnum
     * @param reportDataDate
     * @return
     */
    public boolean generateReport(ReportTypeEnum reportTypeEnum, String reportDataDate) {
        log.info("开始生成报表数据文件,报表类型:{},数据日期:{}", reportTypeEnum.getValue(), reportDataDate);
        boolean result = true;
        // 初始化报表生成记录
        ReportRecord record = this.initReportRecord(reportTypeEnum, reportDataDate);
        if (record == null) {
            return result;
        }
        File reportTempFile = null;
        try {
            // 生成报表数据文件
            reportTempFile = this.generateReportFile(reportDataDate);
            log.info("报表数据文件临时生成完成,开始进行上传到S3文件服务器中,报表类型:{},数据日期:{}", reportTypeEnum.getValue(), reportDataDate);
            // 上传文件到S3服务器中
            String url = awsS3Util.uploadChunkedFile(record.getReportFileName(), reportTempFile, awzS3Properties.getBucket(),
                    this.getReportFilePath(reportTypeEnum), CannedAccessControlList.PublicReadWrite);
            if (StringUtils.isBlank(url)) {
                log.error("报表数据文件上传失败,报表类型:{},数据日期:{}", reportTypeEnum.getValue(), reportDataDate);
                record.setReportGenerateStatus(OperationStatusEnum.FAIL.getStatus());
            } else {
                log.info("报表数据文件上传成功,报表类型:{},数据日期:{}", reportTypeEnum.getValue(), reportDataDate);
                record.setReportFileFullPath(url);
                record.setReportGenerateStatus(OperationStatusEnum.SUCCESS.getStatus());
            }
        } catch (Exception e) {
            log.error("报表数据文件生成异常,报表类型:{},数据日期:{}", reportTypeEnum.getValue(), reportDataDate, e);
            record.setReportGenerateStatus(OperationStatusEnum.FAIL.getStatus());
        } finally {
            log.info("开始进行删除临时文件,报表类型:{},数据日期:{}", reportTypeEnum.getValue(), reportDataDate);
            // 删除临时文件
            if (reportTempFile != null && reportTempFile.exists()) {
                reportTempFile.delete();
            }
            // 更新结果
            record.setLastModifyTime(LocalDateTime.now());
            reportRecordMapper.updateById(record);
            // 如果生成失败就发送lark告警
            if (StringUtils.equals(record.getReportGenerateStatus(), OperationStatusEnum.FAIL.getStatus())) {
                this.sendLarkAlarm(reportTypeEnum, reportDataDate);
                result = false;
            }
        }
        return result;
    }

    /**
     * 生成报表文件
     *
     * @param reportDataDate
     */
    protected abstract File generateReportFile(String reportDataDate);

    /**
     * 初始化报表记录
     *
     * @param reportTypeEnum
     * @param reportDataDate
     * @return (返回null为已存在生成成功的数据, 否则会返回初始化成功的记录)
     */
    private ReportRecord initReportRecord(ReportTypeEnum reportTypeEnum, String reportDataDate) {
        ReportRecord record = reportRecordMapper.selectOne(Wrappers.<ReportRecord>lambdaQuery()
                .eq(ReportRecord::getReportType, reportTypeEnum.getValue())
                .eq(ReportRecord::getReportDataDate, reportDataDate));
        if (record != null) {
            if (StringUtils.equals(record.getReportGenerateStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                log.warn("已存在生成成功的记录数据,请勿重复生成,数据日期:{}", reportDataDate);
                return null;
            } else {
                record.setReportGenerateStatus(OperationStatusEnum.PENDING.getStatus());
                record.setReportFileFullPath(null);
                LocalDateTime now = LocalDateTime.now();
                record.setReportGenerateDate(now.format(DateTimeFormatter.BASIC_ISO_DATE));
                record.setCreateTime(now);
                record.setLastModifyTime(now);
                reportRecordMapper.updateById(record);
            }
        } else {
            record = new ReportRecord();
            record.setReportType(reportTypeEnum.getValue());
            record.setReportFileName(this.getReportFileName(reportTypeEnum, reportDataDate));
            record.setReportDataDate(reportDataDate);
            record.setReportGenerateStatus(OperationStatusEnum.PENDING.getStatus());
            LocalDateTime now = LocalDateTime.now();
            record.setReportGenerateDate(now.format(DateTimeFormatter.BASIC_ISO_DATE));
            record.setCreateTime(now);
            record.setLastModifyTime(now);
            reportRecordMapper.insert(record);
        }
        return record;
    }

    /**
     * 获取文件名称
     *
     * @param reportDataDate
     * @return
     */
    protected String getReportFileName(ReportTypeEnum reportTypeEnum, String reportDataDate) {
        return String.format(reportTypeEnum.getFileNameFormat(), reportDataDate);
    }

    /**
     * 获取文件目录
     *
     * @param reportTypeEnum
     * @return
     */
    protected String getReportFilePath(ReportTypeEnum reportTypeEnum) {
        return rootPath + "/" + reportRootPath + "/" + reportTypeEnum.getFileDir();
    }

    /**
     * 发送LARK告警
     * @param reportTypeEnum
     * @param reportDataDate
     */
    private void sendLarkAlarm(ReportTypeEnum reportTypeEnum, String reportDataDate) {
        log.warn("报表生成失败开始发送lark告警,报表类型:{},数据日期:{}", reportTypeEnum.getValue(), reportDataDate);
        String msg = String.format("[报表生成失败]报表类型:%s, 报表数据日期:%s, 报表生成日期:%s",
                reportTypeEnum.getValue(), reportDataDate, LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE));
        larkAlarmUtil.sendTextAlarm(msg);
    }
}
