package com.kun.linkage.customer.controller.webhook;

import com.kun.linkage.common.base.Result;
import com.kun.linkage.customer.facade.dto.TechnicalParamsCreateDTO;
import com.kun.linkage.customer.facade.dto.TechnicalParamsUpdateDTO;
import com.kun.linkage.customer.facade.vo.TechnicalParamsVO;
import com.kun.linkage.customer.service.TechnicalParamsBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 技术参数配置API
 *
 * @since 2025-07-28
 */
@Slf4j
@Tag(name = "技术参数配置API", description = "技术参数配置API")
@RestController
@RequestMapping("/api/technicalParams")
public class TechnicalParamsController {

    @Resource
    private TechnicalParamsBizService technicalParamsBizService;

    /**
     * 根据机构号查询技术参数配置
     *
     * @param organizationNo 机构号
     * @return 技术参数配置
     */
    @Operation(description = "根据机构号查询技术参数配置", summary = "根据机构号查询技术参数配置")
    @GetMapping("/getByOrganizationNo")
    public Result<TechnicalParamsVO> getByOrganizationNo(@RequestParam("organizationNo") String organizationNo) {
        log.info("根据机构号查询技术参数配置，机构号：{}", organizationNo);
        try {
            TechnicalParamsVO result = technicalParamsBizService.getByOrganizationNo(organizationNo);
            if (result == null) {
                return Result.fail("技术参数配置不存在");
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据机构号查询技术参数配置失败，机构号：{}", organizationNo, e);
            return Result.fail("查询失败");
        }
    }

    /**
     * 新增技术参数配置
     *
     * @param dto 创建数据
     * @return 创建结果
     */
    @Operation(description = "新增技术参数配置", summary = "新增技术参数配置")
    @PostMapping("/create")
    public Result<TechnicalParamsVO> create(@RequestBody @Validated TechnicalParamsCreateDTO dto) {
        log.info("新增技术参数配置，参数：{}", dto);
        try {
            return technicalParamsBizService.createTechnicalParams(dto);
        } catch (Exception e) {
            log.error("新增技术参数配置失败", e);
            return Result.fail("新增失败");
        }
    }

    /**
     * 更新技术参数配置
     *
     * @param dto 更新数据
     * @return 更新结果
     */
    @Operation(description = "更新技术参数配置", summary = "更新技术参数配置")
    @PostMapping("/update")
    public Result<Void> update(@RequestBody @Validated TechnicalParamsUpdateDTO dto) {
        log.info("更新技术参数配置，参数：{}", dto);
        try {
            return technicalParamsBizService.updateTechnicalParams(dto);
        } catch (Exception e) {
            log.error("更新技术参数配置失败", e);
            return Result.fail("更新失败");
        }
    }
}
