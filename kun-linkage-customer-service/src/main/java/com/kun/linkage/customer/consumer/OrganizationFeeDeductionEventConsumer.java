package com.kun.linkage.customer.consumer;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.lark.LarkAlarmUtil;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.MqConsumerGroupConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.base.enums.OperationStatusEnum;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.db.entity.OrganizationFeeDetail;
import com.kun.linkage.common.db.mapper.OrganizationFeeDetailMapper;
import com.kun.linkage.common.external.facade.api.kcard.KCardKunAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.KCardPayXAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXDirectionEnum;
import com.kun.linkage.common.external.facade.api.kcard.req.KunDebitSubReq;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXDebitSubReq;
import com.kun.linkage.common.external.facade.api.kcard.res.KunDebitSubRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXDebitSubRsp;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import com.kun.linkage.customer.facade.constants.CustomerLockConstant;
import com.kun.linkage.customer.facade.constants.CustomerTipConstant;
import com.kun.linkage.customer.facade.enums.DeductProcessorEnum;
import com.kun.linkage.customer.facade.enums.OrganizationFeeCollectionMethodEnum;
import com.kun.linkage.customer.facade.enums.OrganizationFeeCollectionStatusEnum;
import com.kun.linkage.customer.facade.vo.mq.OrganizationFeeDeductionEventVO;
import com.kun.linkage.customer.service.CardManagementBizService;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 机构费用扣款事件消费
 */
@Component
@RocketMQMessageListener(consumerGroup = MqConsumerGroupConstant.KL_ORGANIZATION_FEE_DEDUCTION_GROUP,
        topic = MqTopicConstant.ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC, messageModel = MessageModel.CLUSTERING)
public class OrganizationFeeDeductionEventConsumer implements RocketMQListener<OrganizationFeeDeductionEventVO> {
    private static final Logger log = LoggerFactory.getLogger(OrganizationFeeDeductionEventConsumer.class);
    @Resource
    private OrganizationFeeDetailMapper organizationFeeDetailMapper;
    @Resource
    private CardManagementBizService cardManagementBizService;
    @Resource
    private KCardKunAccountFacade kCardKunAccountFacade;
    @Resource
    private KCardPayXAccountFacade kCardPayXAccountFacade;
    @Resource
    private RedissonLockUtil redissonLockUtil;
    @Resource
    private LarkAlarmUtil larkAlarmUtil;

    @Override
    @NewSpan
    public void onMessage(OrganizationFeeDeductionEventVO eventVO) {
        MDC.put("traceId", eventVO.getLogContext().getTraceId());
        log.info("[机构费用扣除事件]接收到事件请求:{}", JSON.toJSONString(eventVO));
        RLock lock = null;
        boolean retryFlag = false;
        OrganizationFeeDetail organizationFeeDetail = null;
        try {
            if (StringUtils.isBlank(eventVO.getFeeDetailId()) || StringUtils.isBlank(eventVO.getMpcGroupCode())
                    || eventVO.getTransactionDatetime() == null || StringUtils.isBlank(eventVO.getMpcToken())) {
                log.error("[机构费用扣除事件]请求参数异常,请检查,请求参数:{}", JSON.toJSONString(eventVO));
                return;
            }
            // 使用费用明细id作为key
            String lockKey = CustomerLockConstant.ORGANIZATION_FEE_DEDUCTION_LOCK_PREFIX + eventVO.getFeeDetailId();
            lock = redissonLockUtil.getLock(lockKey);
            if (lock == null || !lock.tryLock()) {
                log.warn("[机构费用扣除事件]获取锁失败,lockKey:{}", lockKey);
                retryFlag = true;
            } else {
                organizationFeeDetail = organizationFeeDetailMapper.selectOne(Wrappers.<OrganizationFeeDetail>lambdaQuery()
                        .eq(OrganizationFeeDetail::getId, eventVO.getFeeDetailId())
                        .eq(OrganizationFeeDetail::getTransactionDatetime, eventVO.getTransactionDatetime())
                        .eq(OrganizationFeeDetail::getFeeCollectionMethod, OrganizationFeeCollectionMethodEnum.REAL_TIME.getValue())
                        .eq(OrganizationFeeDetail::getFeeCollectionStatus, OrganizationFeeCollectionStatusEnum.NOT_COLLECTED.getValue()));
                if (organizationFeeDetail == null) {
                    // 不存在实时收取手续费且状态为未收的费用数据
                    log.error("[机构费用扣除事件]未找到状态为未收的费用数据,请检查,费用明细id:{},交易日期时间:{}",
                            eventVO.getFeeDetailId(), eventVO.getTransactionDatetime());
                    return;
                }
                // 如果为空就代表第一次扣或者是前面扣款状态未知,直接使用原来的流水号进行重试,如果明确失败该字段会被设置为空
                if (StringUtils.isBlank(organizationFeeDetail.getDeductRequestNo())) {
                    organizationFeeDetail.setDeductRequestNo(String.valueOf(IdWorker.getId()));
                }
                if (StringUtils.equals(organizationFeeDetail.getDeductProcessor(), DeductProcessorEnum.KUN.getValue())) {
                    // 调用kun动账
                    KunDebitSubReq kunDebitSubReq = new KunDebitSubReq();
                    kunDebitSubReq.setToken(eventVO.getMpcToken());
                    kunDebitSubReq.setGroupProductCode(eventVO.getMpcGroupCode());
                    kunDebitSubReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
                    kunDebitSubReq.setAccountNo(organizationFeeDetail.getOrganizationNo());
                    if (organizationFeeDetail.getDeductFeeAmount().compareTo(BigDecimal.ZERO) >= 0) {
                        kunDebitSubReq.setDirection(KunAndPayXDirectionEnum.TO_GROUP.getDirection());
                    } else {
                        kunDebitSubReq.setDirection(KunAndPayXDirectionEnum.TO_USER.getDirection());
                    }
                    kunDebitSubReq.setRequestNo(organizationFeeDetail.getDeductRequestNo());
                    kunDebitSubReq.setCurrency(organizationFeeDetail.getDeductCurrencyCode());
                    kunDebitSubReq.setAmount(organizationFeeDetail.getDeductFeeAmount());
                    kunDebitSubReq.setRemark(organizationFeeDetail.getRemark());
                    log.info("[机构费用扣除事件]调用KUN账户扣除本金开始,请求参数:{}", JSON.toJSONString(kunDebitSubReq));
                    Result<KunDebitSubRsp> result = kCardKunAccountFacade.kunDebitSub(kunDebitSubReq);
                    log.info("[机构费用扣除事件]调用KUN账户扣除本金结束,响应参数:{}", JSON.toJSONString(result));
                    // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
                    if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                        if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                            // 明确成功
                            log.info("[机构费用扣除事件]调用KUN账户扣除本金成功");
                            organizationFeeDetail.setFeeCollectionStatus(OrganizationFeeCollectionStatusEnum.COLLECTED.getValue());
                        } else if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.FAIL.getStatus())) {
                            // 明确失败
                            log.error("[机构费用扣除事件]调用KUN账户扣除本金明确失败");
                            // 明确失败需要将request清空,后面生成新的进行调用
                            organizationFeeDetail.setDeductRequestNo(null);
                            throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                        } else {
                            log.error("[机构费用扣除事件]调用KUN账户扣除本金状态未知");
                            throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                        }
                    } else {
                        log.error("[机构费用扣除事件]调用KUN账户扣除本金状态未知");
                        throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                    }
                } else {
                    // 调用payx动账
                    PayXDebitSubReq payXDebitSubReq = new PayXDebitSubReq();
                    payXDebitSubReq.setToken(eventVO.getMpcToken());
                    payXDebitSubReq.setGroupProductCode(eventVO.getMpcGroupCode());
                    payXDebitSubReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
                    payXDebitSubReq.setAccountNo(organizationFeeDetail.getOrganizationNo());
                    if (organizationFeeDetail.getDeductFeeAmount().compareTo(BigDecimal.ZERO) >= 0) {
                        payXDebitSubReq.setDirection(KunAndPayXDirectionEnum.TO_GROUP.getDirection());
                    } else {
                        payXDebitSubReq.setDirection(KunAndPayXDirectionEnum.TO_USER.getDirection());
                    }
                    payXDebitSubReq.setRequestNo(organizationFeeDetail.getDeductRequestNo());
                    payXDebitSubReq.setCurrency(organizationFeeDetail.getDeductCurrencyCode());
                    payXDebitSubReq.setAmount(organizationFeeDetail.getDeductFeeAmount());
                    payXDebitSubReq.setRemark(organizationFeeDetail.getRemark());
                    log.info("[机构费用扣除事件]调用PayX账户动账开始,请求参数:{}", JSON.toJSONString(payXDebitSubReq));
                    Result<PayXDebitSubRsp> result = kCardPayXAccountFacade.payXDebitSub(payXDebitSubReq);
                    log.info("[机构费用扣除事件]调用PayX账户动账结束,响应参数:{}", JSON.toJSONString(result));
                    // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
                    if (result != null && StringUtils.equals(result.getCode(), String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
                        if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                            // 明确成功
                            log.info("[机构费用扣除事件]调用PayX账户动账成功");
                            organizationFeeDetail.setFeeCollectionStatus(OrganizationFeeCollectionStatusEnum.COLLECTED.getValue());
                        } else if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.FAIL.getStatus())) {
                            // 明确失败
                            log.error("[机构费用扣除事件]调用PayX账户动账明确失败");
                            // 明确失败需要将request清空,后面生成新的进行调用
                            organizationFeeDetail.setDeductRequestNo(null);
                            throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                        } else {
                            log.error("[机构费用扣除事件]调用PayX账户扣除本金状态未知");
                            throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                        }
                    } else {
                        log.error("[机构费用扣除事件]调用PayX账户扣除本金状态未知");
                        throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
                    }
                }
            }
        } catch (Exception e) {
            log.error("[机构费用扣除事件]处理失败,异常信息:", e);
            retryFlag = true;
        } finally {
            if (organizationFeeDetail != null) {
                LocalDateTime now = LocalDateTime.now();
                organizationFeeDetail.setCallCount(organizationFeeDetail.getCallCount() + 1);
                organizationFeeDetail.setLastModifyTime(now);
                int row = organizationFeeDetailMapper.update(organizationFeeDetail,
                        Wrappers.<OrganizationFeeDetail>lambdaQuery()
                                .eq(OrganizationFeeDetail::getId, organizationFeeDetail.getId())
                                .eq(OrganizationFeeDetail::getTransactionDatetime, organizationFeeDetail.getTransactionDatetime()));
                log.info("update {} row organizationFeeDetail", row);
            }
            redissonLockUtil.unlock(lock);
            if (retryFlag && (organizationFeeDetail != null && organizationFeeDetail.getCallCount() < 5)) {
                log.warn("[机构费用扣除事件]事件处理失败,10秒后进行重试");
                cardManagementBizService.sendOrganizationFeeDeductionToMq(organizationFeeDetail, eventVO.getMpcToken(), eventVO.getMpcGroupCode());
            }
            // 发送告警
            if (retryFlag && (organizationFeeDetail != null && organizationFeeDetail.getCallCount() >= 5)) {
                log.warn("[机构费用扣除事件]重试后依旧失败,发送LARK告警");
                this.sendLarkAlarm(organizationFeeDetail);
            }
        }
    }

    /**
     * 发送LARK告警
     *
     * @param organizationFeeDetail
     */
    private void sendLarkAlarm(OrganizationFeeDetail organizationFeeDetail) {
        String msg = String.format("[机构费用扣除失败]机构号:%s, 费用类型:%s, 扣除金额:%s, 扣除币种:%s, 费用明细id:%s",
                organizationFeeDetail.getOrganizationNo(), organizationFeeDetail.getFeeType(),
                organizationFeeDetail.getDeductFeeAmount(), organizationFeeDetail.getDeductCurrencyCode(), organizationFeeDetail.getId());
        larkAlarmUtil.sendTextAlarm(msg);
    }
}
