package com.kun.linkage.customer.controller.boss.organization;

import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.dto.ReviewDTO;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeConfigAddSubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeConfigModifySubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeConfigPageQueryDTO;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeConfigReviewRecordPageQueryDTO;
import com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeConfigAndDetailReviewRecordVO;
import com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeConfigAndDetailVO;
import com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeConfigReviewRecordVO;
import com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeConfigVO;
import com.kun.linkage.customer.service.organization.OrganizationFeeConfigBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 机构费率配置管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Tag(name = "机构费率配置管理", description = "机构费率配置管理")
@RestController
@RequestMapping("/boss/organizationFeeConfig")
public class BossOrganizationFeeConfigController extends BaseVccBossController {
    @Resource
    private OrganizationFeeConfigBizService organizationFeeConfigBizService;

    /**
     * 分页查询机构费率配置
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询机构费率配置信息", summary = "分页查询机构费率配置信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFee"})
    @GetMapping("/pageList")
    public Result<PageResult<OrganizationFeeConfigVO>> pageList(OrganizationFeeConfigPageQueryDTO dto) {
        PageResult<OrganizationFeeConfigVO> list = organizationFeeConfigBizService.pageList(dto);
        return Result.success(list);
    }

    /**
     * 查询机构费率配置明细信息
     *
     * @param id
     * @return
     */
    @Operation(description = "查询机构费率配置明细信息", summary = "查询机构费率配置明细信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFee"})
    @GetMapping("/detail")
    public Result<OrganizationFeeConfigAndDetailVO> detail(@RequestParam(name = "id") Long id) {
        return organizationFeeConfigBizService.detail(id);
    }


    /**
     * 提交新增机构费率配置信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "提交新增机构费率配置信息", summary = "提交新增机构费率配置信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFee"})
    @PostMapping("/addSubmit")
    public Result<Void> addSubmit(@RequestBody @Validated OrganizationFeeConfigAddSubmitDTO dto) {
        return organizationFeeConfigBizService.addSubmit(dto, this.getCurrentBossUserVO());
    }

    /**
     * 提交修改机构费率配置信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "提交修改机构费率配置信息", summary = "提交修改机构费率配置信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFee"})
    @PostMapping("/modifySubmit")
    public Result<Void> modifySubmit(@RequestBody @Validated OrganizationFeeConfigModifySubmitDTO dto) {
        return organizationFeeConfigBizService.modifySubmit(dto, this.getCurrentBossUserVO());
    }

    /**
     * 审核数据
     *
     * @param dto
     * @return
     */
    @Operation(description = "审核数据", summary = "审核数据")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFee"})
    @PostMapping("/review")
    public Result<Void> review(@RequestBody @Validated ReviewDTO dto) {
        return organizationFeeConfigBizService.review(dto, this.getCurrentBossUserVO());
    }

    /**
     * 分页查询审核信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询审核信息", summary = "分页查询审核信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFee"})
    @GetMapping("/review/pageList")
    public Result<PageResult<OrganizationFeeConfigReviewRecordVO>> reviewPageList(OrganizationFeeConfigReviewRecordPageQueryDTO dto) {
        PageResult<OrganizationFeeConfigReviewRecordVO> list = organizationFeeConfigBizService.reviewPageList(dto);
        return Result.success(list);
    }

    /**
     * 查询审核机构费率配置明细信息
     *
     * @param reviewId
     * @return
     */
    @Operation(description = "查询审核机构费率配置明细信息", summary = "查询审核机构费率配置明细信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFee"})
    @GetMapping("/review/detail")
    public Result<OrganizationFeeConfigAndDetailReviewRecordVO> reviewDetail(@RequestParam(name = "reviewId") String reviewId) {
        return organizationFeeConfigBizService.reviewDetail(reviewId);
    }
}
