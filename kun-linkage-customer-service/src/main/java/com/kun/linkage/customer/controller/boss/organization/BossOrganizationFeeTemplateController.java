package com.kun.linkage.customer.controller.boss.organization;

import com.kun.linkage.boss.support.annotation.VerifyVccBossPermission;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.dto.ReviewDTO;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeTemplateAddSubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeTemplateModifySubmitDTO;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeTemplatePageQueryDTO;
import com.kun.linkage.customer.facade.dto.organization.fee.OrganizationFeeTemplateReviewRecordPageQueryDTO;
import com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeTemplateAndDetailReviewRecordVO;
import com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeTemplateAndDetailVO;
import com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeTemplateReviewRecordVO;
import com.kun.linkage.customer.facade.vo.organization.fee.OrganizationFeeTemplateVO;
import com.kun.linkage.customer.service.organization.OrganizationFeeTemplateBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 机构费率模版管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Tag(name = "机构费率模版管理", description = "机构费率模版管理")
@RestController
@RequestMapping("/boss/organizationFeeTemplate")
public class BossOrganizationFeeTemplateController extends BaseVccBossController {
    @Resource
    private OrganizationFeeTemplateBizService organizationFeeTemplateBizService;

    /**
     * 分页查询机构费率模版
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询机构费率模版信息", summary = "分页查询机构费率模版信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFee"})
    @GetMapping("/pageList")
    public Result<PageResult<OrganizationFeeTemplateVO>> pageList(OrganizationFeeTemplatePageQueryDTO dto) {
        PageResult<OrganizationFeeTemplateVO> list = organizationFeeTemplateBizService.pageList(dto);
        return Result.success(list);
    }

    /**
     * 查询所有有效模版信息
     * @return
     */
    @Operation(description = "查询所有有效模版信息", summary = "查询所有有效模版信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFee"})
    @GetMapping("/allValidList")
    public Result<List<OrganizationFeeTemplateVO>> allValidList() {
        List<OrganizationFeeTemplateVO> list = organizationFeeTemplateBizService.allValidList();
        return Result.success(list);
    }

    /**
     * 查询机构费率模版明细信息
     *
     * @param templateNo
     * @return
     */
    @Operation(description = "查询机构费率模版明细信息", summary = "查询机构费率模版明细信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFee"})
    @GetMapping("/detail")
    public Result<OrganizationFeeTemplateAndDetailVO> detail(@RequestParam(name = "templateNo") String templateNo) {
        return organizationFeeTemplateBizService.detail(templateNo);
    }


    /**
     * 提交新增机构费率模版信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "提交新增机构费率模版信息", summary = "提交新增机构费率模版信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFee"})
    @PostMapping("/addSubmit")
    public Result<Void> addSubmit(@RequestBody @Validated OrganizationFeeTemplateAddSubmitDTO dto) {
        return organizationFeeTemplateBizService.addSubmit(dto, this.getCurrentBossUserVO());
    }

    /**
     * 提交修改机构费率模版信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "提交修改机构费率模版信息", summary = "提交修改机构费率模版信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFee"})
    @PostMapping("/modifySubmit")
    public Result<Void> modifySubmit(@RequestBody @Validated OrganizationFeeTemplateModifySubmitDTO dto) {
        return organizationFeeTemplateBizService.modifySubmit(dto, this.getCurrentBossUserVO());
    }

    /**
     * 审核数据
     *
     * @param dto
     * @return
     */
    @Operation(description = "审核数据", summary = "审核数据")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFee"})
    @PostMapping("/review")
    public Result<Void> review(@RequestBody @Validated ReviewDTO dto) {
        return organizationFeeTemplateBizService.review(dto, this.getCurrentBossUserVO());
    }

    /**
     * 分页查询审核信息
     *
     * @param dto
     * @return
     */
    @Operation(description = "分页查询审核信息", summary = "分页查询审核信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFee"})
    @GetMapping("/review/pageList")
    public Result<PageResult<OrganizationFeeTemplateReviewRecordVO>> reviewPageList(OrganizationFeeTemplateReviewRecordPageQueryDTO dto) {
        PageResult<OrganizationFeeTemplateReviewRecordVO> list = organizationFeeTemplateBizService.reviewPageList(dto);
        return Result.success(list);
    }

    /**
     * 查询审核机构费率模版明细信息
     *
     * @param reviewId
     * @return
     */
    @Operation(description = "查询审核机构费率模版明细信息", summary = "查询审核机构费率模版明细信息")
    @VerifyVccBossPermission(verifyCodes = {"boss-kl-organizationFee"})
    @GetMapping("/review/detail")
    public Result<OrganizationFeeTemplateAndDetailReviewRecordVO> reviewDetail(@RequestParam(name = "reviewId") String reviewId) {
        return organizationFeeTemplateBizService.reviewDetail(reviewId);
    }
}
