package com.kun.linkage.customer.consumer;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.lark.LarkAlarmUtil;
import com.kun.linkage.common.base.constants.MqConsumerGroupConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.base.enums.OperationStatusEnum;
import com.kun.linkage.common.db.entity.CardRechargeDetail;
import com.kun.linkage.common.db.mapper.CardRechargeDetailMapper;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import com.kun.linkage.customer.facade.constants.CustomerLockConstant;
import com.kun.linkage.customer.facade.enums.DeductProcessorEnum;
import com.kun.linkage.customer.facade.vo.mq.CardRechargeBookkeepReversalEventVO;
import com.kun.linkage.customer.service.CardRechargeBizService;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 卡充值记账冲正通知事件消费者
 */
@Component
@RocketMQMessageListener(consumerGroup = MqConsumerGroupConstant.KL_CUSTOMER_CARD_RECHARGE_BOOKKEEP_REVERSAL_GROUP,
        topic = MqTopicConstant.CARD_RECHARGE_BOOKKEEP_REVERSAL_EVENT_TOPIC, messageModel = MessageModel.CLUSTERING)
public class CardRechargeBookkeepReversalEventConsumer implements RocketMQListener<CardRechargeBookkeepReversalEventVO> {
    private static final Logger log = LoggerFactory.getLogger(CardRechargeBookkeepReversalEventConsumer.class);
    @Resource
    private CardRechargeDetailMapper cardRechargeDetailMapper;
    @Resource
    private CardRechargeBizService cardRechargeBizService;
    @Resource
    private RedissonLockUtil redissonLockUtil;
    @Resource
    private LarkAlarmUtil larkAlarmUtil;

    @Override
    @NewSpan
    public void onMessage(CardRechargeBookkeepReversalEventVO eventVO) {
        MDC.put("traceId", eventVO.getLogContext().getTraceId());
        log.info("[卡充值记账冲正通知事件]接收到事件请求:{}", JSON.toJSONString(eventVO));
        RLock lock = null;
        boolean retryFlag = false;
        CardRechargeDetail cardRechargeDetail = null;
        try {
            if (StringUtils.isBlank(eventVO.getCardRechargeDetailId()) || StringUtils.isBlank(eventVO.getMpcGroupCode())
                    || eventVO.getRechargeDatetime() == null) {
                log.error("[卡充值记账冲正通知事件]请求参数异常,请检查,请求参数:{}", JSON.toJSONString(eventVO));
                return;
            }
            // 使用卡充值记录id作为key
            String lockKey = CustomerLockConstant.CARD_RECHARGE_BOOKKEEP_REVERSAL_LOCK_PREFIX + eventVO.getCardRechargeDetailId();
            lock = redissonLockUtil.getLock(lockKey);
            if (lock == null || !lock.tryLock()) {
                log.warn("[卡充值记账冲正通知事件]获取锁失败,lockKey:{}", lockKey);
                retryFlag = true;
            } else {
                cardRechargeDetail = cardRechargeDetailMapper.selectOne(Wrappers.<CardRechargeDetail>lambdaQuery()
                        .eq(CardRechargeDetail::getId, eventVO.getCardRechargeDetailId())
                        .eq(CardRechargeDetail::getRechargeDatetime, eventVO.getRechargeDatetime())
                        .eq(CardRechargeDetail::getRechargeStatus, OperationStatusEnum.FAIL.getStatus()));
                if (cardRechargeDetail == null) {
                    // 只有失败的才会需要冲账
                    log.error("[卡充值记账冲正通知事件]未找到处理失败的卡充值记录明细,请检查,卡充值记录明细ID:{},充值日期时间:{}",
                            eventVO.getCardRechargeDetailId(), eventVO.getRechargeDatetime());
                    return;
                }
                if (StringUtils.equals(cardRechargeDetail.getDeductProcessor(), DeductProcessorEnum.KUN.getValue())) {
                    cardRechargeBizService.processingDigitalCurrencyBookkeepReversal(cardRechargeDetail, eventVO.getMpcToken(), eventVO.getMpcGroupCode());
                } else {
                    cardRechargeBizService.processingFiatCurrencyBookkeepReversal(cardRechargeDetail, eventVO.getMpcToken(), eventVO.getMpcGroupCode());
                }
            }
        } catch (Exception e) {
            log.error("[卡充值记账冲正通知事件]处理失败,异常信息:", e);
            retryFlag = true;
        } finally {
            if (cardRechargeDetail != null) {
                LocalDateTime now = LocalDateTime.now();
                cardRechargeDetail.setBookkeepReversalCount(cardRechargeDetail.getBookkeepReversalCount() + 1);
                cardRechargeDetail.setLastModifyTime(now);
                    int row = cardRechargeDetailMapper.update(cardRechargeDetail,
                            Wrappers.<CardRechargeDetail>lambdaQuery()
                                    .eq(CardRechargeDetail::getId, cardRechargeDetail.getId())
                                    .eq(CardRechargeDetail::getRechargeDatetime, cardRechargeDetail.getRechargeDatetime()));
                    log.info("update {} row cardRechargeDetail", row);

            }
            redissonLockUtil.unlock(lock);
            if (retryFlag && (cardRechargeDetail != null && cardRechargeDetail.getBookkeepReversalCount() < 5)) {
                log.warn("[卡充值记账冲正通知事件]事件处理失败,30秒后进行重试");
                cardRechargeBizService.sendCardRechargeBookkeepReversalToMq(cardRechargeDetail, eventVO.getMpcToken(), eventVO.getMpcGroupCode());
            }
            // 发送告警
            if (retryFlag && (cardRechargeDetail != null && cardRechargeDetail.getBookkeepReversalCount() >= 5)) {
                log.warn("[卡充值记账冲正通知事件]重试后依旧失败,发送LARK告警");
                this.sendLarkAlarm(cardRechargeDetail);
            }
        }
    }

    /**
     * 发送LARK告警
     * @param cardRechargeDetail
     */
    private void sendLarkAlarm(CardRechargeDetail cardRechargeDetail) {
        String msg = String.format("[卡充值记账冲正异常]机构号:%s, 客户号:%s，卡id:%s, 充值金额:%s, 充值币种:%s, 请求流水号:%s",
                cardRechargeDetail.getOrganizationNo(), cardRechargeDetail.getCustomerId(), cardRechargeDetail.getCardId(),
                cardRechargeDetail.getRechargeAmount(), cardRechargeDetail.getRechargeCurrencyCode(), cardRechargeDetail.getRequestNo());
        larkAlarmUtil.sendTextAlarm(msg);
    }
}
