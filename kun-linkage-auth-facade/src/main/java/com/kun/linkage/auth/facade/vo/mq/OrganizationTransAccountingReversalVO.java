package com.kun.linkage.auth.facade.vo.mq;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.deser.std.DateDeserializers;
import com.fasterxml.jackson.databind.ser.std.DateSerializer;
import com.kun.common.util.mq.BaseMqMessage;

import java.io.Serializable;
import java.util.Date;

/**
 * 机构记账冲正通知事件VO 用于在Kun和PayX之间进行记账冲正操作时传递必要的信息
 */
public class OrganizationTransAccountingReversalVO extends BaseMqMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    private String organizationAccountingDetailId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = DateDeserializers.DateDeserializer.class)
    @JsonSerialize(using = DateSerializer.class)
    private Date createTime;

    public String getOrganizationAccountingDetailId() {
        return organizationAccountingDetailId;
    }

    public void setOrganizationAccountingDetailId(String organizationAccountingDetailId) {
        this.organizationAccountingDetailId = organizationAccountingDetailId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}
