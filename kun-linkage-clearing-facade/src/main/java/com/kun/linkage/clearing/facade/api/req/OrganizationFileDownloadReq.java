package com.kun.linkage.clearing.facade.api.req;

import com.kun.linkage.clearing.facade.constant.OrganizationFileTypeEnum;
import com.kun.linkage.common.base.annotation.EnumValue;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

public class OrganizationFileDownloadReq implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 机构号
     */
    @Schema(description = "机构号")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    private String organizationNo;
    /**
     * 文件类型
     */
    @Schema(description = "文件类型")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @EnumValue(enumClass = OrganizationFileTypeEnum.class, message = CommonTipConstant.REQUEST_PARAM_ERROR)
    private String fileType;
    /**
     * 文件日期
     */
    @Schema(description = "文件日期")
    @NotBlank(message = CommonTipConstant.REQUEST_PARAM_MISSING)
    @DateTimeFormat(pattern = "yyyyMMdd")
    private String fileDate;

    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileDate() {
        return fileDate;
    }

    public void setFileDate(String fileDate) {
        this.fileDate = fileDate;
    }
}
