package com.kun.linkage.clearing.facade.api.res;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;

public class OrganizationFileDownloadRes implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * base64编码之后的文件
     */
    @Schema(description = "base64编码之后的文件")
    private String base64File;

    public String getBase64File() {
        return base64File;
    }

    public void setBase64File(String base64File) {
        this.base64File = base64File;
    }
}
