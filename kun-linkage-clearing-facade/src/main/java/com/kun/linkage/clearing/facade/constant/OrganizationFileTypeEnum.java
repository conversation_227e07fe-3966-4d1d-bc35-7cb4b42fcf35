package com.kun.linkage.clearing.facade.constant;

public enum OrganizationFileTypeEnum {
    SETTLEMENT_FILE("SETTLEMENT_FILE", "settlementFile", "Settlement_%s_%s.csv", "清算文件"),
    RELEASE_FILE("RELEASE_FILE", "releaseFile", "Release_%s_%s.csv", "释放文件");

    private final String value;
    private final String fileDir;
    private final String fileNameFormat;
    private final String desc;

    OrganizationFileTypeEnum(String value, String fileDir, String fileNameFormat, String desc) {
        this.value = value;
        this.fileDir = fileDir;
        this.fileNameFormat = fileNameFormat;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getFileDir() {
        return fileDir;
    }

    public String getFileNameFormat() {
        return fileNameFormat;
    }

    public String getDesc() {
        return desc;
    }

    public static OrganizationFileTypeEnum getEnumByValue(String value) {
        for (OrganizationFileTypeEnum itemEnum : OrganizationFileTypeEnum.values()) {
            if (itemEnum.getValue().equals(value)) {
                return itemEnum;
            }
        }
        return null;
    }
}
