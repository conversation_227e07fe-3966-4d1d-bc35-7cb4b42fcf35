package com.kun.linkage.clearing.facade.constant;

public enum KLClearingExceptionTypeEnum {
    /**
     * 持卡人记账异常
     */
    CARDHOLDER_ACCOUNTING_EXCEPTION("cardholder_accounting_exception", "持卡人记账异常"),

    /**
     * 机构记账异常
     */
    ORGANIZATION_ACCOUNTING_EXCEPTION("organization_accounting_exception_reversal", "机构记账异常"),
    ;

    private String value;

    private String description;

    private String dictType = "KL_CLEARING_EXCEPTION@EXCEPTION_TYPE";

    KLClearingExceptionTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public String getDictType() {
        return dictType;
    }
}
