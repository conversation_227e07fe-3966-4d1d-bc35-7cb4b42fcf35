package com.kun.linkage.clearing.facade.vo.kunlinkage;

import java.io.Serializable;

public class OrganizationReleaseFileVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 交易id
     */
    private String transactionId;
    /**
     * 专用卡id
     */
    private String cardTkId;
    /**
     * 专用客户id
     */
    private String customerTkId;
    /**
     * 交易类型
     */
    private String transactionType;
    /**
     * 交易币种
     */
    private String transactionCurrencyCode;
    /**
     * 交易金额
     */
    private String transactionAmount;
    /**
     * 请求partner保证金币种
     */
    private String settleCurrencyCode;
    /**
     * 请求partner保证金金额
     */
    private String settleAmount;
    /**
     * 持卡人币种
     */
    private String cardholderCurrencyCode;
    /**
     * 持卡人释放金额
     */
    private String cardholderAmount;
    /**
     * 参考号
     */
    private String referenceNo;
    /**
     * 授权码
     */
    private String approvalCode;
    /**
     * MCC
     */
    private String merchantType;
    /**
     * 商户终端号
     */
    private String cardAcceptorTerminalCode;
    /**
     * 商户号
     */
    private String cardAcceptorIdentification;
    /**
     * 商户名称
     */
    private String cardAcceptorName;
    /**
     * 商户城市
     */
    private String cardAcceptorCity;
    /**
     * 商户国家码
     */
    private String cardAcceptorCountry;

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getCardTkId() {
        return cardTkId;
    }

    public void setCardTkId(String cardTkId) {
        this.cardTkId = cardTkId;
    }

    public String getCustomerTkId() {
        return customerTkId;
    }

    public void setCustomerTkId(String customerTkId) {
        this.customerTkId = customerTkId;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getTransactionCurrencyCode() {
        return transactionCurrencyCode;
    }

    public void setTransactionCurrencyCode(String transactionCurrencyCode) {
        this.transactionCurrencyCode = transactionCurrencyCode;
    }

    public String getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(String transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public String getSettleCurrencyCode() {
        return settleCurrencyCode;
    }

    public void setSettleCurrencyCode(String settleCurrencyCode) {
        this.settleCurrencyCode = settleCurrencyCode;
    }

    public String getSettleAmount() {
        return settleAmount;
    }

    public void setSettleAmount(String settleAmount) {
        this.settleAmount = settleAmount;
    }

    public String getCardholderCurrencyCode() {
        return cardholderCurrencyCode;
    }

    public void setCardholderCurrencyCode(String cardholderCurrencyCode) {
        this.cardholderCurrencyCode = cardholderCurrencyCode;
    }

    public String getCardholderAmount() {
        return cardholderAmount;
    }

    public void setCardholderAmount(String cardholderAmount) {
        this.cardholderAmount = cardholderAmount;
    }

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public String getApprovalCode() {
        return approvalCode;
    }

    public void setApprovalCode(String approvalCode) {
        this.approvalCode = approvalCode;
    }

    public String getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(String merchantType) {
        this.merchantType = merchantType;
    }

    public String getCardAcceptorTerminalCode() {
        return cardAcceptorTerminalCode;
    }

    public void setCardAcceptorTerminalCode(String cardAcceptorTerminalCode) {
        this.cardAcceptorTerminalCode = cardAcceptorTerminalCode;
    }

    public String getCardAcceptorIdentification() {
        return cardAcceptorIdentification;
    }

    public void setCardAcceptorIdentification(String cardAcceptorIdentification) {
        this.cardAcceptorIdentification = cardAcceptorIdentification;
    }

    public String getCardAcceptorName() {
        return cardAcceptorName;
    }

    public void setCardAcceptorName(String cardAcceptorName) {
        this.cardAcceptorName = cardAcceptorName;
    }

    public String getCardAcceptorCity() {
        return cardAcceptorCity;
    }

    public void setCardAcceptorCity(String cardAcceptorCity) {
        this.cardAcceptorCity = cardAcceptorCity;
    }

    public String getCardAcceptorCountry() {
        return cardAcceptorCountry;
    }

    public void setCardAcceptorCountry(String cardAcceptorCountry) {
        this.cardAcceptorCountry = cardAcceptorCountry;
    }
}
