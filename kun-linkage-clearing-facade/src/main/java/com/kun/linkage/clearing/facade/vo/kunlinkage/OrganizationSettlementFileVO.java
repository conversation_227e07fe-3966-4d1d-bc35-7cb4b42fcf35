package com.kun.linkage.clearing.facade.vo.kunlinkage;

import java.io.Serializable;

public class OrganizationSettlementFileVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 清算日期
     */
    private String clearDate;
    /**
     * 清算id
     */
    private String clearId;
    /**
     * 原清算id
     */
    private String originalClearId;
    /**
     * 交易id
     */
    private String transactionId;
    /**
     * 专用卡id
     */
    private String cardTkId;
    /**
     * 专用客户id
     */
    private String customerTkId;
    /**
     * 借贷记标识
     * C=Credit
     * D=Debit
     */
    private String debitCreditIndicator;
    /**
     * 交易类型
     */
    private String transactionType;
    /**
     * 交易币种
     */
    private String transactionCurrencyCode;
    /**
     * 交易金额
     */
    private String transactionAmount;
    /**
     * 清算的持卡人币种
     */
    private String settlementCurrencyCode;
    /**
     * 清算的持卡人金额
     */
    private String settlementAmount;
    /**
     * 交易日期时间
     */
    private String transactionDatetime;
    /**
     * 参考号
     */
    private String referenceNo;
    /**
     * 授权码
     */
    private String approveCode;
    /**
     * MCC
     */
    private String merchantType;
    /**
     * 商户号
     */
    private String cardAcceptorIdentification;
    /**
     * 商户名称
     */
    private String cardAcceptorName;
    /**
     * 商户城市
     */
    private String cardAcceptorCity;
    /**
     * 商户国家码
     */
    private String cardAcceptorCountry;

    public String getClearDate() {
        return clearDate;
    }

    public void setClearDate(String clearDate) {
        this.clearDate = clearDate;
    }

    public String getClearId() {
        return clearId;
    }

    public void setClearId(String clearId) {
        this.clearId = clearId;
    }

    public String getOriginalClearId() {
        return originalClearId;
    }

    public void setOriginalClearId(String originalClearId) {
        this.originalClearId = originalClearId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getCardTkId() {
        return cardTkId;
    }

    public void setCardTkId(String cardTkId) {
        this.cardTkId = cardTkId;
    }

    public String getCustomerTkId() {
        return customerTkId;
    }

    public void setCustomerTkId(String customerTkId) {
        this.customerTkId = customerTkId;
    }

    public String getDebitCreditIndicator() {
        return debitCreditIndicator;
    }

    public void setDebitCreditIndicator(String debitCreditIndicator) {
        this.debitCreditIndicator = debitCreditIndicator;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getTransactionCurrencyCode() {
        return transactionCurrencyCode;
    }

    public void setTransactionCurrencyCode(String transactionCurrencyCode) {
        this.transactionCurrencyCode = transactionCurrencyCode;
    }

    public String getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(String transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public String getSettlementCurrencyCode() {
        return settlementCurrencyCode;
    }

    public void setSettlementCurrencyCode(String settlementCurrencyCode) {
        this.settlementCurrencyCode = settlementCurrencyCode;
    }

    public String getSettlementAmount() {
        return settlementAmount;
    }

    public void setSettlementAmount(String settlementAmount) {
        this.settlementAmount = settlementAmount;
    }

    public String getTransactionDatetime() {
        return transactionDatetime;
    }

    public void setTransactionDatetime(String transactionDatetime) {
        this.transactionDatetime = transactionDatetime;
    }

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public String getApproveCode() {
        return approveCode;
    }

    public void setApproveCode(String approveCode) {
        this.approveCode = approveCode;
    }

    public String getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(String merchantType) {
        this.merchantType = merchantType;
    }

    public String getCardAcceptorIdentification() {
        return cardAcceptorIdentification;
    }

    public void setCardAcceptorIdentification(String cardAcceptorIdentification) {
        this.cardAcceptorIdentification = cardAcceptorIdentification;
    }

    public String getCardAcceptorName() {
        return cardAcceptorName;
    }

    public void setCardAcceptorName(String cardAcceptorName) {
        this.cardAcceptorName = cardAcceptorName;
    }

    public String getCardAcceptorCity() {
        return cardAcceptorCity;
    }

    public void setCardAcceptorCity(String cardAcceptorCity) {
        this.cardAcceptorCity = cardAcceptorCity;
    }

    public String getCardAcceptorCountry() {
        return cardAcceptorCountry;
    }

    public void setCardAcceptorCountry(String cardAcceptorCountry) {
        this.cardAcceptorCountry = cardAcceptorCountry;
    }
}
