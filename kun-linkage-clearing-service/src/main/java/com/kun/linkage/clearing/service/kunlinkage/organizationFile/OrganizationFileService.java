package com.kun.linkage.clearing.service.kunlinkage.organizationFile;

import cn.hutool.core.codec.Base64;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.aws.AwsS3Util;
import com.kun.common.util.aws.AwzS3Properties;
import com.kun.linkage.clearing.facade.api.req.OrganizationFileDownloadReq;
import com.kun.linkage.clearing.facade.api.res.OrganizationFileDownloadRes;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.enums.OperationStatusEnum;
import com.kun.linkage.common.db.entity.OrganizationFileRecord;
import com.kun.linkage.common.db.mapper.OrganizationFileRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <p>
 * 机构文件服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Service
public class OrganizationFileService {
    private static final Logger log = LoggerFactory.getLogger(OrganizationFileService.class);
    @Resource
    private OrganizationFileRecordMapper organizationFileRecordMapper;
    @Resource
    private AwzS3Properties awzS3Properties;

    /**
     * 下载机构文件
     *
     * @param downloadReq
     * @return
     */
    public Result<OrganizationFileDownloadRes> downloadFile(OrganizationFileDownloadReq downloadReq) throws IOException {
        OrganizationFileRecord organizationFileRecord = organizationFileRecordMapper.selectOne(Wrappers.<OrganizationFileRecord>lambdaQuery()
                .eq(OrganizationFileRecord::getOrganizationNo, downloadReq.getOrganizationNo())
                .eq(OrganizationFileRecord::getFileType, downloadReq.getFileType())
                .eq(OrganizationFileRecord::getFileGenerateStatus, OperationStatusEnum.SUCCESS.getStatus())
                .eq(OrganizationFileRecord::getFileDate, downloadReq.getFileDate()));
        if (organizationFileRecord == null || StringUtils.isBlank(organizationFileRecord.getFileFullPath()) || StringUtils.isBlank(organizationFileRecord.getFilePathName())) {
            log.error("未找到机构文件记录,机构编号:{},文件类型:{},文件日期:{}", downloadReq.getOrganizationNo(), downloadReq.getFileType(), downloadReq.getFileDate());
            return Result.fail(CommonTipConstant.FILE_NOT_FOUND);
        }
        ResponseEntity<byte[]> responseEntity = AwsS3Util.downloadByName(awzS3Properties.getBucket(), organizationFileRecord.getFilePathName());
        if (responseEntity == null || responseEntity.getBody() == null || responseEntity.getBody().length == 0) {
            log.error("文件下载失败,URL:{}", organizationFileRecord.getFileFullPath());
            return Result.fail(CommonTipConstant.FILE_NOT_FOUND);
        }
        OrganizationFileDownloadRes res = new OrganizationFileDownloadRes();
        res.setBase64File(Base64.encode(responseEntity.getBody()));
        return Result.success(res);
    }
}
