package com.kun.linkage.clearing.enums;

public enum ClearingTransCodeEnum {

    SALES("05", "消费",CalculationEnum.SUBTRACTION),
    REFUND("06", "退货",CalculationEnum.ADDITION),
    WITHDRAWAL("07", "取现",CalculationEnum.SUBTRACTION),
    SALES_VOID("25", "消费撤销",CalculationEnum.ADDITION),
    REFUND_VOID("26", "退货撤销",CalculationEnum.SUBTRACTION),
    WITHDRAWAL_VOID("27", "取现撤销",CalculationEnum.ADDITION),
    DISPUTE("33", "争议",CalculationEnum.SUBTRACTION),
    DISPUTE_15("15", "争议",CalculationEnum.SUBTRACTION),
    DISPUTE_16("16", "争议",CalculationEnum.SUBTRACTION),
    DISPUTE_17("17", "争议",CalculationEnum.SUBTRACTION),
    DISPUTE_35("35", "争议",CalculationEnum.SUBTRACTION),
    DISPUTE_36("36", "争议",CalculationEnum.SUBTRACTION),
    DISPUTE_37("37", "争议",CalculationEnum.SUBTRACTION),;

    private String code;
    private String description;
    private CalculationEnum calculationEnum;


    ClearingTransCodeEnum(String code, String description, CalculationEnum calculationEnum) {
        this.code = code;
        this.description = description;
        this.calculationEnum = calculationEnum;
    }

    public String getCode() {
        return code;
    }


    public String getDescription() {
        return description;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public CalculationEnum getCalculationEnum() {
        return calculationEnum;
    }

    public void setCalculationEnum(CalculationEnum calculationEnum) {
        this.calculationEnum = calculationEnum;
    }

    public static ClearingTransCodeEnum fromCode(String code) {
        for (ClearingTransCodeEnum transCodeEnum : values()) {
            if (transCodeEnum.getCode().equals(code)) {
                return transCodeEnum;
            }
        }
        return null;
    }

}
