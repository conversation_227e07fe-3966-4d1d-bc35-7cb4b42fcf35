package com.kun.linkage.clearing.service.kunlinkage;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.uid.DateUtils;
import com.kun.common.util.uid.UidGenerator;
import com.kun.linkage.account.facade.constants.AccountTipConstant;
import com.kun.linkage.clearing.dto.TransactionClearingContext;
import com.kun.linkage.clearing.facade.constant.KLClearingExceptionReasonEnum;
import com.kun.linkage.clearing.facade.constant.KLClearingExceptionStatusEnum;
import com.kun.linkage.clearing.facade.constant.KLClearingExceptionTypeEnum;
import com.kun.linkage.clearing.facade.vo.boss.ClearingExceptionInquiryPageVO;
import com.kun.linkage.clearing.facade.vo.boss.ClearingExceptionInquiryRequestVO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonConstant;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.KLClearingExceptionTrans;
import com.kun.linkage.common.db.mapper.KLClearingExceptionTransMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class KLClearingExceptionTransService {

    @Resource
    private KLClearingExceptionTransMapper clearingExceptionTransMapper;

    /** 唯一ID生成器 */
    @Resource
    protected UidGenerator uidGenerator;

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void recordCardholderClearingExceptionTrans(TransactionClearingContext context, String errorCode, String errorMessage) {
        this.recordClearingExceptionTrans(context, KLClearingExceptionTypeEnum.CARDHOLDER_ACCOUNTING_EXCEPTION.getValue(), errorCode, errorMessage);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void recordOrganizationClearingExceptionTrans(TransactionClearingContext context, String errorCode, String errorMessage) {
        this.recordClearingExceptionTrans(context, KLClearingExceptionTypeEnum.ORGANIZATION_ACCOUNTING_EXCEPTION.getValue(), errorCode, errorMessage);
    }

    private void recordClearingExceptionTrans(TransactionClearingContext context, String exceptionType, String errorCode, String errorMessage) {
        try {
            KLClearingExceptionTrans clearingExceptionTrans = new KLClearingExceptionTrans();
            clearingExceptionTrans.setId(String.valueOf(uidGenerator.getUID()));
            clearingExceptionTrans.setClearingId(context.getClearingId());
            clearingExceptionTrans.setProcessor(context.getPostTransRequestVO().getProcessor());
            clearingExceptionTrans.setProcessorRequestId(context.getPostTransRequestVO().getProcessorRequestId());
            clearingExceptionTrans.setProcessorTransId(context.getPostTransRequestVO().getProcessorTransId());
            clearingExceptionTrans.setOriginalProcessorTransId(context.getPostTransRequestVO().getOriginalProcessorTransId());
            clearingExceptionTrans.setMerchantNo(context.getPostTransRequestVO().getMerchantNo());
            clearingExceptionTrans.setMerchantName(context.getOrganizationBasicInfo() == null ? null : context.getOrganizationBasicInfo().getOrganizationName());
            clearingExceptionTrans.setCustomerId(context.getOrganizationCustomerCardInfo() == null ? null : context.getOrganizationCustomerCardInfo().getCustomerId());
            clearingExceptionTrans.setStatus(context.getAuthFlow() == null ? null : context.getAuthFlow().getStatus());
            clearingExceptionTrans.setMti(context.getPostTransRequestVO().getMti());
            clearingExceptionTrans.setProcessingCode(context.getPostTransRequestVO().getProcessingCode());
            clearingExceptionTrans.setGatewayCardId(context.getPostTransRequestVO().getGatewayCardId());
            clearingExceptionTrans.setProcessorCardId(context.getPostTransRequestVO().getProcessorCardId());
            clearingExceptionTrans.setIssuerCardId(context.getPostTransRequestVO().getIssuerCardId());
            clearingExceptionTrans.setMaskedCardNo(context.getPostTransRequestVO().getMaskedCardNo());
            clearingExceptionTrans.setTransType(context.getPostTransRequestVO().getTransType());
            clearingExceptionTrans.setCardProductCode(context.getPostTransRequestVO().getCardProductCode());
            clearingExceptionTrans.setTransCurrency(context.getPostTransRequestVO().getTransCurrency());
            clearingExceptionTrans.setTransAmount(context.getPostTransRequestVO().getTransAmount());
            clearingExceptionTrans.setTransFee(context.getPostTransRequestVO().getTransFee());
            clearingExceptionTrans.setCardholderBillingCurrency(context.getPostTransRequestVO().getCardholderBillingCurrency());
            clearingExceptionTrans.setCardholderBillingAmount(context.getPostTransRequestVO().getCardholderBillingAmount());
            clearingExceptionTrans.setCardholderMarkupBillingAmount(
                context.getPostTransRequestVO().getCardholderMarkupBillingAmount());
            clearingExceptionTrans.setMarkupRate(context.getPostTransRequestVO().getMarkupRate());
            clearingExceptionTrans.setMarkupAmount(context.getPostTransRequestVO().getMarkupAmount());
            clearingExceptionTrans.setPosEntryMode(context.getPostTransRequestVO().getPosEntryMode());
            clearingExceptionTrans.setTransactionLocalDatetime(
                context.getPostTransRequestVO().getTransactionLocalDatetime());
            clearingExceptionTrans.setConversionRateCardholderBilling(
                context.getPostTransRequestVO().getConversionRateCardholderBilling());
            clearingExceptionTrans.setApproveCode(context.getPostTransRequestVO().getApproveCode());
            clearingExceptionTrans.setAcquireReferenceNo(context.getPostTransRequestVO().getAcquireReferenceNo());
            clearingExceptionTrans.setCardAcceptorName(context.getPostTransRequestVO().getCardAcceptorName());
            clearingExceptionTrans.setCardAcceptorId(context.getPostTransRequestVO().getCardAcceptorId());
            clearingExceptionTrans.setCardAcceptorTid(context.getPostTransRequestVO().getCardAcceptorTid());
            clearingExceptionTrans.setCardAcceptorCountryCode(context.getPostTransRequestVO().getCardAcceptorCountryCode());
            clearingExceptionTrans.setCardAcceptorPostalCode(context.getPostTransRequestVO().getCardAcceptorPostalCode());
            clearingExceptionTrans.setCardAcceptorRegion(context.getPostTransRequestVO().getCardAcceptorRegion());
            clearingExceptionTrans.setCardAcceptorCity(context.getPostTransRequestVO().getCardAcceptorCity());
            clearingExceptionTrans.setCardAcceptorStreet(context.getPostTransRequestVO().getCardAcceptorStreet());
            clearingExceptionTrans.setMcc(context.getPostTransRequestVO().getMcc());
            clearingExceptionTrans.setArn(context.getPostTransRequestVO().getArn());
            clearingExceptionTrans.setProcessorExt1(context.getPostTransRequestVO().getProcessorExt1());
            clearingExceptionTrans.setOriginalAuthFlowId(context.getAuthFlow() == null ? null : context.getAuthFlow().getOriginalId());
            clearingExceptionTrans.setOriginalProcessorRequestId(context.getAuthFlow() == null ? null : context.getAuthFlow().getOriginalProcessorRequestId());
            clearingExceptionTrans.setOriginalTransTime(context.getAuthFlow() == null ? null : context.getAuthFlow().getOriginalTransTime());
            clearingExceptionTrans.setExceptionReason(convertClearingExceptionReason(errorCode, null));// 响应中的原因不能直接记，熔断的时候会导致超过字段长度
            clearingExceptionTrans.setExceptionType(exceptionType);
            clearingExceptionTrans.setTransDate(context.getPostTransRequestVO().getTransDate());
            clearingExceptionTrans.setTransTime(context.getAuthFlow() == null ? DateUtils.parseDate(context.getPostTransRequestVO().getTransactionLocalDatetime(),
                CommonConstant.YYYYMMDDHHMMSS) : context.getAuthFlow().getCreateTime());
            clearingExceptionTrans.setClearingDate(context.getPostTransRequestVO().getClearingDate());
            clearingExceptionTrans.setClearAmount(context.getPostTransRequestVO().getClearAmount());
            clearingExceptionTrans.setProcessFlag(KLClearingExceptionStatusEnum.TO_BE_PROCESSED.getValue());
            clearingExceptionTrans.setCreateTime(DateTimeUtils.getCurrentDateTime());
            clearingExceptionTrans.setUpdateTime(clearingExceptionTrans.getCreateTime());
            int insertRows = clearingExceptionTransMapper.insert(clearingExceptionTrans);
            log.error("保存清分异常记录: {}, 影响行数: {}", JSON.toJSONString(clearingExceptionTrans), insertRows);
        } catch (Exception e) {
            log.error("保存清分异常记录失败, 交易Id: {}, 错误码: {}, 错误信息: {}, 异常: {}",
                context.getClearingId(), errorCode, errorMessage, e.getMessage(), e);
        }
    }

    private String convertClearingExceptionReason(String errorCode, String errorMessage) {
        if (StringUtils.isBlank(errorCode)) {
            return KLClearingExceptionReasonEnum.UNKNOWN_ERROR.getValue();
        }
        switch (errorCode) {
            case AccountTipConstant.INSUFFICIENT_ACCOUNT_BALANCE:
            case CommonTipConstant.REQUEST_TIMEOUT:
                return KLClearingExceptionReasonEnum.ACCOUNTING_FAIL.getValue();
            default:
                return StringUtils.isBlank(errorMessage) ? errorCode : errorMessage;
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void saveClearingExceptionTrans(KLClearingExceptionTrans clearingExceptionTrans) {
        int insert = clearingExceptionTransMapper.insert(clearingExceptionTrans);
        log.info("保存清算异常记录, 清算ID: {}, 影响行数: {}", clearingExceptionTrans.getClearingId(), insert);
    }

    public PageResult<ClearingExceptionInquiryPageVO> pageList(ClearingExceptionInquiryRequestVO requestVO) {
        LambdaQueryWrapper<KLClearingExceptionTrans> wrapper = Wrappers.lambdaQuery();
        wrapper.ge(KLClearingExceptionTrans::getClearingDate, requestVO.getClearingDateFrom().replaceAll("-", ""));
        wrapper.le(KLClearingExceptionTrans::getClearingDate, requestVO.getClearingDateUntil().replaceAll("-", ""));
        if (requestVO.getClearingId() != null) {
            wrapper.eq(KLClearingExceptionTrans::getClearingId, requestVO.getClearingId());
        }
        if (requestVO.getProcessor() != null) {
            wrapper.eq(KLClearingExceptionTrans::getProcessor, requestVO.getProcessor());
        }
        if (requestVO.getAuthorizationDateFrom() != null) {
            wrapper.ge(KLClearingExceptionTrans::getTransTime, DateTimeUtils.truncateToSecond(
                DateUtils.parseDate(requestVO.getAuthorizationDateFrom(), DateUtils.DAY_PATTERN)));
        }
        if (requestVO.getAuthorizationDateUntil() != null) {
            wrapper.le(KLClearingExceptionTrans::getTransTime, DateTimeUtils.truncateToSecond(
                DateUtils.parseDate(requestVO.getAuthorizationDateFrom(), DateUtils.DAY_PATTERN)));
        }
        if (requestVO.getMerchantNo() != null) {
            wrapper.eq(KLClearingExceptionTrans::getMerchantNo, requestVO.getMerchantNo());
        }
        if (requestVO.getTransType() != null) {
            wrapper.eq(KLClearingExceptionTrans::getTransType, requestVO.getTransType());
        }
        if (requestVO.getAcquireReferenceNo() != null) {
            wrapper.eq(KLClearingExceptionTrans::getAcquireReferenceNo, requestVO.getAcquireReferenceNo());
        }
        if (requestVO.getArn() != null) {
            wrapper.eq(KLClearingExceptionTrans::getArn, requestVO.getArn());
        }
        if (requestVO.getGatewayCardId() != null) {
            wrapper.eq(KLClearingExceptionTrans::getGatewayCardId, requestVO.getGatewayCardId());
        }
        if (requestVO.getApproveCode() != null) {
            wrapper.eq(KLClearingExceptionTrans::getApproveCode, requestVO.getApproveCode());
        }
        if (requestVO.getProcessFlag() != null) {
            wrapper.eq(KLClearingExceptionTrans::getProcessFlag, requestVO.getProcessFlag());
        }
        if (requestVO.getCardNoSuffix() != null) {
            wrapper.eq(KLClearingExceptionTrans::getMaskedCardNo, requestVO.getCardNoSuffix());
        }
        wrapper.orderByDesc(KLClearingExceptionTrans::getCreateTime);
        PageResult<KLClearingExceptionTrans> pageResult =
            PageHelperUtil.getPage(requestVO, () -> clearingExceptionTransMapper.selectList(wrapper));
        PageResult<ClearingExceptionInquiryPageVO> result = convertToPageResult(pageResult);
        return result;
    }

    private PageResult<ClearingExceptionInquiryPageVO> convertToPageResult(
        PageResult<KLClearingExceptionTrans> pageResult) {
        List<ClearingExceptionInquiryPageVO> list = pageResult.getData().stream().map(clearingTrans -> {
            ClearingExceptionInquiryPageVO vo = new ClearingExceptionInquiryPageVO();
            vo.setClearingId(clearingTrans.getClearingId());
            vo.setProcessor(clearingTrans.getProcessor());
            vo.setClearingDate(DateUtils.formatDate(clearingTrans.getCreateTime(), DateUtils.DAY_PATTERN));
            if (clearingTrans.getTransTime() != null) {
                vo.setAuthorizationTime(DateUtils.formatDate(clearingTrans.getTransTime(), DateUtils.DATETIME_PATTERN));
            }
            vo.setMerchantNo(clearingTrans.getMerchantNo());
            vo.setMerchantName(clearingTrans.getMerchantName());
            vo.setGatewayCardId(clearingTrans.getGatewayCardId());
            vo.setTransType(clearingTrans.getTransType());
            vo.setTransCurrency(clearingTrans.getTransCurrency());
            vo.setTransCurrencyExponent(clearingTrans.getTransCurrencyExponent());
            vo.setTransAmount(clearingTrans.getTransAmount());
            vo.setCardholderCurrency(clearingTrans.getCardholderBillingCurrency());
            vo.setCardholderCurrencyExponent(clearingTrans.getCardholderCurrencyExponent());
            vo.setCardholderAmount(clearingTrans.getCardholderBillingAmount());
            vo.setCardholderMarkupAmount(clearingTrans.getCardholderMarkupBillingAmount());
            vo.setProcessBy(clearingTrans.getProcessBy());
            vo.setAcquireReferenceNo(clearingTrans.getAcquireReferenceNo());
            vo.setApproveCode(clearingTrans.getApproveCode());
            vo.setArn(clearingTrans.getArn());
            vo.setAuthTransId(clearingTrans.getAuthFlowId());
            vo.setExceptionReason(clearingTrans.getExceptionReason());
            vo.setProcessFlag(clearingTrans.getProcessFlag());
            vo.setOriginalClearingId(clearingTrans.getOriginalProcessorTransId());
            return vo;
        }).collect(Collectors.toList());
        return new PageResult<ClearingExceptionInquiryPageVO>(list, pageResult.getPageNum(), pageResult.getPageSize(),
            pageResult.getTotal(), pageResult.getExtraInfo());
    }

    public Result<Boolean> assignToMerchant(String clearingId) {
        // TODO: Implement the logic to assign the exception transaction to the merchant
        return Result.success(true);
    }

    public Result<Boolean> retry(String clearingId) {
        // TODO: Implement the logic to retry the exception transaction
        return Result.success(true);
    }
}
