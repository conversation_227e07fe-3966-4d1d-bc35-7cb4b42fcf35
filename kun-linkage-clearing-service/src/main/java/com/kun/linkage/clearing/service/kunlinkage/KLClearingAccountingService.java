package com.kun.linkage.clearing.service.kunlinkage;

import com.alibaba.fastjson.JSON;
import com.kun.common.util.uid.UidGenerator;
import com.kun.linkage.clearing.dto.TransactionClearingContext;
import com.kun.linkage.clearing.facade.constant.KunLinkageClearingResponseCodeEnum;
import com.kun.linkage.clearing.service.external.KunDigitalCurrencyService;
import com.kun.linkage.clearing.service.external.PayxFiatCurrencyService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.OrganizationAccountingDetailAccountingProcessorEnum;
import com.kun.linkage.common.base.constants.OrganizationAccountingDetailBizTypeEnum;
import com.kun.linkage.common.base.constants.OrganizationAccountingDetailBizUsageEnum;
import com.kun.linkage.common.base.dto.OrganizationAccountingDetailExternalRequestAttr;
import com.kun.linkage.common.base.enums.DigitalCurrencyEnum;
import com.kun.linkage.common.base.enums.FiatCurrencyEnum;
import com.kun.linkage.common.base.enums.OperationStatusEnum;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.OrganizationAccountingDetail;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXDirectionEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXRemarkEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunSideTypeEnum;
import com.kun.linkage.common.external.facade.api.kcard.res.KunDebitSubRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXDebitSubRsp;
import com.kun.linkage.customer.facade.enums.OrganizationPoolCurrencyCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

@Slf4j
@Service
public class KLClearingAccountingService {

    /** 唯一ID生成器 */
    @Resource
    protected UidGenerator uidGenerator;

    @Resource
    private OrganizationAccountingDetailService organizationAccountingDetailService;

    @Resource
    private KunDigitalCurrencyService kunDigitalCurrencyService;

    @Resource
    private PayxFiatCurrencyService payxFiatCurrencyService;

    @Resource
    private KLClearingExceptionTransService clearingExceptionTransService;

    /**
     * 处理卡清分商户记账
     *
     * @param context
     */
    public void processOrganizationAccounting(TransactionClearingContext context) {
        // 初始化商户记账信息
        this.initializeOrganizationTransAccounting(context);
        if (DigitalCurrencyEnum.contains(context.getOrganizationBasicInfo().getPoolCurrencyCode())) {
            // 扣除币种是数币
            log.info("清分记商户数币账开始,扣除币种:{}", context.getOrganizationBasicInfo().getPoolCurrencyCode());
            this.handleDigitalCurrencyTransaction(context);
        } else {
            log.info("清分记商户法币账开始,扣除币种:{}", context.getOrganizationBasicInfo().getPoolCurrencyCode());
            // 扣除币种是法币
            this.handleFiatCurrencyTransaction(context);
        }
        if (context.isNeedToRecordMerchantExceptionClearingTrans()) {
            // 需要记录商户异常清分清分
            log.info("处理卡清分商户记账异常,清分ID:{}", context.getClearingId());
            try {
                clearingExceptionTransService.recordOrganizationClearingExceptionTrans(context,
                    KunLinkageClearingResponseCodeEnum.ACCOUNTING_FAIL.getCode(),
                    context.getMerchantAccountingDetail().getFailMessage());
                context.setMerchantExceptionClearingTransRecorded(true);
            } catch (Exception e) {
                log.error("记录商户异常清分清分失败,清分ID:{}", context.getClearingId(), e);
            }
        } else {
            log.info("处理卡清分商户记账成功,清分ID:{}", context.getClearingId());
        }
    }

    /**
     * 处理法币清分的记账逻辑
     *
     * @param context
     * @return
     */
    private void handleFiatCurrencyTransaction(TransactionClearingContext context) {
        OrganizationAccountingDetail merchantAccountingDetail = context.getMerchantAccountingDetail();
        try {
            this.processingFiatCurrencyAccountingTransaction(context);
            merchantAccountingDetail.setAccountingProcessor(
                OrganizationAccountingDetailAccountingProcessorEnum.PAYX.getValue());
            // 获取KUN记账属性
            OrganizationAccountingDetailExternalRequestAttr payxAccountingAttr =
                this.getKunAndPayxAccountingAttr(context, merchantAccountingDetail,
                    merchantAccountingDetail.getAccountingProcessor());
            // 插入记账记录
            organizationAccountingDetailService.insertOrganizationAccountingDetail(merchantAccountingDetail);
            // 处理法币清分逻辑
            try {
                // 更新请求流水号
                merchantAccountingDetail.setRequestNo(String.valueOf(uidGenerator.getUID()));
                log.info("更新记账请求流水号, 记账ID: {}, 请求流水号: {}", merchantAccountingDetail.getId(),
                    merchantAccountingDetail.getRequestNo());
                organizationAccountingDetailService.updateRequestNo(merchantAccountingDetail);
                // 调用账户动账
                this.processingFiatCurrencyAccountingTransaction(context, payxAccountingAttr, merchantAccountingDetail);
                // 更新记账状态
                merchantAccountingDetail.setLastModifyTime(DateTimeUtils.getCurrentDateTime());
                organizationAccountingDetailService.updateAccountingResult(merchantAccountingDetail);
                if (OperationStatusEnum.SUCCESS.getStatus().equals(merchantAccountingDetail.getAccountingStatus())) {
                    // 记账成功
                    return;
                } else {
                    // 发送冲账事件到MQ
                    log.info("处理卡清分商户法币记账扣款失败,发送冲账事件到mq,清分ID:{}, 扣款币种:{}, 扣除金额:{}",
                        context.getClearingId(), merchantAccountingDetail.getAccountingCurrencyCode(),
                        merchantAccountingDetail.getAccountingAmount());
                }
            } catch (BusinessException e) {
                log.error("处理卡清分商户法币记账扣款业务异常", e);
                context.setNeedToRecordMerchantExceptionClearingTrans(true);
            } catch (Exception e) {
                log.error("处理卡清分商户法币记账扣款异常,发送冲账事件到mq", e);
                context.setNeedToRecordMerchantExceptionClearingTrans(true);
            }
        } catch (Exception e) {
            log.error("处理卡清分商户法币记账法币记账未知异常", e);
            context.setNeedToRecordMerchantExceptionClearingTrans(true);
        }
    }

    /**
     * 处理卡清分商户法币记账扣款
     *
     * @param context
     * @param kunAccountingAttr
     * @param merchantAccountingDetail
     */
    private void processingFiatCurrencyAccountingTransaction(TransactionClearingContext context,
        OrganizationAccountingDetailExternalRequestAttr kunAccountingAttr,
        OrganizationAccountingDetail merchantAccountingDetail) {
        Result<PayXDebitSubRsp> result =
            payxFiatCurrencyService.payxDebitSub(kunAccountingAttr.getMpcToken(), kunAccountingAttr.getMpcGroupCode(),
                merchantAccountingDetail.getOrganizationNo(), merchantAccountingDetail.getRequestNo(),
                merchantAccountingDetail.getAccountingCurrencyCode(), kunAccountingAttr.getDirection(),
                merchantAccountingDetail.getAccountingAmount(), KunAndPayXRemarkEnum.TRANSACTION_BPC.getRemark());
        // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
        if (result != null && StringUtils.equals(result.getCode(),
            String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
            if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                // 明确成功
                log.info("处理卡清分商户法币记账扣款成功,清分ID:{}, 扣款币种:{}, 扣除金额:{}", context.getClearingId(),
                    merchantAccountingDetail.getAccountingCurrencyCode(),
                    merchantAccountingDetail.getAccountingAmount());
                merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.SUCCESS.getStatus());
            } else if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.FAIL.getStatus())) {
                // 明确失败不需要冲账
                context.setNeedToRecordMerchantExceptionClearingTrans(true);
                log.info("处理卡清分商户法币记账扣款明确失败,清分ID:{}, 扣款币种:{}, 扣除金额:{}",
                    context.getClearingId(), merchantAccountingDetail.getAccountingCurrencyCode(),
                    merchantAccountingDetail.getAccountingAmount());
                merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.FAIL.getStatus());
                merchantAccountingDetail.setFailMessage(result.getMessage());
            } else {
                log.info("处理卡清分商户法币记账扣款状态未知,清分ID:{}, 扣款币种:{}, 扣除金额:{}",
                    context.getClearingId(), merchantAccountingDetail.getAccountingCurrencyCode(),
                    merchantAccountingDetail.getAccountingAmount());
                merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.FAIL.getStatus());
                merchantAccountingDetail.setFailMessage(result.getMessage());
                // 设置冲账标志为true, 需要冲账
                context.setNeedToRecordMerchantExceptionClearingTrans(true);
            }
        } else {
            log.info("处理卡清分商户法币记账扣款异常,清分ID:{}, 扣款币种:{}, 扣除金额:{}", context.getClearingId(),
                merchantAccountingDetail.getAccountingCurrencyCode(), merchantAccountingDetail.getAccountingAmount());
            merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.FAIL.getStatus());
            merchantAccountingDetail.setFailMessage("Unknown error occurred during accounting.");
            // 设置冲账标志为true, 需要冲账
            context.setNeedToRecordMerchantExceptionClearingTrans(true);
        }
    }

    /**
     * 获取KUN和PAYX的记账属性
     *
     * @param context 清分上下文
     * @return KUN和PAYX的记账属性
     */
    private void processingFiatCurrencyAccountingTransaction(TransactionClearingContext context) {
        BigDecimal fxRate = BigDecimal.ONE;
        OrganizationAccountingDetail merchantAccountingDetail = context.getMerchantAccountingDetail();
        if (!StringUtils.equals(merchantAccountingDetail.getAccountingCurrencyCode(),
            merchantAccountingDetail.getBizCurrencyCode())) {
            fxRate = payxFiatCurrencyService.askPrice(context.getOrganizationBasicInfo(),
                merchantAccountingDetail.getBizAmount(), merchantAccountingDetail.getBizCurrencyCode(),
                merchantAccountingDetail.getAccountingCurrencyCode());
        }
        merchantAccountingDetail.setFxRate(fxRate);
        log.info("处理卡清分商户法币记账法币转法币汇率计算结束,清分ID:{}, 汇率:{}", context.getClearingId(),
            merchantAccountingDetail.getFxRate());
        // 计算数币记账金额
        merchantAccountingDetail.setAccountingAmount(merchantAccountingDetail.getBizAmount().multiply(fxRate)
            .setScale(merchantAccountingDetail.getAccountingCurrencyPrecision(), RoundingMode.UP));
        log.info("处理卡清分商户数币记账法币转法币汇率计算结束,清分ID:{}, 扣除金额:{}", context.getClearingId(),
            merchantAccountingDetail.getAccountingAmount());
    }

    /**
     * 处理卡清分商户数币记账扣款
     *
     * @param context
     */
    private void handleDigitalCurrencyTransaction(TransactionClearingContext context) {
        OrganizationAccountingDetail merchantAccountingDetail = context.getMerchantAccountingDetail();
        try {
            // 换汇并计算数币记账金额
            this.processingDigitalCurrencyExchange(context);
            merchantAccountingDetail.setAccountingProcessor(
                OrganizationAccountingDetailAccountingProcessorEnum.KUN.getValue());
            // 获取KUN记账属性
            OrganizationAccountingDetailExternalRequestAttr kunAccountingAttr =
                this.getKunAndPayxAccountingAttr(context, merchantAccountingDetail,
                    merchantAccountingDetail.getAccountingProcessor());
            // 插入记账记录
            organizationAccountingDetailService.insertOrganizationAccountingDetail(merchantAccountingDetail);
            try {
                // 更新请求流水号
                merchantAccountingDetail.setRequestNo(String.valueOf(uidGenerator.getUID()));
                log.info("更新记账请求流水号, 记账ID: {}, 请求流水号: {}", merchantAccountingDetail.getId(),
                    merchantAccountingDetail.getRequestNo());
                // 更新请求流水号
                organizationAccountingDetailService.updateRequestNo(merchantAccountingDetail);
                // 调用账户动账
                this.processingDigitalCurrencyAccountingTransaction(context, kunAccountingAttr,
                    merchantAccountingDetail);
                // 更新记账状态
                merchantAccountingDetail.setLastModifyTime(DateTimeUtils.getCurrentDateTime());
                organizationAccountingDetailService.updateAccountingResult(merchantAccountingDetail);
            } catch (BusinessException e) {
                log.error("处理卡清分商户数币记账扣款业务异常", e);
                context.setNeedToRecordMerchantExceptionClearingTrans(true);
            } catch (Exception e) {
                log.error("处理卡清分商户数币记账扣款异常,发送重试事件到mq", e);
                context.setNeedToRecordMerchantExceptionClearingTrans(true);
            }
        } catch (Exception e) {
            log.error("处理卡清分商户数币记账法币记账未知异常", e);
            context.setNeedToRecordMerchantExceptionClearingTrans(true);
        }
    }

    /**
     * 处理卡清分商户数币记账扣款
     *
     * @param context
     * @param kunAccountingAttr
     * @param merchantAccountingDetail
     */
    private void processingDigitalCurrencyAccountingTransaction(TransactionClearingContext context,
        OrganizationAccountingDetailExternalRequestAttr kunAccountingAttr,
        OrganizationAccountingDetail merchantAccountingDetail) {
        Result<KunDebitSubRsp> kunDebitSubRspResult =
            kunDigitalCurrencyService.kunDebitSub(kunAccountingAttr.getMpcToken(), kunAccountingAttr.getMpcGroupCode(),
                merchantAccountingDetail.getOrganizationNo(), merchantAccountingDetail.getRequestNo(),
                merchantAccountingDetail.getAccountingCurrencyCode(), kunAccountingAttr.getDirection(),
                merchantAccountingDetail.getAccountingAmount(), KunAndPayXRemarkEnum.TRANSACTION_BPC.getRemark());
        // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
        if (kunDebitSubRspResult != null && StringUtils.equals(kunDebitSubRspResult.getCode(),
            String.valueOf(HttpStatus.SC_OK)) && kunDebitSubRspResult.getData() != null) {
            if (StringUtils.equals(kunDebitSubRspResult.getData().getStatus(),
                OperationStatusEnum.SUCCESS.getStatus())) {
                // 明确成功
                log.info("处理卡清分商户数币记账扣款成功,清分ID:{}, 扣款币种:{}, 扣除金额:{}", context.getClearingId(),
                    merchantAccountingDetail.getAccountingCurrencyCode(),
                    merchantAccountingDetail.getAccountingAmount());
                merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.SUCCESS.getStatus());
            } else if (StringUtils.equals(kunDebitSubRspResult.getData().getStatus(),
                OperationStatusEnum.FAIL.getStatus())) {
                // 明确失败不需要重试
                log.info("处理卡清分商户数币记账扣款明确失败,清分ID:{}, 扣款币种:{}, 扣除金额:{}",
                    context.getClearingId(), merchantAccountingDetail.getAccountingCurrencyCode(),
                    merchantAccountingDetail.getAccountingAmount());
                merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.FAIL.getStatus());
                merchantAccountingDetail.setFailMessage(kunDebitSubRspResult.getMessage());
                context.setNeedToRecordMerchantExceptionClearingTrans(true);
            } else {
                log.info("处理卡清分商户数币记账扣款状态未知,清分ID:{}, 扣款币种:{}, 扣除金额:{}",
                    context.getClearingId(), merchantAccountingDetail.getAccountingCurrencyCode(),
                    merchantAccountingDetail.getAccountingAmount());
                merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.FAIL.getStatus());
                merchantAccountingDetail.setFailMessage(kunDebitSubRspResult.getMessage());
                context.setNeedToRecordMerchantExceptionClearingTrans(true);
            }
        } else {
            log.info("处理卡清分商户数币记账扣款异常,清分ID:{}, 扣款币种:{}, 扣除金额:{}", context.getClearingId(),
                merchantAccountingDetail.getAccountingCurrencyCode(), merchantAccountingDetail.getAccountingAmount());
            merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.FAIL.getStatus());
            merchantAccountingDetail.setFailMessage("Unknown error occurred during accounting.");
            merchantAccountingDetail.setAccountingReversalStatus(OperationStatusEnum.PENDING.getStatus());
            context.setNeedToRecordMerchantExceptionClearingTrans(true);
        }
    }

    /**
     * 处理卡清分商户法币记账扣款
     *
     * @param context
     * @param merchantAccountingDetail
     */
    private OrganizationAccountingDetailExternalRequestAttr getKunAndPayxAccountingAttr(
        TransactionClearingContext context, OrganizationAccountingDetail merchantAccountingDetail, String processor) {
        merchantAccountingDetail.setAccountingProcessor(processor);
        OrganizationAccountingDetailExternalRequestAttr kunAccountingAttr =
            new OrganizationAccountingDetailExternalRequestAttr();
        kunAccountingAttr.setMpcToken(context.getOrganizationBasicInfo().getMpcToken());
        kunAccountingAttr.setMpcGroupCode(context.getOrganizationBasicInfo().getMpcGroupCode());
        kunAccountingAttr.setDirection(KunAndPayXDirectionEnum.TO_GROUP.getDirection());
        kunAccountingAttr.setRemark(KunAndPayXRemarkEnum.CLEARING_BPC.getRemark());
        merchantAccountingDetail.setExternalRequestAttr(JSON.toJSONString(kunAccountingAttr));
        return kunAccountingAttr;
    }

    /**
     * 处理卡清分商户数币记账法币转数币汇率计算
     *
     * @param context
     */
    private void processingDigitalCurrencyExchange(TransactionClearingContext context) {
        log.info("处理卡清分商户数币记账法币转数币汇率计算,清分ID:{}", context.getClearingId());
        BigDecimal fxRate = BigDecimal.ONE;
        OrganizationAccountingDetail organizationTransAccounting = context.getMerchantAccountingDetail();
        if (!(StringUtils.equals(organizationTransAccounting.getBizCurrencyCode(),
            FiatCurrencyEnum.USD.getCurrencyCode()) && StringUtils.equals(
            organizationTransAccounting.getAccountingCurrencyCode(), DigitalCurrencyEnum.USDT.getValue()))) {
            // 注意注意!!!!  kun的接口币对必须是数币在前、法币在后,返回的也是数币兑法币的汇率
            fxRate = kunDigitalCurrencyService.askPrice(context.getOrganizationBasicInfo(),
                organizationTransAccounting.getBizAmount(), KunSideTypeEnum.BUY,
                organizationTransAccounting.getAccountingCurrencyCode(),
                organizationTransAccounting.getBizCurrencyCode());
        }
        organizationTransAccounting.setFxRate(fxRate);
        log.info("处理卡清分商户数币记账法币转数币汇率计算结束,清分ID:{}, 汇率:{}", context.getClearingId(),
            organizationTransAccounting.getFxRate());
        // 计算数币记账金额
        organizationTransAccounting.setAccountingAmount(organizationTransAccounting.getBizAmount().multiply(fxRate)
            .setScale(organizationTransAccounting.getAccountingCurrencyPrecision(), RoundingMode.UP));
        log.info("处理卡清分商户数币记账法币转数币汇率计算结束,清分ID:{}, 扣除金额:{}", context.getClearingId(),
            organizationTransAccounting.getAccountingAmount());
    }

    /**
     * 初始化商户记账信息
     *
     * @param context 清分上下文
     * @return 商户记账信息
     */
    private OrganizationAccountingDetail initializeOrganizationTransAccounting(TransactionClearingContext context) {
        OrganizationAccountingDetail organizationAccountingDetail = new OrganizationAccountingDetail();
        organizationAccountingDetail.setId(String.valueOf(uidGenerator.getUID()));
        organizationAccountingDetail.setBizType(OrganizationAccountingDetailBizTypeEnum.CLEARING.getValue());
        organizationAccountingDetail.setBizUsage(OrganizationAccountingDetailBizUsageEnum.PRINCIPAL.getValue());
        organizationAccountingDetail.setOrganizationNo(context.getOrganizationBasicInfo().getOrganizationNo());
        organizationAccountingDetail.setCustomerId(context.getOrganizationCustomerCardInfo().getCustomerId());
        // 请求时填入
        organizationAccountingDetail.setRequestNo(null);
        // 业务方填写
        organizationAccountingDetail.setExternalRequestAttr(null);
        organizationAccountingDetail.setCardId(context.getOrganizationCustomerCardInfo().getCardId());
        organizationAccountingDetail.setBizTime(context.getClearingTrans().getCreateTime());
        if (context.getAuthFlow() != null) {
            organizationAccountingDetail.setBizAmount(
                context.getPostTransRequestVO().getCardholderMarkupBillingAmountOffset());
            organizationAccountingDetail.setBizCurrencyCode(context.getClearingTrans().getCardholderBillingCurrency());
            organizationAccountingDetail.setBizCurrencyPrecision(
                context.getClearingTrans().getCardholderCurrencyExponent());
        } else {
            organizationAccountingDetail.setBizAmount(
                context.getPostTransRequestVO().getCardholderMarkupBillingAmount());
            organizationAccountingDetail.setBizCurrencyCode(context.getClearingTrans().getCardholderBillingCurrency());
            organizationAccountingDetail.setBizCurrencyPrecision(
                context.getClearingTrans().getCardholderCurrencyExponent());
        }
        organizationAccountingDetail.setBizId(context.getClearingId());
        // 业务方填写
        organizationAccountingDetail.setFxRate(null);
        organizationAccountingDetail.setAccountingCurrencyCode(
            context.getOrganizationBasicInfo().getPoolCurrencyCode());
        // 业务方填写
        organizationAccountingDetail.setAccountingProcessor(null);
        // 从币种表取精度
        organizationAccountingDetail.setAccountingCurrencyPrecision(
            OrganizationPoolCurrencyCodeEnum.getPrecisionByValue(
                organizationAccountingDetail.getAccountingCurrencyCode()));
        // 业务方填写
        organizationAccountingDetail.setAccountingAmount(null);
        organizationAccountingDetail.setAccountingStatus(OperationStatusEnum.PENDING.getStatus());
        organizationAccountingDetail.setAccountingReversalCount(0);
        organizationAccountingDetail.setCreateTime(DateTimeUtils.getCurrentDateTime());
        organizationAccountingDetail.setLastModifyTime(organizationAccountingDetail.getCreateTime());
        context.setMerchantAccountingDetail(organizationAccountingDetail);
        return organizationAccountingDetail;
    }
}
