package com.kun.linkage.clearing.service.kunlinkage.organizationFile;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.kun.common.utils.utils.DateUtils;
import com.kun.linkage.clearing.ext.mapper.OrganizationFileExtMapper;
import com.kun.linkage.clearing.facade.vo.kunlinkage.OrganizationReleaseFileVO;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 机构结算文件生成业务服务类
 */
@Service
public class OrganizationReleaseFileBizService extends AbstractOrganizationFileService {
    private static final Logger log = LoggerFactory.getLogger(OrganizationReleaseFileBizService.class);
    @Resource
    private OrganizationFileExtMapper organizationFileExtMapper;

    @Override
    protected File generateFile(String fileDate, String organizationNo) {
        String startId = "0";
        // 获取文件日期那天的开始和结束时间
        LocalDateTime startReleaseTime = LocalDateTime.of(Integer.parseInt(fileDate.substring(0, 4)),
                Integer.parseInt(fileDate.substring(4, 6)),
                Integer.parseInt(fileDate.substring(6, 8)),
                0, 0, 0);
        LocalDateTime endReleaseTime = LocalDateTime.of(Integer.parseInt(fileDate.substring(0, 4)),
                Integer.parseInt(fileDate.substring(4, 6)),
                Integer.parseInt(fileDate.substring(6, 8)),
                23, 59, 59);
        // 授权流水表是分表的,释放时间不会超过30天,此处查找数据的时候使用当前时间向前推40天,以防万一
        // 配置文件中配置的分表策略必须是date类型的,不然会报错
        Date endCreateTime = DateTimeUtils.getCurrentDateTime();
        Date startCreateTime = DateUtils.addDays(endCreateTime, -40);
        List<OrganizationReleaseFileVO> allDataList = new ArrayList<>();
        while (true) {
            // 获取数据
            List<OrganizationReleaseFileVO> dataList =
                    organizationFileExtMapper.pageListReleaseFileData(
                            startReleaseTime, endReleaseTime, startCreateTime, endCreateTime, organizationNo, startId);
            if (dataList == null || dataList.isEmpty()) {
                // 没有待处理数据
                log.warn("文件日期:{},机构号:{},数据获取完成", fileDate, organizationNo);
                break;
            } else {
                dataList.forEach(item ->
                        item.setCardAcceptorName("\"" + (item.getCardAcceptorName() == null ? "" : item.getCardAcceptorName()) + "\""));
                startId = dataList.get(dataList.size() - 1).getTransactionId();
            }
            allDataList.addAll(dataList);
        }
        File tempFile = FileUtil.createTempFile();
        CsvWriter writer = CsvUtil.getWriter(tempFile, StandardCharsets.UTF_8);
        writer.writeBeans(allDataList);
        return tempFile;
    }
}
