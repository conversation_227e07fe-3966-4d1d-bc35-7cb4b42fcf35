package com.kun.linkage.clearing.controller.api;

import com.alibaba.fastjson.JSON;
import com.kun.linkage.clearing.constant.ClearingApplicationConstant;
import com.kun.linkage.clearing.facade.api.req.OrganizationFileDownloadReq;
import com.kun.linkage.clearing.facade.api.res.OrganizationFileDownloadRes;
import com.kun.linkage.clearing.service.kunlinkage.organizationFile.OrganizationFileService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 机构文件下载管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
@Tag(name = "机构文件管理", description = "机构文件管理")
@RestController
@RequestMapping("/api/organizationFile")
public class OrganizationFileController {
    protected Logger log = LoggerFactory.getLogger(String.valueOf(this.getClass()));
    @Resource
    private RedissonLockUtil redissonLockUtil;
    @Resource
    private OrganizationFileService organizationFileService;

    /**
     * 下载文件
     *
     * @param downloadReq
     * @return
     */
    @Operation(description = "下载文件", summary = "下载文件")
    @RequestMapping(value = "/download", method = RequestMethod.POST)
    public Result<OrganizationFileDownloadRes> download(@RequestBody @Validated OrganizationFileDownloadReq downloadReq){
        log.info("[机构文件下载]入参:{}", JSON.toJSONString(downloadReq));
        RLock lock = null;
        try {
            String lockKey = ClearingApplicationConstant.ORGANIZATION_FILE_DOWNLOAD_LOCK_PREFIX + downloadReq.getOrganizationNo()
                    + downloadReq.getFileType() + downloadReq.getFileDate();
            lock = redissonLockUtil.getLock(lockKey);
            if (lock != null && lock.tryLock()) {
                return organizationFileService.downloadFile(downloadReq);
            } else {
                log.error("[机构文件下载]获取锁失败,lockKey:{}", lockKey);
                return Result.fail(CommonTipConstant.DUPLICATE_REQUEST);
            }
        } catch (Exception e) {
            log.error("[机构文件下载]下载失败,异常信息:", e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        } finally {
            redissonLockUtil.unlock(lock);
        }
    }
}
