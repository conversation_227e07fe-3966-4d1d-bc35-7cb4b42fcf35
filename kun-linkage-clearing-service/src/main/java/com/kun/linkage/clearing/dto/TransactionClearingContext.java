package com.kun.linkage.clearing.dto;

import com.kun.linkage.auth.facade.constant.TransactionTypeCatogoryEnum;
import com.kun.linkage.auth.facade.constant.TransactionTypeEnum;
import com.kun.linkage.clearing.enums.ClearIngTypeEnum;
import com.kun.linkage.clearing.facade.vo.kunlinkage.PostTransRequestVO;
import com.kun.linkage.clearing.facade.vo.kunlinkage.PostTransResponseVO;
import com.kun.linkage.common.db.entity.*;

public class TransactionClearingContext {

    /**
     * Kun-Linkage清分ID
     */
    private String clearingId;

    /**
     * 清分请求数据
     */
    private PostTransRequestVO postTransRequestVO;

    /**
     * 清分响应数据
     */
    private PostTransResponseVO postTransResponseVO;

    /**
     * 交易类型枚举
     * <p>
     * 包含了各种交易类型的代码、方向和描述。
     */
    private TransactionTypeEnum transactionTypeEnum;

    /**
     * 清分类型枚举
     * <p>
     */
    private ClearIngTypeEnum clearIngTypeEnum;

    /**
     * 交易类型分类枚举
     * <p>
     * 用于将交易类型分为授权、退货和冲正/撤销等类别。
     */
    private TransactionTypeCatogoryEnum transactionTypeCatogoryEnum;

    /**
     * 机构信息
     */
    private OrganizationBasicInfo organizationBasicInfo;

    /**
     * 技术参数
     */
    private TechnicalParams technicalParams;

    /**
     * 机构用户卡信息
     */
    private OrganizationCustomerCardInfo organizationCustomerCardInfo;

    /**
     * 机构客户基础账户信息
     */
    private OrganizationCustomerAccountInfo customerBasicAccount;

    /**
     * 机构客户数币账户信息
     */
    private OrganizationCustomerAccountInfo customerCryptoAccount;

    /**
     * 机构客户信用账户信息
     */
    private OrganizationCustomerAccountInfo customerCreditAccount;

    /**
     * 授权交易记录
     */
    private AuthFlow authFlow;

    /**
     * 授权交易扩展信息
     */
    private AuthFlowExt authFlowExt;

    private AuthFlow originalAuthFlow;

    private AuthFlowExt originalAuthFlowExt;

    private AuthFlow firstAuthFlow;

    private AuthFlowExt firstAuthFlowExt;

    private KLClearingTrans clearingTrans;

    private KLClearingTrans originalClearingTrans;

    private boolean needToRecordCardholderExceptionClearingTrans = false;

    private boolean cardholderExceptionClearingTransRecorded = false;

    /**
     * 商户记账明细(交易本金)
     * <p>
     * 用于存储商户的记账明细信息。
     */
    private OrganizationAccountingDetail merchantAccountingDetail;

    /**
     * 是否需要记录商户异常清分交易
     */
    private boolean needToRecordMerchantExceptionClearingTrans = false;

    /**
     * 商户异常清分交易是否已记录
     */
    private boolean merchantExceptionClearingTransRecorded = false;

    public String getClearingId() {
        return clearingId;
    }

    public void setClearingId(String clearingId) {
        this.clearingId = clearingId;
    }

    public PostTransRequestVO getPostTransRequestVO() {
        return postTransRequestVO;
    }

    public void setPostTransRequestVO(PostTransRequestVO postTransRequestVO) {
        this.postTransRequestVO = postTransRequestVO;
    }

    public PostTransResponseVO getPostTransResponseVO() {
        return postTransResponseVO;
    }

    public void setPostTransResponseVO(PostTransResponseVO postTransResponseVO) {
        this.postTransResponseVO = postTransResponseVO;
    }

    public TransactionTypeEnum getTransactionTypeEnum() {
        return transactionTypeEnum;
    }

    public void setTransactionTypeEnum(TransactionTypeEnum transactionTypeEnum) {
        this.transactionTypeEnum = transactionTypeEnum;
    }

    public ClearIngTypeEnum getClearIngTypeEnum() {
        return clearIngTypeEnum;
    }

    public void setClearIngTypeEnum(ClearIngTypeEnum clearIngTypeEnum) {
        this.clearIngTypeEnum = clearIngTypeEnum;
    }

    public TransactionTypeCatogoryEnum getTransactionTypeCatogoryEnum() {
        return transactionTypeCatogoryEnum;
    }

    public void setTransactionTypeCatogoryEnum(TransactionTypeCatogoryEnum transactionTypeCatogoryEnum) {
        this.transactionTypeCatogoryEnum = transactionTypeCatogoryEnum;
    }

    public OrganizationBasicInfo getOrganizationBasicInfo() {
        return organizationBasicInfo;
    }

    public void setOrganizationBasicInfo(OrganizationBasicInfo organizationBasicInfo) {
        this.organizationBasicInfo = organizationBasicInfo;
    }

    public TechnicalParams getTechnicalParams() {
        return technicalParams;
    }

    public void setTechnicalParams(TechnicalParams technicalParams) {
        this.technicalParams = technicalParams;
    }

    public OrganizationCustomerCardInfo getOrganizationCustomerCardInfo() {
        return organizationCustomerCardInfo;
    }

    public void setOrganizationCustomerCardInfo(OrganizationCustomerCardInfo organizationCustomerCardInfo) {
        this.organizationCustomerCardInfo = organizationCustomerCardInfo;
    }

    public OrganizationCustomerAccountInfo getCustomerBasicAccount() {
        return customerBasicAccount;
    }

    public void setCustomerBasicAccount(OrganizationCustomerAccountInfo customerBasicAccount) {
        this.customerBasicAccount = customerBasicAccount;
    }

    public OrganizationCustomerAccountInfo getCustomerCryptoAccount() {
        return customerCryptoAccount;
    }

    public void setCustomerCryptoAccount(OrganizationCustomerAccountInfo customerCryptoAccount) {
        this.customerCryptoAccount = customerCryptoAccount;
    }

    public OrganizationCustomerAccountInfo getCustomerCreditAccount() {
        return customerCreditAccount;
    }

    public void setCustomerCreditAccount(OrganizationCustomerAccountInfo customerCreditAccount) {
        this.customerCreditAccount = customerCreditAccount;
    }

    public AuthFlow getAuthFlow() {
        return authFlow;
    }

    public void setAuthFlow(AuthFlow authFlow) {
        this.authFlow = authFlow;
    }

    public AuthFlowExt getAuthFlowExt() {
        return authFlowExt;
    }

    public void setAuthFlowExt(AuthFlowExt authFlowExt) {
        this.authFlowExt = authFlowExt;
    }

    public AuthFlow getOriginalAuthFlow() {
        return originalAuthFlow;
    }

    public void setOriginalAuthFlow(AuthFlow originalAuthFlow) {
        this.originalAuthFlow = originalAuthFlow;
    }

    public AuthFlowExt getOriginalAuthFlowExt() {
        return originalAuthFlowExt;
    }

    public void setOriginalAuthFlowExt(AuthFlowExt originalAuthFlowExt) {
        this.originalAuthFlowExt = originalAuthFlowExt;
    }

    public AuthFlow getFirstAuthFlow() {
        return firstAuthFlow;
    }

    public void setFirstAuthFlow(AuthFlow firstAuthFlow) {
        this.firstAuthFlow = firstAuthFlow;
    }

    public AuthFlowExt getFirstAuthFlowExt() {
        return firstAuthFlowExt;
    }

    public void setFirstAuthFlowExt(AuthFlowExt firstAuthFlowExt) {
        this.firstAuthFlowExt = firstAuthFlowExt;
    }

    public KLClearingTrans getClearingTrans() {
        return clearingTrans;
    }

    public void setClearingTrans(KLClearingTrans clearingTrans) {
        this.clearingTrans = clearingTrans;
    }

    public KLClearingTrans getOriginalClearingTrans() {
        return originalClearingTrans;
    }

    public void setOriginalClearingTrans(KLClearingTrans originalClearingTrans) {
        this.originalClearingTrans = originalClearingTrans;
    }

    public boolean isNeedToRecordCardholderExceptionClearingTrans() {
        return needToRecordCardholderExceptionClearingTrans;
    }

    public void setNeedToRecordCardholderExceptionClearingTrans(boolean needToRecordCardholderExceptionClearingTrans) {
        this.needToRecordCardholderExceptionClearingTrans = needToRecordCardholderExceptionClearingTrans;
    }

    public boolean isCardholderExceptionClearingTransRecorded() {
        return cardholderExceptionClearingTransRecorded;
    }

    public void setCardholderExceptionClearingTransRecorded(boolean cardholderExceptionClearingTransRecorded) {
        this.cardholderExceptionClearingTransRecorded = cardholderExceptionClearingTransRecorded;
    }

    public OrganizationAccountingDetail getMerchantAccountingDetail() {
        return merchantAccountingDetail;
    }

    public void setMerchantAccountingDetail(OrganizationAccountingDetail merchantAccountingDetail) {
        this.merchantAccountingDetail = merchantAccountingDetail;
    }

    public boolean isNeedToRecordMerchantExceptionClearingTrans() {
        return needToRecordMerchantExceptionClearingTrans;
    }

    public void setNeedToRecordMerchantExceptionClearingTrans(boolean needToRecordMerchantExceptionClearingTrans) {
        this.needToRecordMerchantExceptionClearingTrans = needToRecordMerchantExceptionClearingTrans;
    }

    public boolean isMerchantExceptionClearingTransRecorded() {
        return merchantExceptionClearingTransRecorded;
    }

    public void setMerchantExceptionClearingTransRecorded(boolean merchantExceptionClearingTransRecorded) {
        this.merchantExceptionClearingTransRecorded = merchantExceptionClearingTransRecorded;
    }
}
