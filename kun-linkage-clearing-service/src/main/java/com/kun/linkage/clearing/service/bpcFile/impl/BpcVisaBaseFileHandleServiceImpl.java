package com.kun.linkage.clearing.service.bpcFile.impl;

import com.alibaba.fastjson.JSON;
import com.kun.common.util.uid.UidGeneratorUtil;
import com.kun.linkage.account.facade.enums.BusinessSystemEnum;
import com.kun.linkage.clearing.dto.MarkupFeeCalculateDto;
import com.kun.linkage.clearing.enums.*;
import com.kun.linkage.clearing.enums.visa.CalculateTransactionType;
import com.kun.linkage.clearing.ext.mapper.ClearingInfoExtMapper;
import com.kun.linkage.clearing.facade.constant.ClearingProcessingStatusEnum;
import com.kun.linkage.clearing.facade.vo.kunlinkage.PostTransRequestVO;
import com.kun.linkage.clearing.facade.vo.kunlinkage.PostTransResponseVO;
import com.kun.linkage.clearing.service.ChMarkupService;
import com.kun.linkage.clearing.service.bpcFile.*;
import com.kun.linkage.clearing.service.kunlinkage.KLClearingHandler;
import com.kun.linkage.clearing.utils.BigDecimalConverter;
import com.kun.linkage.clearing.vo.req.BpcBaseFileReq;
import com.kun.linkage.clearing.vo.visa.AuthFlowVo;
import com.kun.linkage.clearing.vo.visa.CardInfoVo;
import com.kun.linkage.clearing.vo.visa.UpdateAuthFlowVo;
import com.kun.linkage.common.base.utils.SensitiveInfoUtil;
import com.kun.linkage.common.db.entity.*;
import com.kun.linkage.common.db.mapper.ClearingErrorMapper;
import com.kun.linkage.common.db.mapper.ClearingFileErrorLogMapper;
import com.kun.linkage.common.db.mapper.ExceptionInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
public class BpcVisaBaseFileHandleServiceImpl {

    @Resource
    private IVisaBase05DataService visaBase05DataService;
    @Resource
    private ClearingInfoExtMapper clearingInfoExtMapper;
    @Resource
    private ChMarkupService chMarkupService;
    @Resource
    private ClearingFileErrorLogMapper clearingFileErrorLogMapper;
    @Resource
    private ClearingErrorMapper clearingErrorMapper;
    @Resource
    private ExceptionInfoMapper exceptionInfoMapper;
    @Resource
    private IVrolDisputeService vrolDisputeService;
    @Resource
    private IVisaBase33DataService iVisaBase33DataService;
    @Resource
    private CurrencyInfoService currencyInfoService;
    @Resource
    private KLClearingHandler klClearingHandler;
    @Resource
    private IClearingInfoService clearingInfoService;
    @Value("${vcc.clearing.notifyUrl}")
    private String vccNotifyUrl;

    private static final String AUTH_FLOW_PREFIX = "kc_auth_flow_";

    private static final int BATCH_SELECT_TOTAL = 500;

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");

    private static final DateTimeFormatter FORMATTER_YYYY_MM = DateTimeFormatter.ofPattern("yyyyMM");
    private static final SimpleDateFormat SIMPLE_DATE_FORMAT_YYYY_MM = new SimpleDateFormat("yyyyMM");

    private static final DateTimeFormatter FORMATTER_YYMMDD = DateTimeFormatter.ofPattern("yyMMdd");


    private static final List<String> INIT_CLEARING_TC_CODE = Arrays.asList("05", "06", "07", "25", "26", "27");


    /**
     * 清算数据处理
     * @param bpcBaseFileReq
     */
    public void handDate(BpcBaseFileReq bpcBaseFileReq,List<String> visa05FileIds) {
        String fileName = bpcBaseFileReq.getFileName();
        log.info("开始执行清算数据处理;filaName:{}", fileName);
        //拿到文件查询分页批量查询处理中的数据
        Long total05Data = visaBase05DataService.countByFileName(fileName,visa05FileIds);
        ChannelSourceEnum channelSourceEnum = ChannelSourceEnum.BPC;
        Long maxId = 0L;

        //往上找一个月
        LocalDate localDate = LocalDate.now();
        LocalDate lastMonthDate = localDate.plusMonths(-1L);
        //查找的表名称
        String authTableName = AUTH_FLOW_PREFIX+localDate.format(FORMATTER_YYYY_MM);
        String lastAuthTableName = AUTH_FLOW_PREFIX+lastMonthDate.format(FORMATTER_YYYY_MM);

        if(total05Data > 0){
            while (true){
                //拿到05的数据,(05，06，07 且usageCode = 1)，25，26，27数据要处理,(05，06，07 且usageCode = 9) ,15,16,17,35,36,37 数据单独存和tc33放一起
                List<VisaBase05Data> visaBase05DataList = clearingInfoExtMapper.selectVisa05DataFileList(maxId,BATCH_SELECT_TOTAL,fileName,visa05FileIds);
                if (visaBase05DataList == null || visaBase05DataList.isEmpty()) {
                    log.info("visaBase05DataList is empty;fileName:{},maxId:{}", fileName, maxId);
                    break;
                }
                for (VisaBase05Data visaBase05Data : visaBase05DataList) {
                    Long fileId_05 = visaBase05Data.getId();
                    String transactionCode = visaBase05Data.getTransactionCode();
                    String usageCode0 = visaBase05Data.getUsageCode0();
                    //是否需要初始化清分数据;true 需要初始化清分数据
                    Boolean initClearingB = this.isInitClearing(transactionCode, usageCode0);

                    // 前置校验：争议数据直接处理
                    if (!initClearingB) {
                        log.info("存争议数据; transactionCode:{}, fileId:{}", transactionCode, fileId_05);
                        saveDisputeData(visaBase05Data, null);
                        continue;
                    }

                    //争议数据直接丢到Dispute 数据中
                    LocalDate clearingDate = LocalDate.now();
                    ClearingInfo clearingInfo = null;
                    //05没有匹配上授权,25没有匹配上清分数据
                    ExceptionInfo exceptionInfo = null;
                    try {

                        //初始化清算表,且没有匹配上数据直接进异常表等待人工复核
                        clearingInfo = this.initClearingInfo(visaBase05Data,channelSourceEnum,clearingDate,fileName);
                        //如果没有找到cardId 需要进入到异常表
                        if(StringUtils.isBlank(clearingInfo.getProcessorCardId())){
                            log.error("没有找到cardId;文件id:{},clearingDate:{}",fileId_05,clearingDate);
                            //清分文件异常数据；1.根据卡号没有找到cardId
                            ClearingError clearingError = this.initClearingError(clearingInfo);
                            clearingInfo.setClearingStatus(ClearingStatusEnum.FAILURE.getValue());
                            //新增清算数据，
                            clearingInfoService.save(clearingInfo);
                            Long clearingId = clearingInfo.getClearingId();
                            clearingError.setClearingId(clearingId);
                            clearingError.setClearingDate(clearingDate);
                            clearingErrorMapper.insert(clearingError);
                            continue;
                        }

                        AuthFlowVo authFlowVo = null;
                        //需要更新的授权流水
                        UpdateAuthFlowVo updateAuthFlowVo = null;
                        ClearingInfo updateOriginalClearing = null;

                        //需要通知KL/VCC的请求参数
                        Boolean isNotify = false;
                        ClearingTransCodeEnum clearingTransCodeEnum = ClearingTransCodeEnum.fromCode(transactionCode);
                        if (clearingTransCodeEnum == null) {
                            log.error("未知交易码;transactionCode={},fileId={}", transactionCode, visaBase05Data.getId());
                            continue;
                        }
                        String acqArn = clearingInfo.getAcqArn();
                        //用来匹配授权交易的条件如下
                        String processorCardId = clearingInfo.getProcessorCardId();
                        String authCode = clearingInfo.getAuthCode();
                        //这个时间是yyyyMMdd
                        String transactionDate = clearingInfo.getTransactionDate();
                        String cardAcceptorId = clearingInfo.getCardAcceptorId();
                        String acquiringId = clearingInfo.getAcquiringId();

                        Integer reversalFlag = YesFlagEnum.NO.getNumValue();

                        switch (clearingTransCodeEnum) {
                            case SALES:
                            case WITHDRAWAL:
                                //正常处理；先根据卡号拿到cardId;然后再匹配授权流水表数据
                                //匹配授权流水
                                authFlowVo = this.selectAuthFlow(processorCardId, authCode, transactionDate.substring(2), cardAcceptorId, acquiringId, authTableName, lastAuthTableName,transactionCode);
                                if(null != authFlowVo){
                                    //补充清算流水数据,更新授权相关数据到清算流水数据中,
                                    clearingInfo = this.supplementClearingInfo(clearingInfo, authFlowVo);
                                    //对比授权流水，false:正常对比无异常;true:有异常(这里的异常是指,交易币种不一致,账单币种不一致),这里面顺手处理下差异金额相关数据
                                    Boolean b = this.contrastClearingInfoAndAuthFlow(clearingInfo, authFlowVo);
                                    if(!b){
                                        //无异常,需要通知KL/VCC
                                        isNotify = true;
                                        //处理需要更新的数据
                                        updateAuthFlowVo = this.handUpdateAuthFlow(clearingInfo,authFlowVo);
                                    }else {
                                        //有异常,需要进异常表
                                        log.warn("交易币种不一致,持卡人账单币种不一致;cleraFileId:{}:authFlowId{}",fileId_05,authFlowVo.getId());
                                        clearingInfo.setErrorFlag(YesFlagEnum.YES.getNumValue());
                                        clearingInfo.setErrorReason("交易币种不一致,持卡人账单币种和清分币种,持卡人币种不一致");
                                        exceptionInfo = this.initExceptionInfo(clearingInfo);
                                    }
                                }else {
                                    log.info("没有匹配上授权交易;authCode:{},cleraFileId:{}", authCode, fileId_05);
                                    //需要通知KL/VCC；需要设置差异金额；记差异金额以及打差异标记
                                    clearingInfo.setDifferenceFlag(YesFlagEnum.YES.getNumValue());
                                    clearingInfo.setTransactionAmountOffset(clearingInfo.getClearAmount());
                                    clearingInfo.setCardholderBillingAmountOffset(clearingInfo.getCardholderAmount());
                                    clearingInfo.setCardholderMarkupBillingAmountOffset(clearingInfo.getCardholderBillingAmountWithMarkup());
                                    isNotify = true;
                                    //强制发送授权流水
                                    clearingInfo.setErrorFlag(YesFlagEnum.YES.getNumValue());
                                    clearingInfo.setErrorReason("没有匹配上授权交易");
                                }
                                break;
                            case REFUND:
                                authFlowVo = this.selectAuthFlow(processorCardId, authCode, transactionDate.substring(2), cardAcceptorId, acquiringId, authTableName, lastAuthTableName,transactionCode);

                                //不管能不能匹配上都需要,需要通知KL/VCC，匹配上没有差异金额,匹配不上差异金额就是退货金额
                                isNotify = true;
                                //如果能匹配上直接修改授权流表水数据
                                if(null != authFlowVo){
                                    //补充清算流水数据,更新授权相关数据到清算流水数据中
                                    clearingInfo = this.supplementClearingInfo(clearingInfo, authFlowVo);
                                    //对比授权流水，false:正常对比无异常;true:有异常(这里的异常是指,交易币种不一致,账单币种不一致),这里面顺手处理下差异金额相关数据
                                    Boolean b = this.contrastClearingInfoAndAuthFlow(clearingInfo, authFlowVo);
                                    if(!b){
                                        //处理需要更新的授权流水
                                        updateAuthFlowVo = this.handUpdateAuthFlow(clearingInfo,authFlowVo);
                                    }else {
                                        //发送授权通知,退货不需要计算markup，原来的是多少，强制清算多少
                                        clearingInfo.setErrorFlag(YesFlagEnum.YES.getNumValue());
                                        clearingInfo.setErrorReason("退货交易币种不一致,持卡人账单币种和清分币种,持卡人币种不一致");
                                        //匹配不上记录差异金额
                                        clearingInfo.setDifferenceFlag(YesFlagEnum.YES.getNumValue());
                                        clearingInfo.setTransactionAmountOffset(clearingInfo.getClearAmount());
                                        BigDecimal cardholderAmount = clearingInfo.getCardholderAmount();
                                        clearingInfo.setCardholderBillingAmountOffset(cardholderAmount);
                                        BigDecimal cardholderMarkupBillingAmountOffset = cardholderAmount.multiply(BigDecimal.ONE.add(clearingInfo.getMarkupRate()));
                                        clearingInfo.setCardholderMarkupBillingAmountOffset(cardholderMarkupBillingAmountOffset);
                                    }
                                }else {
                                    //发送授权通知,退货不需要计算markup，原来的是多少，强制清算多少
                                    clearingInfo.setErrorFlag(YesFlagEnum.YES.getNumValue());
                                    clearingInfo.setErrorReason("退货交易匹配不上授权流水");
                                    //匹配不上记录差异金额
                                    clearingInfo.setDifferenceFlag(YesFlagEnum.YES.getNumValue());
                                    clearingInfo.setTransactionAmountOffset(clearingInfo.getClearAmount());
                                    BigDecimal cardholderAmount = clearingInfo.getCardholderAmount();
                                    clearingInfo.setCardholderBillingAmountOffset(cardholderAmount);
                                    BigDecimal cardholderMarkupBillingAmountOffset = cardholderAmount.multiply(BigDecimal.ONE.add(clearingInfo.getMarkupRate()));
                                    clearingInfo.setCardholderMarkupBillingAmountOffset(cardholderMarkupBillingAmountOffset);
                                }
                                break;
                            //用ARN 匹配清算数据
                            case SALES_VOID:
                            case REFUND_VOID:
                            case WITHDRAWAL_VOID:
                                //根据arn 查询近1个月的原清算数据;
                                LocalDate selectMonths = clearingDate.plusMonths(-1);
                                ClearingInfo originalClearingInfo = clearingInfoService.selectOriginalClearingInfoByArn(acqArn, clearingInfo.getTransCode(),selectMonths,clearingDate);

                                if(null == originalClearingInfo){
                                    //进差异表,
                                    clearingInfo.setErrorFlag(YesFlagEnum.YES.getNumValue());
                                    clearingInfo.setErrorReason("撤销交易arm匹配不上清分流水数据");
                                    exceptionInfo = this.initExceptionInfo(clearingInfo);
                                }else {
                                    clearingInfo.setOriginalClearingNo(originalClearingInfo.getClearingNo());
                                    clearingInfo.setOriginalClearingId(originalClearingInfo.getClearingId());
                                    clearingInfo.setAuthId(originalClearingInfo.getAuthId());
                                    clearingInfo.setTransId(originalClearingInfo.getTransId());
                                    reversalFlag = YesFlagEnum.YES.getNumValue();
                                    //结算金额
                                    BigDecimal clearAmount = clearingInfo.getClearAmount();
                                    //匹配上的源剩余清算金额
                                    BigDecimal originalRemainingClearAmount = originalClearingInfo.getRemainingClearAmount();
                                    // 首先看清分的金额是否大于源剩余清算金额 大于进异常表;
                                    if(clearAmount.compareTo(originalRemainingClearAmount) > 0){
                                        log.warn("撤销交易清算金额大于源剩余清算金额:fileId:{}",fileId_05);
                                        clearingInfo.setErrorReason("撤销交易清算金额大于源剩余清算金额");
                                        clearingInfo.setErrorFlag(YesFlagEnum.YES.getNumValue());
                                        exceptionInfo = this.initExceptionInfo(clearingInfo);
                                    }else {
                                        //需要通知KL/VCC
                                        isNotify = true;
                                        updateOriginalClearing = new ClearingInfo();
                                        //需要更新源清分流水数据,只改清分剩余金额
                                        updateOriginalClearing.setReversalFlag(YesFlagEnum.YES.getNumValue());
                                        updateOriginalClearing.setClearingId(originalClearingInfo.getClearingId());
                                        updateOriginalClearing.setClearingDate(originalClearingInfo.getClearingDate());
                                        updateOriginalClearing.setRemainingClearAmount(clearAmount.subtract(originalRemainingClearAmount));

                                        //更新差异标志和差异金额
                                        clearingInfo.setDifferenceFlag(YesFlagEnum.YES.getNumValue());
                                        clearingInfo.setTransactionAmountOffset(clearingInfo.getClearAmount());
                                        clearingInfo.setCardholderBillingAmountOffset(clearingInfo.getCardholderAmount());
                                        clearingInfo.setCardholderMarkupBillingAmountOffset(clearingInfo.getCardholderBillingAmountWithMarkup());

                                        //这个就是最顶级的授权流水id
                                        Long originalAuthId = originalClearingInfo.getAuthId();
                                        String authDate = originalClearingInfo.getAuthDate();

                                        //这个里面有是否匹配授权交易;没有直接就不用去更新数据了;
                                        // 1.0.2 版本撤销交易不在去更新授权流水表中的剩余金额
                                        /*if(null != originalAuthId && null != authDate){
                                            //查询，如果没有的话不用去更新
                                            String tableName = AUTH_FLOW_PREFIX+authDate.substring(0,6);
                                            AuthFlowVo originalAuthFlowVo = clearingInfoExtMapper.selectVccCallbackInfoByAuthFlowId(originalAuthId, tableName);
                                            if(null != originalAuthFlowVo){
                                                clearingInfo.setAuthId(originalClearingInfo.getAuthId());
                                                clearingInfo.setTransId(originalClearingInfo.getTransId());
                                                updateAuthFlowVo = this.reversalUpdateAuthFlow(clearingInfo,originalClearingInfo,originalAuthFlowVo,tableName);
                                            }
                                        }*/
                                    }
                                }

                                break;
                            case DISPUTE_15:
                            case DISPUTE_16:
                            case DISPUTE_17:
                            case DISPUTE_35:
                            case DISPUTE_36:
                            case DISPUTE_37:
                                saveDisputeData(visaBase05Data,null);
                                continue;
                            default:
                                log.error("清算数据处理未知的transCode:{};fileId_05:{}", transactionCode,fileId_05);
                                break;
                        }

                        //新增清算数据,更新通知的标志
                        clearingInfo.setNotifyFlag(isNotify ? YesFlagEnum.YES.getNumValue() : YesFlagEnum.NO.getNumValue());
                        clearingInfoService.save(clearingInfo);
                        Long clearingId = clearingInfo.getClearingId();

                        //通知KL/VCC
                        if(isNotify){
                            log.info("通知KL/VCC");
                            PostTransRequestVO postTransRequestVO = initPostTransRequestVO(clearingInfo, authFlowVo,reversalFlag);
                            Boolean b = authNotice(postTransRequestVO, clearingInfo.getSystem());
                            //更新清分通知结果
                            clearingInfoService.updateNotifyResultsById(clearingId,clearingDate,b ? 1:2);
                        }

                        //新增清算差异数据
                        if(null != exceptionInfo){
                            exceptionInfo.setClearingId(clearingId);
                            exceptionInfo.setClearingDate(clearingDate);
                            exceptionInfo.setTransactionType(clearingInfo.getTransactionType());
                            exceptionInfo.setClearAmount(clearingInfo.getClearAmount());
                            exceptionInfo.setProcessStatus(ClearingProcessingStatusEnum.PENDING.getValue());
                            exceptionInfo.setErrorReason(clearingInfo.getErrorReason());
                            exceptionInfoMapper.insert(exceptionInfo);
                        }
                        //更新授权流水表数据
                        if(null != updateAuthFlowVo ){
                            this.updateAuthFlow(updateAuthFlowVo);
                        }
                        //更新源授权流水表的剩余结算金额
                        if(null != updateOriginalClearing){
                            clearingInfoService.updateRemainingClearAmountById(updateOriginalClearing.getRemainingClearAmount(),
                                    updateOriginalClearing.getClearingId(),updateOriginalClearing.getClearingDate());
                        }


                    }catch (Exception e){
                        log.error("处理05Base清算文件异常;fielId:{},fileName:{},channelSourceEnum:{};异常信息:",fileId_05,fileName,channelSourceEnum.getDescription(), e);
                        ClearingFileErrorLog clearingFileErrorLog = new ClearingFileErrorLog();
                        clearingFileErrorLog.setClearingFileId(fileId_05);
                        clearingFileErrorLog.setClearingDate(clearingDate);
                        clearingFileErrorLog.setFileName(fileName);
                        clearingFileErrorLog.setChannelSource(channelSourceEnum.getCode());
                        clearingFileErrorLog.setCreateTime(new Date());
                        clearingFileErrorLog.setUpdateTime(new Date());
                        clearingFileErrorLogMapper.insert(clearingFileErrorLog);
                    }
                }

                // 更新maxId为当前批次的最大ID，避免重复查询
                int size = visaBase05DataList.size()-1;
                maxId = visaBase05DataList.get(size).getId();

            }

        }else {
            log.info("当天没有要处理的文件fileName:{}",fileName);
        }
    }

    /**
     * 撤销交易处理需要更新授权流水数据
     * @param clearingInfo
     * @param originalClearingInfo
     * @param originalAuthFlowVo
     * @param tableName
     * @return
     */
    private UpdateAuthFlowVo reversalUpdateAuthFlow(ClearingInfo clearingInfo,ClearingInfo originalClearingInfo, AuthFlowVo originalAuthFlowVo,
                                                    String tableName) {

        UpdateAuthFlowVo updateAuthFlowVo = new UpdateAuthFlowVo();
        BigDecimal clearAmount = clearingInfo.getClearAmount();
        Long originalAuthId = clearingInfo.getAuthId();
        //持卡人币种金额不含markup
        BigDecimal cardholderAmount = clearingInfo.getCardholderAmount();
        //持卡人账单币种含markup
        BigDecimal cardholderBillingAmountWithMarkup = clearingInfo.getCardholderBillingAmountWithMarkup();
        //设置更新的值
        updateAuthFlowVo.setId(originalAuthId);
        updateAuthFlowVo.setTableName(tableName);
        updateAuthFlowVo.setClearStatus(YesFlagEnum.YES.getNumValue());
        //需要修改的金额 当前金额+授权流水中的剩余进
        BigDecimal updateRemainAuthAmt = clearAmount.add(originalAuthFlowVo.getRemainAuthAmt());
        BigDecimal updateRemainBillAmt = cardholderAmount.add(originalAuthFlowVo.getRemainBillAmt());
        BigDecimal updateRemainBillAmtWithMarkup = cardholderBillingAmountWithMarkup.add(originalAuthFlowVo.getRemainBillAmtWithMarkup());
        BigDecimal updateRemainFrozenAmt = cardholderBillingAmountWithMarkup.add(originalAuthFlowVo.getRemainFrozenAmt());

        //自己存储的当前剩余金额
        BigDecimal originalAuthRemainAuthAmt = originalClearingInfo.getAuthRemainAuthAmt();
        BigDecimal originalAuthRemainBillAmt = originalClearingInfo.getAuthRemainBillAmt();
        BigDecimal originalCardholderMarkupAmount = originalClearingInfo.getAuthRemainBillAmtWithMarkup();
        BigDecimal authRemainFrozenAmt = originalClearingInfo.getAuthRemainFrozenAmt();

        //如果当前的清算金额大于剩余可用金额只能更新为源剩余可用金额
        if(updateRemainAuthAmt.compareTo(originalAuthRemainAuthAmt) > 0){
            updateRemainAuthAmt = originalAuthRemainAuthAmt;
        }
        //如果当前的持卡人金额大于剩余可用的持卡人金额更新为原剩余可用持卡人金额
        if(updateRemainBillAmt.compareTo(originalAuthRemainBillAmt) > 0){
            updateRemainBillAmt = originalAuthRemainBillAmt;
        }
        //更新的持卡人含markUp金额
        if(updateRemainBillAmtWithMarkup.compareTo(originalCardholderMarkupAmount) > 0){
            updateRemainBillAmtWithMarkup = originalCardholderMarkupAmount;
        }
        //剩余冻结金额
        if(updateRemainFrozenAmt.compareTo(authRemainFrozenAmt) > 0){
            updateRemainFrozenAmt = authRemainFrozenAmt;
        }
        //交易币种金额汇总 不含markup
        updateAuthFlowVo.setRemainAuthAmt(updateRemainAuthAmt);
        //剩余账单金额 不含markup
        updateAuthFlowVo.setRemainBillAmt(updateRemainBillAmt);
        //剩余账单总金额（含markup）
        updateAuthFlowVo.setRemainBillAmtWithMarkup(updateRemainBillAmtWithMarkup);
        //剩余冻结金额(含markUp)
        updateAuthFlowVo.setRemainFrozenAmt(updateRemainFrozenAmt);
        updateAuthFlowVo.setUpdateTime(clearingInfo.getUpdateTime());

        return updateAuthFlowVo;
    }


    /**
     * 通知KL/VCC
     * @param postTransRequestVO 通知数据
     * @return 成功或者失败
     */
    private Boolean authNotice(PostTransRequestVO postTransRequestVO,String system){
        log.info("通知KL/VCC system:{}; postTransRequestVO:{}", system, JSON.toJSONString(postTransRequestVO));
        if(BusinessSystemEnum.KL.getValue().equalsIgnoreCase(system)){
            log.info("通知KL");
            PostTransResponseVO postTransResponseVO = klClearingHandler.handleClearing(postTransRequestVO);
            log.info("通知KL返回数据:{}", JSON.toJSONString(postTransResponseVO));
            if(null != postTransResponseVO  && postTransResponseVO.getCode().equalsIgnoreCase("0000")){
                return true;
            }
        }else if(BusinessSystemEnum.VCC.getValue().equalsIgnoreCase(system)){
            log.info("通知VCC");
            return this.notifyVcc(postTransRequestVO);
        }else {
            log.error("未知的数据系统");
        }
        return false;
    }

    /**
     * 调用VCC通知
     * @param postTransRequestVO 请求数据
     * @return
     */
    private Boolean notifyVcc(PostTransRequestVO postTransRequestVO){
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            String requestJson = JSON.toJSONString(postTransRequestVO);
            HttpEntity<String> entity = new HttpEntity<>(requestJson, headers);

            // 记录接口调用开始时间
            long startTime = System.currentTimeMillis();
            ResponseEntity<PostTransResponseVO> responseEntity = restTemplate.exchange(
                    vccNotifyUrl,
                    HttpMethod.POST,
                    entity,
                    PostTransResponseVO.class
            );

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            PostTransResponseVO postTransResponseVO = responseEntity.getBody();
            log.info("VCC接口调用时间: {} ms;通知VCC返回数据:{}", duration,JSON.toJSONString(postTransResponseVO));
            // 判断返回的 code 是否为 "0000"
            if (null != postTransResponseVO && postTransResponseVO.getCode().equalsIgnoreCase("0000")) {
                return true;
            }

        }catch (Exception e){
            log.error("通知VCC失败，异常信息：{}", e.getMessage(), e);
        }
        return false;

    }


    /**
     * 初始化通知数据
     * @param clearingInfo
     * @param authFlowVo
     * @return
     */
    public  PostTransRequestVO initPostTransRequestVO(ClearingInfo clearingInfo,AuthFlowVo authFlowVo,Integer reversalFlag){
        PostTransRequestVO postTransRequestVO = new PostTransRequestVO();
        postTransRequestVO.setRequestId(UUID.randomUUID().toString());
        postTransRequestVO.setProcessor(clearingInfo.getChannelSource());
        postTransRequestVO.setProcessorRequestId(clearingInfo.getProcessorRequestId());
        postTransRequestVO.setProcessorTransId(clearingInfo.getTransId());
        postTransRequestVO.setClearingType(clearingInfo.getClearingType());
        postTransRequestVO.setMerchantNo(clearingInfo.getCustomerMerId());
        if(null != authFlowVo){
            postTransRequestVO.setOriginalProcessorTransId(authFlowVo.getOriginalTransId());
            postTransRequestVO.setConversionRateCardholderBilling(StringUtils.isNotBlank(authFlowVo.getConversionRateCardholderBilling()) ?
                    BigDecimalConverter.convert(authFlowVo.getConversionRateCardholderBilling()) : null);
        }
        postTransRequestVO.setSystemsTraceAuditNumber(clearingInfo.getTraceAuditNo());
        postTransRequestVO.setGatewayCardId(clearingInfo.getKcardId());
        postTransRequestVO.setProcessorCardId(clearingInfo.getProcessorCardId());
        //卡号脱敏,前六后4
        String cardNo = clearingInfo.getCardNo();
        if(StringUtils.isNotBlank(cardNo)){
            String decryptCardNo = SensitiveInfoUtil.innerDecrypt(cardNo);
            postTransRequestVO.setMaskedCardNo(SensitiveInfoUtil.mask(decryptCardNo, 6, 4, '*'));
        }
        postTransRequestVO.setTransType(clearingInfo.getTransactionType());
        postTransRequestVO.setTransCurrency(clearingInfo.getTransactionCurrencyCode());
        postTransRequestVO.setTransAmount(clearingInfo.getAuthAmount());
        postTransRequestVO.setTransAmountOffset(clearingInfo.getTransactionAmountOffset());
        postTransRequestVO.setCardholderBillingCurrency(clearingInfo.getCardholderCurrencyCode());
        postTransRequestVO.setCardholderBillingAmount(clearingInfo.getCardholderAmount());
        postTransRequestVO.setCardholderBillingAmountOffset(clearingInfo.getCardholderBillingAmountOffset());
        postTransRequestVO.setCardholderMarkupBillingAmount(clearingInfo.getCardholderBillingAmountWithMarkup());
        postTransRequestVO.setCardholderMarkupBillingAmountOffset(clearingInfo.getCardholderMarkupBillingAmountOffset());
        postTransRequestVO.setMarkupRate(clearingInfo.getMarkupRate());
        postTransRequestVO.setMarkupAmount(clearingInfo.getCardholderMarkupAmount());
        postTransRequestVO.setPosEntryMode(clearingInfo.getPosEntryModeTcr0());
        postTransRequestVO.setTransactionLocalDatetime(clearingInfo.getTransactionDatetime());
        postTransRequestVO.setApproveCode(clearingInfo.getAuthCode());
        postTransRequestVO.setAcquireReferenceNo(clearingInfo.getReferenceNo());
        postTransRequestVO.setCardAcceptorName(clearingInfo.getCardAcceptorName());
        postTransRequestVO.setCardAcceptorId(clearingInfo.getCardAcceptorId());
        postTransRequestVO.setCardAcceptorTid(clearingInfo.getCardAcceptorTid());
        postTransRequestVO.setCardAcceptorCountryCode(clearingInfo.getCardAcceptorCountryCode());
        postTransRequestVO.setCardAcceptorPostalCode(clearingInfo.getMerchantPostalCode());
        postTransRequestVO.setCardAcceptorCity(clearingInfo.getMerchantCity());
        postTransRequestVO.setMcc(clearingInfo.getCardAcceptorMcc());
        postTransRequestVO.setProcessorExt1(clearingInfo.getFeTransactionNumber());
        postTransRequestVO.setOriginalProcessorRequestId(clearingInfo.getOriginalProcessorRequestId());
        postTransRequestVO.setTransDate(clearingInfo.getTransactionDate());
        postTransRequestVO.setClearingDate(clearingInfo.getClearingDate().format(formatter));
        postTransRequestVO.setClearAmount(clearingInfo.getClearAmount());
        postTransRequestVO.setFeeInterchangeSign(clearingInfo.getIntechangeFeeSign());
        postTransRequestVO.setFeeInterchangeAmount(clearingInfo.getIntechangeFeeAmt());
        postTransRequestVO.setReversalFlag(reversalFlag.toString());
        postTransRequestVO.setArn(clearingInfo.getAcqArn());
        postTransRequestVO.setUsageCode(clearingInfo.getUsageCod());
        postTransRequestVO.setReasonCode(clearingInfo.getReasonCode());
        postTransRequestVO.setCardSchemaProductId(clearingInfo.getCardSchemaProductId());
        postTransRequestVO.setResponseMessage(clearingInfo.getResponseMessage());
        postTransRequestVO.setResponseCode(clearingInfo.getAuthorizationResponseCodeTcr5());
        postTransRequestVO.setGatewayClearingId(String.valueOf(clearingInfo.getClearingId()));

        postTransRequestVO.setTransCurrencyExponent(clearingInfo.getTransactionCurrencyPrecision());
        postTransRequestVO.setCardholderCurrencyExponent(clearingInfo.getCardholderCurrencyPrecision());
        postTransRequestVO.setCardProductCode(clearingInfo.getCardProductCode());

        if(null != clearingInfo.getOriginalClearingId()){
            postTransRequestVO.setOriginalGatewayClearingId(String.valueOf(clearingInfo.getOriginalClearingId()));
        }

        return postTransRequestVO;
    }

    /**
     * 更新授权流水
     * @param updateAuthFlowVo 授权流水数据
     * @return
     */
    private Boolean updateAuthFlow(UpdateAuthFlowVo updateAuthFlowVo){
        String tableName = updateAuthFlowVo.getTableName();
        Integer total = clearingInfoExtMapper.updateAuthFlow(tableName,updateAuthFlowVo);
        return total > 0;
    }

    /**
     * tc05，tc07 对比清算数据和授权流水数据；是否有异常，true:有异常;false:无异常
     * @param clearingInfo 清算数据
     * @param authFlowVo 授权流水
     * @return false:正常对比无差异;true:有差异
     */
    private Boolean contrastClearingInfoAndAuthFlow(ClearingInfo clearingInfo, AuthFlowVo authFlowVo) {
        String transId = authFlowVo.getTransId();
        Boolean contrastFlag = false;

        //授权流水交易币种 这个是三位的数字
        String auth_currencyCodeTransaction = authFlowVo.getCurrencyCodeTransaction();
        //授权流水持卡人币种
        String auth_currencyCodeCardholderBilling = authFlowVo.getCurrencyCodeCardholderBilling();
        //剩余可用金额不含markUp 直接用正常肯定会等于交易金额
        BigDecimal auth_remainAuthAmt = authFlowVo.getRemainAuthAmt();
        //剩余可用账单金额不含markup
        BigDecimal auth_remainBillAmt = authFlowVo.getRemainBillAmt();
        //剩余可用账单金额含markup
        BigDecimal auth_remainBillAmtWithMarkup = authFlowVo.getRemainBillAmtWithMarkup();


        BigDecimal cl_clearAmount = clearingInfo.getClearAmount();
        String cl_transactionCurrencyCode = clearingInfo.getTransactionCurrencyNo();
        BigDecimal cl_cardholderAmount = clearingInfo.getCardholderAmount();
        String cl_cardholderCurrencyNo = clearingInfo.getCardholderCurrencyNo();
        BigDecimal cl_cardholderBillingAmountWithMarkup = clearingInfo.getCardholderBillingAmountWithMarkup();

        //先对比交易币种
        if(!auth_currencyCodeTransaction.equalsIgnoreCase(cl_transactionCurrencyCode)){
            log.info("授权流水交易币种和清算文件交易币种不一致;交易币种:{},清算文件交易币种:{},transId:{}",auth_currencyCodeTransaction,cl_transactionCurrencyCode,transId);
            contrastFlag = true;
            return contrastFlag;
        }

        //对比持卡人币种
        if(!auth_currencyCodeCardholderBilling.equalsIgnoreCase(cl_cardholderCurrencyNo)){
            log.info("授权流水持卡人币种和清算文件交易持卡人币种不一致;交易币种:{},清算文件持卡人币种:{},transId:{}",auth_currencyCodeCardholderBilling,cl_cardholderCurrencyNo,transId);
            contrastFlag = true;
            return contrastFlag;
        }

        //对比交易金额,交易金额有差额;记录交易金额差额,打上差异标记
        if(cl_clearAmount.compareTo(auth_remainAuthAmt) != 0){
            log.info("授权流水交易金额和清算文件交易金额不一致;清算文件交易金额:{},授权剩余可用金额:{},transId:{}",cl_clearAmount,auth_remainAuthAmt,transId);
            // 交易金额不一致需要计算差异的交易金额;清算 - 授权
            BigDecimal differenceAmount = cl_clearAmount.subtract(auth_remainAuthAmt);
            //设置交易的差异金额
            clearingInfo.setDifferenceFlag(YesFlagEnum.YES.getNumValue());
            clearingInfo.setTransactionAmountOffset(differenceAmount);
        }

        //对比持卡人账单金额, 差异金额 = 清分账单金额 - 授权账单剩余金额;持卡人账单金额不一致的时候算markup金额
        if(cl_cardholderAmount.compareTo(auth_remainBillAmt) != 0){
            log.info("授权流水持卡人交易金额和清算文件交易金额不一致;持卡人账单不含markup剩余金额:{},清算文件交易金额:{},transId:{}",auth_remainBillAmt,cl_cardholderAmount,transId);
            //持卡人不含markUp差异金额 清算 - 授权
            BigDecimal differenceAmount = cl_cardholderAmount.subtract(auth_remainBillAmt);
            clearingInfo.setDifferenceFlag(YesFlagEnum.YES.getNumValue());
            clearingInfo.setCardholderBillingAmountOffset(differenceAmount);
            //计算持卡人含markup金额
            log.info("清分交易币种和清分持卡人币种不一致;清分交易币种:{},清分持卡人币种:{},transId:{},清分含markup账单金额:{},授权持卡人账单含markup剩余金额:{}",cl_transactionCurrencyCode,
                    cl_cardholderCurrencyNo,transId,cl_cardholderBillingAmountWithMarkup,auth_remainBillAmtWithMarkup);
            //markup清算 - markup授权
            BigDecimal markupDifferenceAmount = cl_cardholderBillingAmountWithMarkup.subtract(auth_remainBillAmtWithMarkup);
            clearingInfo.setCardholderMarkupBillingAmountOffset(markupDifferenceAmount);
        }

        //如果交易币种和持卡人交易币种不一致需要算markUp金额
        if(cl_cardholderBillingAmountWithMarkup.compareTo(auth_remainBillAmtWithMarkup) != 0){
            clearingInfo.setDifferenceFlag(YesFlagEnum.YES.getNumValue());
        }
        return contrastFlag;
    }


    /**
     * tc 05,06,07 处理需要更新的数据,这里只有在持卡人币种一致的情况才处理
     * @param clearingInfo 清算数据
     * @param authFlowVo 匹配到的授权流水数据
     * @return 需要更新的数据
     */
    private UpdateAuthFlowVo handUpdateAuthFlow(ClearingInfo clearingInfo, AuthFlowVo authFlowVo) {
        //剩余冻结金额
        BigDecimal originalRemainFrozenAmt = authFlowVo.getRemainFrozenAmt();
        //剩余账单金额
        BigDecimal remainBillAmt = authFlowVo.getRemainBillAmt();
        //剩余账单金额含markup
        BigDecimal remainBillAmtWithMarkup = authFlowVo.getRemainBillAmtWithMarkup();
        //剩余金额
        BigDecimal remainAuthAmt = authFlowVo.getRemainAuthAmt();

        Long originalAuthFlowId = authFlowVo.getId();

        String tableName = authFlowVo.getTableName();

        //如果没有原交易或者原交易是退货交易就返回不需要再往上找了;这个不需要了,直接拿到的就是顶级数据
        //一直往上查原交易数据
       /* String originalTransId = authFlowVo.getOriginalTransId();
        Date originalFinishTime = authFlowVo.getOriginalFinishTime();
         BigDecimal originalMarkupRate = authFlowVo.getMarkupRate();
        if(StringUtils.isNotBlank(authFlowVo.getOriginalTransId())  && ! BpcTransTypeEnum.REFUND.getCode().equals(authFlowVo.getTransType())){
            String format = SIMPLE_DATE_FORMAT_YYYY_MM.format(originalFinishTime);
            tableName = AUTH_FLOW_PREFIX + format;
            //最上级的交易数据
            AuthFlowVo originalAuthFlowVo = this.selectOriginalAuthFlowVo(originalTransId,tableName);

            clearingInfo.setOriginalProcessorRequestId(originalAuthFlowVo.getOriginalProcessorRequestId());
            //设置要更新的数据
            originalAuthFlowId = originalAuthFlowVo.getId();
            tableName = originalAuthFlowVo.getTableName();
            originalRemainFrozenAmt = originalAuthFlowVo.getRemainFrozenAmt();
            remainBillAmt = originalAuthFlowVo.getRemainBillAmt();
            originalMarkupRate = originalAuthFlowVo.getMarkupRate();
            remainBillAmtWithMarkup = originalAuthFlowVo.getRemainBillAmtWithMarkup();
            remainAuthAmt = originalAuthFlowVo.getRemainAuthAmt();
        }*/

        //持卡人账单金额
        BigDecimal cl_cardholderAmount = clearingInfo.getCardholderAmount();
        //含markUp 持卡人金额 = 持卡人账单金额 * (1+markup利率)
        BigDecimal cl_markupCardholderAmount = clearingInfo.getCardholderBillingAmountWithMarkup();

        //计算markUp后的金额如果剩余冻结金额是负数，只能更新剩余冻结金额为0;
        BigDecimal updateRemainFrozenAmt = originalRemainFrozenAmt.subtract(cl_markupCardholderAmount);
        if(updateRemainFrozenAmt.compareTo(BigDecimal.ZERO) < 0){
            //冻结金额
            updateRemainFrozenAmt = BigDecimal.ZERO;
        }

        //剩余账单金额不含markUp,如果是负数只能是0
        BigDecimal updateRemainBillAmt = remainBillAmt.subtract(cl_cardholderAmount);
        if(updateRemainBillAmt.compareTo(BigDecimal.ZERO) < 0){
            updateRemainBillAmt = BigDecimal.ZERO;
        }
        //剩余含markup的账单金额 = 第一笔交易的 含markup剩余账单 - 当前清分的含markup 账单金额
        BigDecimal updateRemainBillAmtWithMarkup = remainBillAmtWithMarkup.subtract(cl_markupCardholderAmount);
        if(updateRemainBillAmtWithMarkup.compareTo(BigDecimal.ZERO) < 0){
            updateRemainBillAmtWithMarkup = BigDecimal.ZERO;
        }
        //剩余金额 = 第一笔交易剩余金额 - 当前清分的金额
        BigDecimal updateRemainAuthAmt = remainAuthAmt.subtract(clearingInfo.getClearAmount());
        if(updateRemainAuthAmt.compareTo(BigDecimal.ZERO) < 0){
            updateRemainAuthAmt = BigDecimal.ZERO;
        }

        //设置更新的值
        UpdateAuthFlowVo updateAuthFlowVo = new UpdateAuthFlowVo();
        updateAuthFlowVo.setId(originalAuthFlowId);
        updateAuthFlowVo.setTableName(tableName);
        updateAuthFlowVo.setClearStatus(YesFlagEnum.YES.getNumValue());

        updateAuthFlowVo.setRemainBillAmt(updateRemainBillAmt);
        updateAuthFlowVo.setRemainBillAmtWithMarkup(updateRemainBillAmtWithMarkup);
        updateAuthFlowVo.setRemainFrozenAmt(updateRemainFrozenAmt);
        updateAuthFlowVo.setRemainAuthAmt(updateRemainAuthAmt);
        updateAuthFlowVo.setUpdateTime(clearingInfo.getUpdateTime());
        return updateAuthFlowVo;

    }

    /**
     * 获取获取原交易数据
     * @param originalTransId 原交易交易id
     * @param tableName 表名称
     * @return
     */
    private AuthFlowVo selectOriginalAuthFlowVo(String originalTransId ,String tableName) {
        AuthFlowVo authFlowVo = null;
        String originalProcessorRequestId = null;
        // 循环查找原交易
        while (true) {
            authFlowVo = clearingInfoExtMapper.selectVccCallbackInfoByTransId(originalTransId, tableName);
            //拿到第一次原交易的请求流水id
            if(null == originalProcessorRequestId){
                originalProcessorRequestId = authFlowVo.getOriginalProcessorRequestId();
            }
            if (authFlowVo == null || StringUtils.isBlank(authFlowVo.getOriginalTransId())) {
                break;
            }
            // 如果有原交易ID，继续查找
            originalTransId = authFlowVo.getOriginalTransId();
            Date originalFinishTime = authFlowVo.getOriginalFinishTime();
            String format = SIMPLE_DATE_FORMAT_YYYY_MM.format(originalFinishTime);
            tableName = AUTH_FLOW_PREFIX + format;
            authFlowVo.setTableName(tableName);
        }

        return authFlowVo;
    }


    /**
     * 补充清算流水数据
     * @param clearingInfo 清算数据
     * @param authFlowVo 授权流水数据
     * @return 补充清算流水数据,重要的点 这里面设置了MarkupRate和CardholderBillingAmountWithMarkup
     */
    private ClearingInfo supplementClearingInfo(ClearingInfo clearingInfo, AuthFlowVo authFlowVo) {
        Date createTime = authFlowVo.getCreateTime();
        clearingInfo.setAuthId(authFlowVo.getId());
        clearingInfo.setTransId(authFlowVo.getTransId());
        clearingInfo.setAuthRemainAuthAmt(authFlowVo.getRemainAuthAmt());
        clearingInfo.setAuthDate(simpleDateFormat.format(createTime));
        clearingInfo.setCardAcceptorId(authFlowVo.getCardAcceptorIdentificationCode());
        clearingInfo.setCardAcceptorName(authFlowVo.getCardAcceptorName());
        clearingInfo.setCardAcceptorCountryCode(authFlowVo.getCountryCode());
        clearingInfo.setCardAcceptorTid(authFlowVo.getCardAcceptorTerminalIdentification());
        clearingInfo.setTransactionType(authFlowVo.getAuthType());
        clearingInfo.setTransactionDate(convertToYYYYMMDD(authFlowVo.getDateTimeLocalTransaction()));
        clearingInfo.setTransactionDatetime("20"+authFlowVo.getDateTimeLocalTransaction());
        clearingInfo.setReferenceNo(authFlowVo.getRetrievalReferenceNumber());
        clearingInfo.setTraceAuditNo(authFlowVo.getSystemsTraceAuditNumber());
        clearingInfo.setAuthAmount(authFlowVo.getTransAmt());
        clearingInfo.setAuthCode(authFlowVo.getApproveCode());
        clearingInfo.setResponseMessage(authFlowVo.getReturnMessage());
        clearingInfo.setProcessorRequestId(authFlowVo.getRequestId());
        clearingInfo.setFeTransactionNumber(authFlowVo.getFeTransactionNumber());
        clearingInfo.setAuthRemainBillAmt(authFlowVo.getRemainBillAmt());
        clearingInfo.setAuthRemainBillAmtWithMarkup(authFlowVo.getRemainBillAmtWithMarkup());
        clearingInfo.setAuthRemainFrozenAmt(authFlowVo.getRemainFrozenAmt());
        BigDecimal authMarkupRate = authFlowVo.getMarkupRate();
        clearingInfo.setAuthMarkupRate(authMarkupRate);

        Integer cardholderCurrencyPrecision = clearingInfo.getCardholderCurrencyPrecision();
        //匹配上重新设置清算类型
        String clearIngType = ClearIngTypeEnum.getClearIngTypeByVisaTransCode(clearingInfo.getTransCode(),clearingInfo.getTransactionType());
        clearingInfo.setClearingType(clearIngType);


        //cardHolder相关;退货不用算cardHold数据,退货不需要计算;匹配上的话用原交易的mark利率;
        if(!clearingInfo.getTransCode().equals("06") && !clearingInfo.getTransCode().equals("26")
                && !clearingInfo.getTransactionCurrencyNo().equals(clearingInfo.getCardholderCurrencyNo())) {
            //如果剩余的持卡人markup金额 = 剩余的持卡人金额 * (1+markup利率) 才取原markup利率;或者授权里面的markup利率大于目前算的markup利率
            BigDecimal remainBillAmt = authFlowVo.getRemainBillAmt();
            BigDecimal remainBillAmtWithMarkup = authFlowVo.getRemainBillAmtWithMarkup();
            if(remainBillAmt.multiply(BigDecimal.ONE.add(authMarkupRate)).setScale(cardholderCurrencyPrecision, RoundingMode.HALF_UP).compareTo(remainBillAmtWithMarkup) == 0
                || authMarkupRate.compareTo(clearingInfo.getMarkupRate()) > 0){
                clearingInfo.setMarkupRate(authFlowVo.getMarkupRate());
                clearingInfo.setCardholderBillingAmountWithMarkup(clearingInfo.getCardholderAmount().multiply(BigDecimal.ONE.add(clearingInfo.getMarkupRate())).setScale(cardholderCurrencyPrecision, BigDecimal.ROUND_HALF_UP));
                clearingInfo.setCardholderMarkupAmount(clearingInfo.getCardholderBillingAmountWithMarkup().subtract(clearingInfo.getCardholderAmount()));
            }
        }
        return clearingInfo;
    }


    /**
     * 是否需要初始化清分数据
     * @param tcCode
     * @param usageCode
     * @return true 需要清分;false 不需要
     */
    private Boolean isInitClearing(String tcCode,String usageCode){

        if (!INIT_CLEARING_TC_CODE.contains(tcCode)) {
            return false;
        }

        //如果是,05,06,07 需要判断usageCode != 0 返回false
        if (("05".equals(tcCode) || "06".equals(tcCode) || "07".equals(tcCode))
                && !"1".equals(usageCode)) {
            return false;
        }
        //todo 这里如果是9 才是disputeData

        return true;
    }


    /**
     * 初始化清算数据
     * @return
     */
    public ClearingInfo initClearingInfo(VisaBase05Data visaBase05Data, ChannelSourceEnum channelSourceEnum,
                                         LocalDate clearingDate,String fileName) throws Exception{
        String transactionCode = visaBase05Data.getTransactionCode();
        //05,06,07,25,26,27 才需要;其他直接返回空
        if(INIT_CLEARING_TC_CODE.contains(transactionCode)){
            String usageCode0 = visaBase05Data.getUsageCode0();
            if(!"1".equals(usageCode0)){
                return null;
            }

            Date date = new Date();
            //VisaDataService 中已经处理过金额,如果不在可以在处理
            String accountNumber0 = visaBase05Data.getAccountNumber0();

            ClearingInfo clearingInfo = new ClearingInfo();
            clearingInfo.setClearingNo(String.valueOf(UidGeneratorUtil.getUID()));
            clearingInfo.setClearingFileName(fileName);
            clearingInfo.setClearingFileId(visaBase05Data.getId());
            clearingInfo.setChannelSource(channelSourceEnum.getCode());
            clearingInfo.setClearingDate(clearingDate);
            clearingInfo.setTransCode(visaBase05Data.getTransactionCode());
            clearingInfo.setUsageCod(usageCode0);
            clearingInfo.setReasonCode(visaBase05Data.getReasonCode0());
            clearingInfo.setMerchantPostalCode(visaBase05Data.getMerchantPostalCode6());
            clearingInfo.setMerchantCity(visaBase05Data.getMerchantCity0());

            clearingInfo.setCardAcceptorId(trimIfNotNull(visaBase05Data.getCardAcceptorId1()));
            clearingInfo.setCardAcceptorName(trimIfNotNull(visaBase05Data.getMerchantName0()));
            clearingInfo.setCardAcceptorCountryCode(trimIfNotNull(visaBase05Data.getMerchantCountryCode0()));
            clearingInfo.setCardAcceptorTid(trimIfNotNull(visaBase05Data.getTerminalId1()));
            clearingInfo.setTransactionType(calculateTransactionType(
                    visaBase05Data.getTransactionCode(),
                    visaBase05Data.getUsageCode0(),
                    visaBase05Data.getTransactionCodeQualifier0()
            ));
            clearingInfo.setTransactionDate(formatDateFromMMDD(visaBase05Data.getPurchaseDate0()));
            clearingInfo.setTransactionDatetime(StringUtils.isNotBlank(clearingInfo.getTransactionDate()) ? clearingInfo.getTransactionDate()+"000000" : null );
            clearingInfo.setTransactionCurrencyNo(visaBase05Data.getSourceCurrencyCode0());

            clearingInfo.setClearAmount(convertToBigDecimal(visaBase05Data.getSourceAmount0()));
            clearingInfo.setRemainingClearAmount(clearingInfo.getClearAmount());
            clearingInfo.setReferenceNo(visaBase05Data.getAcquirerReferenceNumber0());
            clearingInfo.setCardholderAmount(convertToBigDecimal(visaBase05Data.getDestinationAmount0()));
            //币种一致的情况下 持卡人账单金额等于持卡人账单金额含markup金额
            clearingInfo.setCardholderBillingAmountWithMarkup(clearingInfo.getCardholderAmount());
            clearingInfo.setCardholderCurrencyNo(visaBase05Data.getDestinationCurrencyCode0());


            clearingInfo.setAcqArn(trimIfNotNull(visaBase05Data.getAcquirerReferenceNumber0()));
            clearingInfo.setAuthAmount(convertToBigDecimal(visaBase05Data.getSourceAmount0()));
            clearingInfo.setDifferenceFlag(YesFlagEnum.NO.getNumValue());
            clearingInfo.setTransactionAmountOffset(BigDecimal.ZERO);
            clearingInfo.setMarkupRate(BigDecimal.ZERO);
            clearingInfo.setCardholderMarkupAmount(BigDecimal.ZERO);

            clearingInfo.setAuthCode(trimIfNotNull(visaBase05Data.getAuthorizationCode0()));
            clearingInfo.setPosEntryModeTcr0(trimIfNotNull(visaBase05Data.getPosEntryMode0()));
            clearingInfo.setAcquiringIdentifierTcr0(trimIfNotNull(visaBase05Data.getAcquirerBusinessId0()));
            clearingInfo.setCardAcceptorMcc(trimIfNotNull(visaBase05Data.getMerchantCategoryCode0()));
            clearingInfo.setCpd(formatCPD(visaBase05Data.getCentralProcessingDate0()));
            clearingInfo.setIntechangeFeeAmt(convertToBigDecimal(visaBase05Data.getInterchangeFeeAmount5()));
            clearingInfo.setIntechangeFeeSign(trimIfNotNull(visaBase05Data.getInterchangeFeeSign5()));
            clearingInfo.setPosEnvironmentTcr1(trimIfNotNull(visaBase05Data.getPosEnvironment1()));
            clearingInfo.setFxRateSourceTcr5(trimIfNotNull(visaBase05Data.getSourceCurrencyToBaseCurrencyExchangeRate5()));
            clearingInfo.setFxRateDestinationTcr5(trimIfNotNull(visaBase05Data.getBaseCurrencyToDestinationCurrencyExchangeRate5()));
            clearingInfo.setAuthorizationResponseCodeTcr5(trimIfNotNull(visaBase05Data.getAuthorizationResponseCode5()));
            clearingInfo.setMultipleClearingSequenceNumberTcr5(trimIfNotNull(visaBase05Data.getMultipleClearingSequenceNumber5()));
            clearingInfo.setMultipleClearingSequenceCountTcr5(trimIfNotNull(visaBase05Data.getMultipleClearingSequenceCount5()));
            clearingInfo.setMvv(trimIfNotNull(visaBase05Data.getMerchantVerificationValue5()));
            clearingInfo.setAcquiringId(clearingInfo.getAcqArn().substring(1,7));
            clearingInfo.setSettlementFlag(visaBase05Data.getSettlementFlag0());
            clearingInfo.setDeleteFlag(YesFlagEnum.NO.getNumValue());

            clearingInfo.setClearingStatus(ClearingStatusEnum.SUCCESS.getValue());
            clearingInfo.setErrorFlag(YesFlagEnum.NO.getNumValue());
            clearingInfo.setReversalFlag(YesFlagEnum.NO.getNumValue());
            clearingInfo.setCreateTime(date);
            clearingInfo.setUpdateTime(date);
            clearingInfo.setCardSchemaProductId(trimIfNotNull(visaBase05Data.getProductId5()));
            clearingInfo.setCardholderBillingAmountOffset(BigDecimal.ZERO);
            clearingInfo.setCardholderMarkupBillingAmountOffset(BigDecimal.ZERO);
            Integer cardholderCurrencyPrecision = clearingInfo.getCardholderCurrencyPrecision();
            clearingInfo.setNotifyFlag(YesFlagEnum.NO.getNumValue());
            //设置清分类型
            String clearIngType = ClearIngTypeEnum.getClearIngTypeByVisaTransCode(clearingInfo.getTransCode(),clearingInfo.getTransactionType());
            clearingInfo.setClearingType(clearIngType);

            //拿到交易币种精度和币种三位字母
            String sourceCurrencyCode0 = visaBase05Data.getSourceCurrencyCode0();
            CurrencyInfo currencyInfo = currencyInfoService.selectByVisaCurrencyNoFromCache(sourceCurrencyCode0);
            if(null == currencyInfo){
                log.error("未知的交易币种sourceCurrencyCode0:{}",sourceCurrencyCode0);
                return clearingInfo;
            }

            clearingInfo.setTransactionCurrencyCode(currencyInfo.getCcyCode());
            clearingInfo.setTransactionCurrencyPrecision(currencyInfo.getExponent());
            //拿到交易币种精度和币种三位字母
            String destinationCurrencyCode0 = visaBase05Data.getDestinationCurrencyCode0();
            CurrencyInfo cardholderCurrencyInfo = currencyInfoService.selectByVisaCurrencyNoFromCache(destinationCurrencyCode0);
            if(null == cardholderCurrencyInfo){
                log.error("未知的持卡人币种sourceCurrencyCode0:{}",destinationCurrencyCode0);
                return clearingInfo;
            }

            clearingInfo.setCardholderCurrencyCode(cardholderCurrencyInfo.getCcyCode());
            clearingInfo.setCardholderCurrencyPrecision(cardholderCurrencyInfo.getExponent());

            //根据卡号查cardInfo数据
            CardInfoVo cardInfoVo = clearingInfoExtMapper.selectCardInfoByCardNo(accountNumber0);
            if(null == cardInfoVo){
                //没有卡数据直接先退出不查利率了
                return clearingInfo;
            }
            //卡号解密脱敏
            String decryptCardNo = SensitiveInfoUtil.innerDecrypt(accountNumber0);
            String maskCardNo = SensitiveInfoUtil.mask(decryptCardNo, 6, 4, '*');
            clearingInfo.setCardNo(accountNumber0);
            clearingInfo.setMaskedCardNo(maskCardNo);
            clearingInfo.setCustomerMerId(cardInfoVo.getMerchantNo());
            clearingInfo.setProcessorCardId(cardInfoVo.getCardId());
            clearingInfo.setKcardId(cardInfoVo.getKcardId());
            clearingInfo.setSystem(cardInfoVo.getSystem());
            clearingInfo.setCardProductCode(cardInfoVo.getCardName());


            //cardHolder相关;退货不用算cardHold数据,退货不需要计算
            if(!transactionCode.equals("06") && !transactionCode.equals("26") && !clearingInfo.getTransactionCurrencyNo().equals(clearingInfo.getCardholderCurrencyNo())) {
                MarkupFeeCalculateDto markupFeeCalculateDto = chMarkupService.getChMarkupTemplateFee(channelSourceEnum.getCode(),
                        clearingInfo.getCustomerMerId(),null,null,
                        clearingInfo.getTransactionCurrencyNo(),clearingInfo.getCardholderAmount(), clearingInfo.getCardholderCurrencyNo(),cardholderCurrencyPrecision);

                clearingInfo.setCardholderBillingAmountWithMarkup(markupFeeCalculateDto.getCardholderBillingAmountWithMarkup());
                clearingInfo.setMarkupRate(markupFeeCalculateDto.getMarkupFeeRate());
                //含markup金额 - 不含markup金额
                clearingInfo.setCardholderMarkupAmount(clearingInfo.getCardholderBillingAmountWithMarkup().subtract(clearingInfo.getCardholderAmount()));
            }

            return clearingInfo;
        }
        return null;
    }

    /**
     * 去掉数据的空格
     * @param value
     * @return
     */
    private String trimIfNotNull(String value){
        if(StringUtils.isBlank(value)){
            return value;
        }else {
            return value.trim();
        }
    }


    /**
     * 初始化匹配异常表
     * @param clearingInfo 清算数据
     * @return exceptionInfo
     */
    public ExceptionInfo initExceptionInfo(ClearingInfo clearingInfo) {
        ExceptionInfo exceptionInfo = new ExceptionInfo();
        exceptionInfo.setClearingNo(clearingInfo.getClearingNo());
        exceptionInfo.setChannelSource(clearingInfo.getChannelSource());
        exceptionInfo.setSystem(clearingInfo.getSystem());
        exceptionInfo.setClearingDate(clearingInfo.getClearingDate());
        exceptionInfo.setAuthId(clearingInfo.getAuthId());
        exceptionInfo.setTransCode(clearingInfo.getTransCode());
        exceptionInfo.setCustomerMerId(clearingInfo.getCustomerMerId());
        exceptionInfo.setCardAcceptorName(clearingInfo.getCardAcceptorName());
        exceptionInfo.setCardAcceptorCountryCode(clearingInfo.getCardAcceptorCountryCode());
        exceptionInfo.setTransactionDate(clearingInfo.getTransactionDate());
        exceptionInfo.setTransactionCurrencyCode(clearingInfo.getTransactionCurrencyCode());
        exceptionInfo.setTransactionCurrencyNo(clearingInfo.getTransactionCurrencyNo());
        exceptionInfo.setTransactionAmount(clearingInfo.getClearAmount());
        exceptionInfo.setReferenceNo(clearingInfo.getReferenceNo());
        exceptionInfo.setTraceAuditNo(clearingInfo.getTraceAuditNo());
        exceptionInfo.setProcessorCardId(clearingInfo.getProcessorCardId());
        exceptionInfo.setKcardId(clearingInfo.getKcardId());
        exceptionInfo.setMaskedCardNo(clearingInfo.getMaskedCardNo());
        exceptionInfo.setCardholderAmount(clearingInfo.getCardholderAmount());
        exceptionInfo.setAcqArn(clearingInfo.getAcqArn());
        exceptionInfo.setAuthAmount(BigDecimal.ZERO);
        exceptionInfo.setDifferenceFlag(YesFlagEnum.NO.getNumValue());
        exceptionInfo.setDifferenceAmount(BigDecimal.ZERO);
        exceptionInfo.setMarkupRate(BigDecimal.ZERO);
        exceptionInfo.setCardholdMarkupAmount(BigDecimal.ZERO);
        exceptionInfo.setAuthCode(clearingInfo.getAuthCode());
        exceptionInfo.setPosEntryModeTcr0(clearingInfo.getPosEntryModeTcr0());
        exceptionInfo.setAcquiringIdentifierTcr0(clearingInfo.getAcquiringIdentifierTcr0());
        exceptionInfo.setCardAcceptorMcc(clearingInfo.getCardAcceptorMcc());
        exceptionInfo.setCpd(clearingInfo.getCpd());
        exceptionInfo.setIntechangeFeeAmt(clearingInfo.getIntechangeFeeAmt());
        exceptionInfo.setIntechangeFeeSign(clearingInfo.getIntechangeFeeSign());
        exceptionInfo.setPosEnvironmentTcr1(clearingInfo.getPosEnvironmentTcr1());
        exceptionInfo.setFxRateDestinationTcr5(clearingInfo.getFxRateDestinationTcr5());
        exceptionInfo.setFxRateDestinationTcr5(clearingInfo.getFxRateDestinationTcr5());
        exceptionInfo.setAuthorizationResponseCodeTcr5(clearingInfo.getAuthorizationResponseCodeTcr5());
        exceptionInfo.setMultipleClearingSequenceNumberTcr5(clearingInfo.getMultipleClearingSequenceNumberTcr5());
        exceptionInfo.setMultipleClearingSequenceCountTcr5(clearingInfo.getMultipleClearingSequenceCountTcr5());
        exceptionInfo.setMvv(clearingInfo.getMvv());
        exceptionInfo.setCreateTime(clearingInfo.getCreateTime());
        exceptionInfo.setUpdateTime(clearingInfo.getUpdateTime());
        exceptionInfo.setCardholderCurrencyCode(clearingInfo.getCardholderCurrencyCode());
        exceptionInfo.setCardholderCurrencyNo(clearingInfo.getCardholderCurrencyNo());
        //如果有差异金额
        if(null != clearingInfo.getTransactionAmountOffset()){
            exceptionInfo.setDifferenceFlag(YesFlagEnum.YES.getNumValue());
            exceptionInfo.setDifferenceAmount(clearingInfo.getTransactionAmountOffset());
            exceptionInfo.setMarkupRate(clearingInfo.getMarkupRate());
            exceptionInfo.setCardholdMarkupAmount(clearingInfo.getCardholderMarkupAmount());
        }
        return exceptionInfo;
    }

    /**
     * 初始化清算异常表数据
     * @param clearingInfo 清算数据
     * @return 清算异常数据
     */
    private ClearingError initClearingError(ClearingInfo clearingInfo){
        ClearingError clearingError = new ClearingError();
        clearingError.setClearingNo(clearingInfo.getClearingNo());
        clearingError.setChannelSource(clearingInfo.getChannelSource());
        clearingError.setSystem(clearingInfo.getSystem());
        clearingError.setClearingDate(clearingInfo.getClearingDate());
        clearingError.setAuthId(clearingInfo.getAuthId());
        clearingError.setTransCode(clearingInfo.getTransCode());
        clearingError.setCustomerMerId(clearingInfo.getCustomerMerId());
        clearingError.setMerchantName(clearingInfo.getCardAcceptorName());
        clearingError.setMerchantCountryCode(clearingInfo.getCardAcceptorCountryCode());
        clearingError.setTransactionDate(clearingInfo.getTransactionDate());
        clearingError.setTransactionCurrencyCode(clearingInfo.getTransactionCurrencyNo());
        clearingError.setTransactionAmount(clearingInfo.getClearAmount());
        clearingError.setReferenceNo(clearingInfo.getReferenceNo());
        clearingError.setTraceAuditNo(clearingInfo.getTraceAuditNo());
        clearingError.setCardId(clearingInfo.getProcessorCardId());
        clearingError.setKcardId(clearingInfo.getKcardId());
        clearingError.setMaskedCardNo(clearingInfo.getMaskedCardNo());
        clearingError.setCardholderAmount(clearingInfo.getCardholderAmount());
        clearingError.setAcqArn(clearingInfo.getAcqArn());
        clearingError.setAuthCode(clearingInfo.getAuthCode());
        clearingError.setCreateTime(clearingInfo.getCreateTime());
        clearingError.setUpdateTime(clearingInfo.getUpdateTime());

        clearingInfo.setClearingStatus(ClearingStatusEnum.FAILURE.getValue());
        return clearingError;
    }

    /**
     * 获取交易类型
     * @param transCode
     * @param usageCode
     * @param transactionCodeQualifier
     * @return
     */
    private String calculateTransactionType(String transCode, String usageCode, String transactionCodeQualifier) {
        return CalculateTransactionType.getTransactionType(transCode, usageCode, transactionCodeQualifier);
    }

    /**
     * YYMMDDhhmmss  转 YYYYMMDD
     * @param dateTimeLocalTransaction  YYMMDDhhmmss
     * @return
     */
    public String convertToYYYYMMDD(String dateTimeLocalTransaction) {
        // 输入验证：确保字符串不为空且长度至少为6位
        if (dateTimeLocalTransaction == null || dateTimeLocalTransaction.length() < 6) {
            return "";
        }
        String yyMMDD = dateTimeLocalTransaction.substring(0, 6);
        return formatDate(yyMMDD);
    }


    // 日期格式转换 (YYMMDD -> YYYYMMDD)
    private String formatDate(String yyMMDD) {
        if (yyMMDD == null || yyMMDD.length() != 6) {
            return "";
        }
        String year = "20" + yyMMDD.substring(0, 2);
        return year + yyMMDD.substring(2);
    }

    // 日期时间格式转换 (YYMMDD -> YYYYMMDD 00:00:00)
    private String formatDateTime(String yyMMDD) {
        if (yyMMDD == null || yyMMDD.length() != 6) {
            return "";
        }
        String year = "20" + yyMMDD.substring(0, 2);
        return year + yyMMDD.substring(2) + " 00:00:00";
    }

    /**
     * 日期格式转换 (MMDD -> YYYYMMDD，处理大于当前日期的情况)
     * @param mmdd
     * @return
     */
    private String formatDateFromMMDD(String mmdd) {
        if (mmdd == null || mmdd.length() != 4) {
            return null;
        }

        Calendar currentCalendar = Calendar.getInstance();
        int currentYear = currentCalendar.get(Calendar.YEAR);
        int currentMonth = currentCalendar.get(Calendar.MONTH) + 1; // Calendar月份从0开始
        int currentDay = currentCalendar.get(Calendar.DAY_OF_MONTH);

        // 解析输入的MMDD
        int inputMonth = Integer.parseInt(mmdd.substring(0, 2));
        int inputDay = Integer.parseInt(mmdd.substring(2));

        // 构建可能的日期
        String candidateDate = currentYear + mmdd;

        // 检查是否大于当前日期
        if (isDateGreater(candidateDate, currentYear, currentMonth, currentDay)) {
            // 保持月和日不变，年份减一
            int adjustedYear = currentYear - 1;
            return adjustedYear + mmdd;
        }

        return candidateDate;
    }


    /**
     * 检查日期是否大于当前日期
     * @param dateStr
     * @param currentYear
     * @param currentMonth
     * @param currentDay
     * @return
     */
    private boolean isDateGreater(String dateStr, int currentYear, int currentMonth, int currentDay) {
        int year = Integer.parseInt(dateStr.substring(0, 4));
        int month = Integer.parseInt(dateStr.substring(4, 6));
        int day = Integer.parseInt(dateStr.substring(6));

        if (year > currentYear) {
            return true;
        } else if (year < currentYear) {
            return false;
        } else {
            if (month > currentMonth) {
                return true;
            } else if (month < currentMonth) {
                return false;
            } else {
                return day > currentDay;
            }
        }
    }

    // 中央处理日期格式转换 (YDDD -> YYYYMMDD)
    public static String formatCPD(String yddd) {
        if (yddd == null || yddd.length() != 4) {
            return "";
        }
        int currentYear = LocalDate.now().getYear();
        String yearPrefix = String.valueOf(currentYear).substring(0, 3);
        String year = yearPrefix + yddd.substring(0, 1);
        int fullYear = Integer.parseInt(year);
        if (fullYear > currentYear) {
            fullYear -= 10;
        }

        int dayOfYear = Integer.parseInt(yddd.substring(1));
        Calendar calendar = new GregorianCalendar(fullYear, 0, 1);
        calendar.add(Calendar.DAY_OF_YEAR, dayOfYear - 1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(calendar.getTime());
    }

    // 字符串转BigDecimal
    private BigDecimal convertToBigDecimal(String value) {
        if (value == null || value.trim().isEmpty()) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }






    /**
     * 查询授权流水
     * @param processorCardId cardId
     * @param authCode 授权码
     * @param transactionDate 交易日期 YYMMDD
     * @param cardAcceptorId 收单商户号
     * @param acquiringId 收单机构号
     * @param firstTableName 第一次匹配的表名称
     * @param secondTable 第二次匹配的表名称
     * @param transactionCode 清分的交易码;05,06,07
     * @return
     */
    private AuthFlowVo selectAuthFlow(String processorCardId,String authCode,String transactionDate,String cardAcceptorId,
                                      String acquiringId,String firstTableName,String secondTable,String transactionCode){
        String tableName = firstTableName;

        LocalDate date = LocalDate.parse(transactionDate, FORMATTER_YYMMDD);

        LocalDate startDate = date.plusDays(-1);
        String startTransactionDate = startDate.format(FORMATTER_YYMMDD);

        LocalDate endDate = date.plusDays(1);
        String endTransactionDate = endDate.format(FORMATTER_YYMMDD);


        AuthFlowVo authFlowVo = clearingInfoExtMapper.selectVccCallbackInfoByParam(processorCardId, authCode, startTransactionDate, cardAcceptorId, acquiringId, firstTableName,endTransactionDate,transactionCode);
        if(null != authFlowVo){
            authFlowVo.setTableName(tableName);
            //如果是授权交易,且有原交易,拿到最上面一笔
            if(BpcTransTypeEnum.CONSUME.getCode().equals(authFlowVo.getTransType()) && StringUtils.isNotBlank(authFlowVo.getOriginalTransId())){
                authFlowVo =  clearingInfoExtMapper.selectVccCallbackInfoByParam(processorCardId, authCode, startTransactionDate, cardAcceptorId, acquiringId, secondTable,endTransactionDate,transactionCode);
                authFlowVo.setTableName(tableName);
            }
            return authFlowVo;
        }


        authFlowVo =  clearingInfoExtMapper.selectVccCallbackInfoByParam(processorCardId, authCode, startTransactionDate, cardAcceptorId, acquiringId, secondTable,endTransactionDate,transactionCode);
        if(null != authFlowVo){
            tableName = secondTable;
            authFlowVo.setTableName(tableName);
        }

        return authFlowVo;
    }


    /**
     * 根据授权流水主键id查授权流水数据
     * @param transId 交易流水id
     * @param tableName 表名称
     * @return
     */
    private AuthFlowVo selectOriginalAuthFlowByByTransId(String transId ,String tableName) {
        AuthFlowVo authFlowVo = null;
        // 循环查找原交易
        while (true) {
            authFlowVo = clearingInfoExtMapper.selectVccCallbackInfoByTransId(transId, tableName);
            //退货不用去找原交易
            if (authFlowVo == null || StringUtils.isBlank(authFlowVo.getOriginalTransId()) ||
                    BpcTransTypeEnum.REFUND.getCode().equals(authFlowVo.getTransType())) {
                break;
            }
            // 如果有原交易ID，继续查找
            transId = authFlowVo.getOriginalTransId();
            Date originalFinishTime = authFlowVo.getOriginalFinishTime();
            String format = SIMPLE_DATE_FORMAT_YYYY_MM.format(originalFinishTime);
            tableName = AUTH_FLOW_PREFIX + format;
            authFlowVo.setTableName(tableName);
        }

        return authFlowVo;
    }


    /**
     * 保存争议交易
     * @param visaBase05Data visa05数据
     * @param visaBase33Data visa33数据
     */
    private void saveDisputeData(VisaBase05Data visaBase05Data, VisaBase33Data visaBase33Data) {
        if(null != visaBase05Data){
            vrolDisputeService.saveVrolDisputeByVisaBase05Data(visaBase05Data);
        }
        if(null != visaBase33Data){
            vrolDisputeService.saveVrolDisputeByVisaBase33Data(visaBase33Data);
        }

    }


    /**
     * 处理33 文件数据
     * @param bpcBaseFileReq
     */
    public void hand33FileDate(BpcBaseFileReq bpcBaseFileReq) {
        String fileName = bpcBaseFileReq.getFileName();
        //拿到文件查询分页批量查询处理中的数据
        Long total05Data = iVisaBase33DataService.countByFileName(fileName);
        ChannelSourceEnum channelSourceEnum = ChannelSourceEnum.BPC;
        Long maxId = 0L;
        if(total05Data > 0){
            while (true) {
                List<VisaBase33Data> visaBase33DataList = clearingInfoExtMapper.selectVisa33DataFileList(maxId, BATCH_SELECT_TOTAL, fileName);
                if (visaBase33DataList == null || visaBase33DataList.isEmpty()) {
                    log.info("visaBase33DataList is empty;fileName:{},maxId:{}", fileName, maxId);
                    break;
                }
                for (VisaBase33Data visaBase33Data : visaBase33DataList) {
                    String transactionCode = visaBase33Data.getTransactionCode();
                    Integer id = visaBase33Data.getId();
                    log.info("需要把数据放到Dispute中;transactionCode:{},fileId:{}",transactionCode,id);
                    saveDisputeData(null,visaBase33Data);
                }
            }
        }
    }
}
