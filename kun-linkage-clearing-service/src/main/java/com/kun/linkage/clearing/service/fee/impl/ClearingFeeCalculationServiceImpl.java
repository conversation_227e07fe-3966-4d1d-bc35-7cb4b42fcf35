package com.kun.linkage.clearing.service.fee.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.kun.common.util.mq.RocketMqService;
import com.kun.linkage.clearing.dto.TransactionClearingContext;
import com.kun.linkage.clearing.enums.YesFlagEnum;
import com.kun.linkage.clearing.service.fee.ClearingFeeCalculationService;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.base.enums.DigitalCurrencyEnum;
import com.kun.linkage.common.base.enums.FiatCurrencyEnum;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.db.entity.*;
import com.kun.linkage.common.db.mapper.CurrencyInfoMapper;
import com.kun.linkage.common.db.mapper.OrganizationFeeConfigMapper;
import com.kun.linkage.common.db.mapper.OrganizationFeeDetailMapper;
import com.kun.linkage.common.db.mapper.OrganizationFeeTemplateDetailMapper;
import com.kun.linkage.common.external.facade.api.kcard.KCardKunAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.KCardPayXAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXRemarkEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunSideTypeEnum;
import com.kun.linkage.common.external.facade.api.kcard.req.KunAskPriceReq;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXAskPriceReq;
import com.kun.linkage.common.external.facade.api.kcard.res.KunAskPriceRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXAskPriceRsp;
import com.kun.linkage.customer.facade.constants.CustomerTipConstant;
import com.kun.linkage.customer.facade.enums.DeductProcessorEnum;
import com.kun.linkage.customer.facade.enums.OrganizationFeeTypeEnum;
import com.kun.linkage.customer.facade.vo.mq.OrganizationFeeDeductionEventVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 清算手续费计算服务实现类
 * <p>
 * 参照AuthTransactionFeeServiceImpl的实现逻辑，实现清算手续费的计算和处理
 * </p>
 *
 * @since 2025-07-22
 */
@Slf4j
@Service
public class ClearingFeeCalculationServiceImpl implements ClearingFeeCalculationService {

    @Resource
    private OrganizationFeeConfigMapper organizationFeeConfigMapper;

    @Resource
    private OrganizationFeeTemplateDetailMapper organizationFeeTemplateDetailMapper;

    @Resource
    private OrganizationFeeDetailMapper organizationFeeDetailMapper;

    @Resource
    private CurrencyInfoMapper currencyInfoMapper;
    @Resource
    private RocketMqService rocketMqService;
    @Resource
    private KCardKunAccountFacade kCardKunAccountFacade;
    @Resource
    private KCardPayXAccountFacade kCardPayXAccountFacade;

    /**
     * 异步处理清算交易手续费
     * <p>
     * 使用@Async注解实现异步处理，避免阻塞主要清算流程
     * </p>
     *
     * @param context 交易清算上下文
     */
    @Async("clearingTaskExecutor")
    @Override
    public void processTransactionFeeAsync(TransactionClearingContext context) {
        try {
            log.info("开始异步处理清算交易手续费，清算ID: {}", context.getClearingId());

            // 检查是否需要收取手续费
            if (context.getTransactionTypeEnum().getDirection() == null) {
                log.info("交易不需要收取手续费，清算ID: {}", context.getClearingId());
                return;
            }

            OrganizationBasicInfo organizationBasicInfo = context.getOrganizationBasicInfo();

            // 如果是第三方授权且资金池币种是数字货币，需要扣除承兑费
            if (organizationBasicInfo.getThirdPartyAuthorizationFlag() == 1
                    && DigitalCurrencyEnum.contains(organizationBasicInfo.getPoolCurrencyCode())) {
                calculateAndSaveTransactionFeeDetail(context, OrganizationFeeTypeEnum.ACCEPTANCE_FEE,
                        KunAndPayXRemarkEnum.ACCEPTANCE_FEE.getRemark());
            }

            // 计算并保存清算手续费明细
            Result<OrganizationFeeDetail> feeDetailResult = calculateAndSaveTransactionFeeDetail(context,
                    OrganizationFeeTypeEnum.CLEARING_FEE, KunAndPayXRemarkEnum.SETTLEMENT_FEE.getRemark());

            if (!feeDetailResult.isSuccess()) {
                log.error("计算并保存清算手续费明细失败，清算ID: {}, 错误信息: {}",
                        context.getClearingId(), feeDetailResult.getMessage());
                return;
            }

            OrganizationFeeDetail feeDetail = feeDetailResult.getData();
            if (feeDetail == null) {
                log.info("无需收取清算手续费，清算ID: {}", context.getClearingId());
                return;
            }

            log.info("清算交易手续费处理成功，清算ID: {}, 手续费金额: {}, 币种: {}",
                    context.getClearingId(),
                    feeDetail.getFeeAmount(),
                    feeDetail.getTransactionCurrencyCode());

        } catch (Exception e) {
            log.error("异步处理清算交易手续费异常，清算ID: {}", context.getClearingId(), e);
        }
    }

    @Override
    public Result<BigDecimal> calculateTransactionFee(TransactionClearingContext context, OrganizationFeeTemplateDetail templateDetail) {
        try {
            log.info("开始计算清算交易手续费，清算ID: {}", context.getClearingId());

            if (templateDetail == null) {
                log.info("未找到匹配的费率模板明细，清算ID: {}", context.getClearingId());
                return Result.success(BigDecimal.ZERO);
            }

            // 计算手续费
            BigDecimal feeAmount = calculateFeeAmount(templateDetail,
                    context.getPostTransRequestVO().getCardholderMarkupBillingAmount());

            log.info("清算交易手续费计算完成，清算ID: {}, 手续费金额: {}, 币种: {}",
                    context.getClearingId(), feeAmount,
                    context.getPostTransRequestVO().getCardholderBillingCurrency());
            return Result.success(feeAmount);
        } catch (Exception e) {
            log.error("计算清算交易手续费异常，清算ID: {}", context.getClearingId(), e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        }
    }

    @Override
    public Result<OrganizationFeeDetail> calculateAndSaveTransactionFeeDetail(TransactionClearingContext context,
                                                                              OrganizationFeeTypeEnum feeType,
                                                                              String remark) {
        try {
            log.info("开始计算并保存清算交易手续费明细，清算ID: {}", context.getClearingId());

            // 获取费率模板明细
            OrganizationFeeTemplateDetail templateDetail = getTemplateDetail(context, feeType);
            // 计算手续费
            Result<BigDecimal> feeResult = calculateTransactionFee(context, templateDetail);
            if (!feeResult.isSuccess()) {
                return Result.fail(feeResult.getMessage());
            }

            BigDecimal feeAmount = feeResult.getData();
            if (feeAmount.compareTo(BigDecimal.ZERO) == 0) {
                log.info("手续费金额为0，不保存费用明细，清算ID: {}", context.getClearingId());
                return Result.success(null);
            }

            // 保存费用明细
            OrganizationFeeDetail feeDetail = buildFeeDetail(context, feeType, feeAmount, remark, templateDetail);
            organizationFeeDetailMapper.insert(feeDetail);

            log.info("清算交易手续费明细保存成功，清算ID: {}, 明细ID: {}, 手续费金额: {}",
                    context.getClearingId(), feeDetail.getId(), feeAmount);

            this.sendOrganizationFeeDeductionToMq(feeDetail, context.getOrganizationBasicInfo().getMpcToken(),
                    context.getOrganizationBasicInfo().getMpcGroupCode());
            log.info("费率类型:{},收取方式为实时收取,发送收取消息到mq,记录id:{}", feeType.getValue(), feeDetail.getId());

            return Result.success(feeDetail);
        } catch (Exception e) {
            log.error("计算并保存清算交易手续费明细异常，清算ID: {}", context.getClearingId(), e);
            return Result.fail(CommonTipConstant.SYSTEM_INSIDE_ERROR);
        }
    }

    /**
     * 获取机构的交易手续费配置
     */
    private OrganizationFeeConfig getTransactionFeeConfig(String organizationNo, String cardProductCode) {
        LocalDateTime now = LocalDateTime.now().withNano(0);
        LambdaQueryWrapper<OrganizationFeeConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrganizationFeeConfig::getOrganizationNo, organizationNo)
                .eq(OrganizationFeeConfig::getCardProductCode, cardProductCode)
                .eq(OrganizationFeeConfig::getStatus, ValidStatusEnum.VALID.getValue())
                .le(OrganizationFeeConfig::getEffectiveStartTime, now)
                .ge(OrganizationFeeConfig::getEffectiveEndTime, now)
                .orderByDesc(OrganizationFeeConfig::getCreateTime)
                .last("LIMIT 1");

        return organizationFeeConfigMapper.selectOne(queryWrapper);
    }

    /**
     * 获取费率模板明细
     */
    private OrganizationFeeTemplateDetail getTemplateDetail(TransactionClearingContext context, OrganizationFeeTypeEnum feeType) {
        // 获取费率配置
        OrganizationFeeConfig feeConfig = getTransactionFeeConfig(
                context.getOrganizationBasicInfo().getOrganizationNo(),
                context.getOrganizationCustomerCardInfo().getCardProductCode()
        );

        LambdaQueryWrapper<OrganizationFeeTemplateDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrganizationFeeTemplateDetail::getTemplateNo, feeConfig.getTemplateNo())
                .eq(OrganizationFeeTemplateDetail::getFeeType, feeType.getValue())
                .eq(OrganizationFeeTemplateDetail::getCurrencyCode, context.getPostTransRequestVO().getCardholderBillingCurrency())
                .le(OrganizationFeeTemplateDetail::getMinAmount, context.getPostTransRequestVO().getCardholderMarkupBillingAmount())
                .ge(OrganizationFeeTemplateDetail::getMaxAmount, context.getPostTransRequestVO().getCardholderMarkupBillingAmount())
                .orderByDesc(OrganizationFeeTemplateDetail::getCreateTime)
                .last("LIMIT 1");

        return organizationFeeTemplateDetailMapper.selectOne(queryWrapper);
    }

    /**
     * 计算手续费金额
     */
    private BigDecimal calculateFeeAmount(OrganizationFeeTemplateDetail templateDetail, BigDecimal billingAmount) {
        // 比例费用计算
        BigDecimal proportionRate = templateDetail.getProportionRate() != null ? templateDetail.getProportionRate() : BigDecimal.ZERO;
        BigDecimal proportionFeeAmount = billingAmount.multiply(proportionRate).setScale(6, RoundingMode.HALF_UP);

        // 应用比例费用的最小值和最大值限制
        if (templateDetail.getProportionMinAmount() != null && proportionFeeAmount.compareTo(templateDetail.getProportionMinAmount()) < 0) {
            proportionFeeAmount = templateDetail.getProportionMinAmount();
        }
        if (templateDetail.getProportionMaxAmount() != null && proportionFeeAmount.compareTo(templateDetail.getProportionMaxAmount()) > 0) {
            proportionFeeAmount = templateDetail.getProportionMaxAmount();
        }

        // 固定费用
        BigDecimal fixedAmount = templateDetail.getFixedAmount() != null ? templateDetail.getFixedAmount() : BigDecimal.ZERO;

        // 返回比例费用和固定费用的较大值
        return proportionFeeAmount.max(fixedAmount);
    }

    /**
     * 构建费用明细对象
     */
    private OrganizationFeeDetail buildFeeDetail(TransactionClearingContext context, OrganizationFeeTypeEnum feeType,
                                                 BigDecimal feeAmount, String remark, OrganizationFeeTemplateDetail templateDetail) {
        OrganizationFeeDetail feeDetail = new OrganizationFeeDetail();
        LocalDateTime now = LocalDateTime.now().withNano(0);
        // 基本信息
        feeDetail.setOrganizationNo(context.getOrganizationBasicInfo().getOrganizationNo());
        feeDetail.setCardProductCode(context.getOrganizationCustomerCardInfo().getCardProductCode());
        feeDetail.setCalculateDatetime(now);
        Date clearTime = context.getClearingTrans().getClearTime();
        feeDetail.setTransactionDatetime(clearTime == null ? null : DateUtil.toLocalDateTime(clearTime));
        feeDetail.setRelatedTransactionId(context.getClearingId());
        feeDetail.setFeeCollectionMethod(templateDetail.getCollectionMethod());

        BigDecimal proportionRate = templateDetail.getProportionRate() != null ? templateDetail.getProportionRate() : BigDecimal.ZERO;
        BigDecimal fixedAmount = templateDetail.getFixedAmount() != null ? templateDetail.getFixedAmount() : BigDecimal.ZERO;
        feeDetail.setSnapshotBillingDimension(templateDetail.getBillingDimension());
        feeDetail.setSnapshotMinAmount(templateDetail.getMinAmount());
        feeDetail.setSnapshotMaxAmount(templateDetail.getMaxAmount());
        feeDetail.setSnapshotProportionRate(proportionRate);
        feeDetail.setSnapshotProportionMinAmount(templateDetail.getProportionMinAmount());
        feeDetail.setSnapshotProportionMaxAmount(templateDetail.getProportionMaxAmount());
        feeDetail.setSnapshotFixedAmount(fixedAmount);
        // 手续费信息
        feeDetail.setFeeType(feeType.getValue());
        feeDetail.setTransactionAmount(context.getPostTransRequestVO().getCardholderMarkupBillingAmount());
        feeDetail.setTransactionCurrencyCode(context.getPostTransRequestVO().getCardholderBillingCurrency());
        feeDetail.setTransactionCurrencyPrecision(context.getPostTransRequestVO().getCardholderCurrencyExponent());
        feeDetail.setFeeAmount(feeAmount);

        // 扣收信息
        feeDetail.setDeductCurrencyCode(context.getOrganizationBasicInfo().getPoolCurrencyCode());
        feeDetail.setRemark(remark);
        feeDetail.setFeeCollectionStatus(YesFlagEnum.NO.getNumValue());

        // 设置扣收处理器和金额
        BigDecimal fxRate = BigDecimal.ONE;

        if (DigitalCurrencyEnum.contains(context.getOrganizationBasicInfo().getPoolCurrencyCode())) {
            // 数币使用KUN处理器
            feeDetail.setDeductProcessor(DeductProcessorEnum.KUN.getValue());
            feeDetail.setDeductCurrencyPrecision(6);

            // 如果交易币种与资金池币种不同，需要汇率换算
            if (!context.getPostTransRequestVO().getCardholderBillingCurrency()
                    .equals(context.getOrganizationBasicInfo().getPoolCurrencyCode())) {
                fxRate = processingDigitalCurrencyExchangeRate(context, feeAmount);
            }

            feeDetail.setDeductFeeAmount(feeAmount.multiply(fxRate).setScale(6, RoundingMode.UP));
        } else {
            // 法币使用PayX处理器
            feeDetail.setDeductProcessor(DeductProcessorEnum.PAYX.getValue());

            // 查询币种精度
            QueryWrapper<CurrencyInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CurrencyInfo::getCcyCode, context.getOrganizationBasicInfo().getPoolCurrencyCode())
                    .eq(CurrencyInfo::getStatus, YesFlagEnum.YES.getNumValue());
            CurrencyInfo currencyInfo = currencyInfoMapper.selectOne(queryWrapper);

            int precision = currencyInfo != null ? currencyInfo.getExponent() : 2;
            feeDetail.setDeductCurrencyPrecision(precision);

            // 如果交易币种与资金池币种不同，需要汇率换算
            if (!context.getPostTransRequestVO().getCardholderBillingCurrency()
                    .equals(context.getOrganizationBasicInfo().getPoolCurrencyCode())) {
                fxRate = processingFiatCurrencyExchangeRate(context);
            }

            feeDetail.setDeductFeeAmount(feeAmount.multiply(fxRate).setScale(precision, RoundingMode.UP));
        }
        feeDetail.setFxRate(fxRate);
        feeDetail.setDeductRequestNo(String.valueOf(IdWorker.getId()));
        feeDetail.setCallCount(1);

        // 设置时间戳
        feeDetail.setCreateTime(now);
        feeDetail.setLastModifyTime(now);

        return feeDetail;
    }


    /**
     * 组装kun询价请求参数
     *
     * @param feeAmount
     * @param context
     * @return
     */
    private KunAskPriceReq assKunAskPriceReq(BigDecimal feeAmount, TransactionClearingContext context) {
        OrganizationBasicInfo organizationBasicInfo = context.getOrganizationBasicInfo();
        KunAskPriceReq kunAskPriceReq = new KunAskPriceReq();
        kunAskPriceReq.setToken(organizationBasicInfo.getMpcToken());
        kunAskPriceReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        kunAskPriceReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        kunAskPriceReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        kunAskPriceReq.setPayAmount(feeAmount);
        kunAskPriceReq.setSideType(KunSideTypeEnum.BUY.getType());
        // kun接口币对必须数币在前法币在后
        // 持卡人币种一定是法币
        kunAskPriceReq.setSymbol(organizationBasicInfo.getPoolCurrencyCode() + "_" + context.getPostTransRequestVO().getCardholderBillingCurrency());
        return kunAskPriceReq;
    }

    /**
     * 进行法币转数币汇率换算
     *
     * @param context
     */
    private BigDecimal processingDigitalCurrencyExchangeRate(TransactionClearingContext context, BigDecimal feeAmount) {
        // 美金兑USDC需要查最新汇率,兑USDT不需要
        // 港币都需要查询最新汇率
        BigDecimal fxRate = BigDecimal.ONE;
        OrganizationBasicInfo organizationBasicInfo = context.getOrganizationBasicInfo();
        String billingCurrency = context.getPostTransRequestVO().getCardholderBillingCurrency();
        if (!(StringUtils.equals(billingCurrency, FiatCurrencyEnum.USD.getCurrencyCode())
                && StringUtils.equals(organizationBasicInfo.getPoolCurrencyCode(), DigitalCurrencyEnum.USDT.getValue()))) {
            // 注意注意!!!!  kun的接口币对必须是数币在前、法币在后,返回的也是数币兑法币的汇率
            KunAskPriceReq kunAskPriceReq = this.assKunAskPriceReq(feeAmount, context);
            log.info("调用KUN汇率查询接口开始,请求参数:{}", JSON.toJSONString(kunAskPriceReq));
            Result<KunAskPriceRsp> kunAskPriceRspResult = kCardKunAccountFacade.kunExchangeRate(kunAskPriceReq);
            log.info("调用KUN汇率查询接口结束,响应参数:{}", JSON.toJSONString(kunAskPriceRspResult));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            if (kunAskPriceRspResult != null && StringUtils.equals(kunAskPriceRspResult.getCode(), String.valueOf(HttpStatus.SC_OK))
                    && kunAskPriceRspResult.getData() != null && kunAskPriceRspResult.getData().getPrice() != null) {
                fxRate = kunAskPriceRspResult.getData().getPrice();
            } else {
                log.error("调用KUN汇率查询接口失败,响应信息:{}", kunAskPriceRspResult);
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }
        log.info("法币转数币汇率换算完成,原币种:{},目标币种:{},汇率:{}",
                billingCurrency, organizationBasicInfo.getPoolCurrencyCode(), fxRate);
        return fxRate;
    }

    /**
     * 进行法币转法币汇率换算
     *
     * @param context
     */
    private BigDecimal processingFiatCurrencyExchangeRate(TransactionClearingContext context) {
        // feeAmount和资金池币种不一致才需要换汇
        BigDecimal fxRate = BigDecimal.ONE;
        OrganizationBasicInfo organizationBasicInfo = context.getOrganizationBasicInfo();
        String billingCurrency = context.getPostTransRequestVO().getCardholderBillingCurrency();
        if (!StringUtils.equals(billingCurrency, organizationBasicInfo.getPoolCurrencyCode())) {
            PayXAskPriceReq payXAskPriceReq = this.assPayXAskPriceReq(billingCurrency, organizationBasicInfo);
            log.info("调用PayX汇率查询接口开始,请求参数:{}", JSON.toJSONString(payXAskPriceReq));
            Result<PayXAskPriceRsp> payXAskPriceRspResult = kCardPayXAccountFacade.payXExchangeRate(payXAskPriceReq);
            log.info("调用PayX汇率查询接口结束,响应参数:{}", JSON.toJSONString(payXAskPriceRspResult));
            // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
            if (payXAskPriceRspResult != null && StringUtils.equals(payXAskPriceRspResult.getCode(), String.valueOf(HttpStatus.SC_OK))
                    && payXAskPriceRspResult.getData() != null && payXAskPriceRspResult.getData().getExchangeRate() != null) {
                fxRate = payXAskPriceRspResult.getData().getExchangeRate();
            } else {
                log.error("调用PayX汇率查询接口失败,响应信息:{}", fxRate);
                throw new BusinessException(CustomerTipConstant.CALL_CHANNEL_FAIL);
            }
        }
        log.info("法币转法币汇率换算完成,原币种:{},目标币种:{},汇率:{}",
                billingCurrency, organizationBasicInfo.getPoolCurrencyCode(), fxRate);
        return fxRate;
    }

    /**
     * 组装payX询价请求参数
     *
     * @param sourceCurrency
     * @param organizationBasicInfo
     * @return
     */
    private PayXAskPriceReq assPayXAskPriceReq(String sourceCurrency, OrganizationBasicInfo organizationBasicInfo) {
        PayXAskPriceReq payXAskPriceReq = new PayXAskPriceReq();
        payXAskPriceReq.setToken(organizationBasicInfo.getMpcToken());
        payXAskPriceReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        payXAskPriceReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        payXAskPriceReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        payXAskPriceReq.setSourceCurrency(sourceCurrency);
        payXAskPriceReq.setTargetCurrency(organizationBasicInfo.getPoolCurrencyCode());
        return payXAskPriceReq;
    }

    /**
     * 发送机构费用扣除事件到mq中
     *
     * @param organizationFeeDetail
     * @param mpcToken
     * @param mpcGroupCode
     */
    public void sendOrganizationFeeDeductionToMq(OrganizationFeeDetail organizationFeeDetail, String mpcToken, String mpcGroupCode) {
        log.info("发送机构费用扣除事件到mq中");
        OrganizationFeeDeductionEventVO organizationFeeDeductionEventVO = new OrganizationFeeDeductionEventVO();
        organizationFeeDeductionEventVO.setFeeDetailId(organizationFeeDetail.getId());
        organizationFeeDeductionEventVO.setTransactionDatetime(organizationFeeDetail.getTransactionDatetime());
        organizationFeeDeductionEventVO.setMpcToken(mpcToken);
        organizationFeeDeductionEventVO.setMpcGroupCode(mpcGroupCode);
        rocketMqService.delayedSend(MqTopicConstant.ORGANIZATION_FEE_DEDUCTION_EVENT_TOPIC, organizationFeeDeductionEventVO, 10000, MqTopicConstant.DELAY_LEVEL_10S);
    }
}
