package com.kun.linkage.clearing.task;

import com.alibaba.fastjson.JSON;
import com.kun.common.util.log.trace.LogContext;
import com.kun.linkage.clearing.facade.constant.OrganizationFileTypeEnum;
import com.kun.linkage.clearing.service.kunlinkage.organizationFile.OrganizationSettlementFileBizService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 机构清算文件生成任务
 */
@Slf4j
@Component
public class OrganizationSettlementFileTask {
    @Resource
    private OrganizationSettlementFileBizService organizationSettlementFileBizService;

    @NewSpan
    @XxlJob("organizationSettlementFileTask")
    public boolean organizationSettlementFileTask() {
        XxlJobHelper.log("[机构清算文件生成任务]开始执行");
        String fileDate = null;
        String organizationNo = null;
        try {
            LogContext.fromContext(LogContext.getContext());
            String jobParam = XxlJobHelper.getJobParam();
            if (StringUtils.isNotBlank(jobParam)) {
                XxlJobHelper.log("[机构清算文件生成任务]生成的参数为XXL-JOB中传入的参数");
                fileDate = JSON.parseObject(jobParam).getString("fileDate");
                organizationNo = JSON.parseObject(jobParam).getString("organizationNo");
            } else {
                XxlJobHelper.log("[机构清算文件生成任务]生成的日期为当前日期");
                fileDate = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            }
            boolean result;
            if (StringUtils.isNotBlank(organizationNo)) {
                XxlJobHelper.log("[机构清算文件生成任务]生成的机构号为:{},生成的文件日期为:{}", organizationNo, fileDate);
                result = organizationSettlementFileBizService.generateByOrganization(OrganizationFileTypeEnum.SETTLEMENT_FILE, fileDate, organizationNo);
            } else {
                XxlJobHelper.log("[机构清算文件生成任务]生成的文件日期为:{}");
                result = organizationSettlementFileBizService.generate(OrganizationFileTypeEnum.SETTLEMENT_FILE, fileDate);
            }
            if (result) {
                return XxlJobHelper.handleSuccess();
            } else {
                return XxlJobHelper.handleFail();
            }
        } catch (Exception e) {
            XxlJobHelper.log(e.getMessage());
            XxlJobHelper.log(e);
            return XxlJobHelper.handleFail(e.getMessage());
        } finally {
            XxlJobHelper.log("[机构清算文件生成任务]执行结束", LogContext.getContext().getTraceId());
            LogContext.destroy();
        }
    }
}
