package com.kun.linkage.clearing.enums;

import com.kun.linkage.common.base.enums.DirectionEnum;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 清分类型枚举
 */
public enum ClearIngTypeEnum {

    CONSUMPTION("1050", "05", DirectionEnum.DEBIT,"消费", Arrays.asList("010000","010001","010002")),
    TRANSFER_OUT("1051", "05", DirectionEnum.DEBIT,"汇款", Collections.singletonList("010005")),
    REFUND("1060", "06", DirectionEnum.CREDIT,"退货", Collections.singletonList("010004")),
    TRANSFER_IN("1061", "06", DirectionEnum.CREDIT,"收款",Collections.singletonList("010006")),
    WITHDRAWAL("1070", "07", DirectionEnum.DEBIT,"取现",Collections.singletonList("010003")),
    CONSUMPTION_VOID("2050", "25", DirectionEnum.CREDIT,"消费撤销", Arrays.asList("410000","410001","410002")),
    TRANSFER_OUT_REVERSAL("2051", "25", DirectionEnum.CREDIT,"汇款撤销",Collections.singletonList("410005")),
    REFUND_REVERSAL("2060", "26",DirectionEnum.DEBIT,"退货撤销",Collections.singletonList("410004")),
    TRANSFER_IN_REVERSAL("2061", "26",DirectionEnum.DEBIT,"收款撤销",Collections.singletonList("410006")),
    WITHDRAWAL_REVERSAL("2070", "27", DirectionEnum.CREDIT,"取现撤销",Collections.singletonList("410003")),
    DISPUTE("3000", "33", null, "争议",null),
    DISPUTE_15("3000", "15",null, "争议",null),
    DISPUTE_16("3000", "16",null, "争议",null),
    DISPUTE_17("3000", "17",null, "争议",null),
    DISPUTE_35("3000", "35",null, "争议",null),
    DISPUTE_36("3000", "36",null, "争议",null),
    DISPUTE_37("3000", "37",null, "争议",null);

    private final String clearIngType;

    private final String visaTransCode;

    private DirectionEnum direction;

    private final String desc;

    private final List<String> transactionType;

    private String dictType = "KL_CLEARING_TRANS@CLEARING_TYPE";

    public String getClearIngType() {
        return clearIngType;
    }

    public String getVisaTransCode() {
        return visaTransCode;
    }

    public DirectionEnum getDirection() {
        return direction;
    }

    public String getDesc() {
        return desc;
    }

    public List<String> getTransactionType() {
        return transactionType;
    }

    public String getDictType() {
        return dictType;
    }

    ClearIngTypeEnum(String clearIngType, String visaTransCode, DirectionEnum direction, String desc, List<String> transactionType) {
        this.clearIngType = clearIngType;
        this.visaTransCode = visaTransCode;
        this.desc = desc;
        this.transactionType = transactionType;
        this.direction = direction;
    }

    /**
     * 根据 visaTransCode 获取对应的 clearIngType
     * @param visaTransCode
     * @return
     */
    public static String getClearIngTypeByVisaTransCode(String visaTransCode,String transactionType) {
        for (ClearIngTypeEnum type : ClearIngTypeEnum.values()) {
            if (type.getVisaTransCode().equals(visaTransCode) && (null == type.getTransactionType() || type.getTransactionType().contains(transactionType))) {
                return type.getClearIngType();
            }
        }
        return null; // 如果没有匹配到，返回 null 或者其他默认值
    }

    public static ClearIngTypeEnum getEnumByClearType(String clearIngType) {
        for (ClearIngTypeEnum type : ClearIngTypeEnum.values()) {
            if (type.getClearIngType().equals(clearIngType)) {
                return type;
            }
        }
        return null; // 如果没有匹配到，返回 null 或者其他默认值
    }

    public static String getDirectionByClearType(String clearType) {
        for (ClearIngTypeEnum type : ClearIngTypeEnum.values()) {
            if (type.getClearIngType().equals(clearType) && null != type.getDirection()) {
                return type.getDirection().getValue();
            }
        }
        return null;
    }


}
