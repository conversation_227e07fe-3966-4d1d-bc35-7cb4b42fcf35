package com.kun.linkage.clearing.service.kunlinkage.organizationFile;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.kun.linkage.clearing.enums.ClearIngTypeEnum;
import com.kun.linkage.clearing.ext.mapper.OrganizationFileExtMapper;
import com.kun.linkage.clearing.facade.vo.kunlinkage.OrganizationSettlementFileVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 机构结算文件生成业务服务类
 */
@Service
public class OrganizationSettlementFileBizService extends AbstractOrganizationFileService {
    private static final Logger log = LoggerFactory.getLogger(OrganizationSettlementFileBizService.class);
    @Resource
    private OrganizationFileExtMapper organizationFileExtMapper;

    @Override
    protected File generateFile(String fileDate, String organizationNo) {
        String startId = "0";
        // 根据文件日期获取表名后缀
        String position = fileDate.substring(0, 6);
        List<OrganizationSettlementFileVO> allDataList = new ArrayList<>();
        while (true) {
            // 获取数据
            List<OrganizationSettlementFileVO> dataList =
                    organizationFileExtMapper.pageListSettlementFileData(position, organizationNo, fileDate, startId);
            if (dataList == null || dataList.isEmpty()) {
                // 没有待处理数据
                log.warn("文件日期:{},机构号:{},数据获取完成", fileDate, organizationNo);
                break;
            } else {
                dataList.forEach(item -> {
                    item.setDebitCreditIndicator(ClearIngTypeEnum.getDirectionByClearType(item.getTransactionType()));
                    item.setCardAcceptorName("\"" + (item.getCardAcceptorName() == null ? "" : item.getCardAcceptorName()) + "\"");
                });
                startId = dataList.get(dataList.size() - 1).getClearId();
            }
            allDataList.addAll(dataList);
        }
        File tempFile = FileUtil.createTempFile();
        CsvWriter writer = CsvUtil.getWriter(tempFile, StandardCharsets.UTF_8);
        writer.writeBeans(allDataList);
        return tempFile;
    }
}
