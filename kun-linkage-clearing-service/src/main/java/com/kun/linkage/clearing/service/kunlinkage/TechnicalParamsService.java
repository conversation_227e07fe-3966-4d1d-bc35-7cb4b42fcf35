package com.kun.linkage.clearing.service.kunlinkage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.db.entity.TechnicalParams;
import com.kun.linkage.common.db.mapper.TechnicalParamsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class TechnicalParamsService {

    @Resource
    private TechnicalParamsMapper technicalParamsMapper;

    /**
     * 获取技术参数
     *
     * @param organizationNo 机构编号
     * @return
     */
    public TechnicalParams getTechnicalParams(String organizationNo) {
        return technicalParamsMapper.selectOne(
            new LambdaQueryWrapper<TechnicalParams>().eq(TechnicalParams::getOrganizationNo, organizationNo)
                .eq(TechnicalParams::getStatus, ValidStatusEnum.VALID.getValue()));
    }
}
