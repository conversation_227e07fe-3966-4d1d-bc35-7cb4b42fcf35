package com.kun.linkage.clearing.service.fee;

import com.kun.linkage.clearing.dto.TransactionClearingContext;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.db.entity.OrganizationFeeDetail;
import com.kun.linkage.common.db.entity.OrganizationFeeTemplateDetail;
import com.kun.linkage.customer.facade.enums.OrganizationFeeTypeEnum;

import java.math.BigDecimal;

/**
 * 清算手续费计算服务接口
 * <p>
 * 负责清算过程中的手续费计算、扣收等操作
 * 参照AuthTransactionFeeServiceImpl的实现逻辑
 * </p>
 *
 * @since 2025-07-22
 */
public interface ClearingFeeCalculationService {

    /**
     * 异步处理清算交易手续费
     * <p>
     * 在清算完成后异步处理手续费计算和扣收，避免阻塞主要清算流程
     * </p>
     *
     * @param context 交易清算上下文
     */
    void processTransactionFeeAsync(TransactionClearingContext context);

    /**
     * 计算清算交易手续费
     * <p>
     * 根据机构费率配置和交易信息计算手续费金额
     * </p>
     *
     * @param context 交易清算上下文
     * @param templateDetail Fee模板
     * @return 计算结果，包含手续费金额和相关信息
     */
    Result<BigDecimal> calculateTransactionFee(TransactionClearingContext context, OrganizationFeeTemplateDetail templateDetail);

    /**
     * 计算并保存清算交易手续费明细
     * <p>
     * 计算手续费并保存到费用明细表，支持KUN和PayX双币种扣收
     * </p>
     *
     * @param context 交易清算上下文
     * @param feeType 手续费类型
     * @param remark 备注信息
     * @return 保存的费用明细记录
     */
    Result<OrganizationFeeDetail> calculateAndSaveTransactionFeeDetail(TransactionClearingContext context, 
                                                                       OrganizationFeeTypeEnum feeType, 
                                                                       String remark);
}
