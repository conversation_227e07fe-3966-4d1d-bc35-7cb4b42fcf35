package com.kun.linkage.clearing.ext.mapper;

import com.kun.linkage.clearing.facade.vo.kunlinkage.OrganizationReleaseFileVO;
import com.kun.linkage.clearing.facade.vo.kunlinkage.OrganizationSettlementFileVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

public interface OrganizationFileExtMapper {

    /**
     * 分页获取机构清算文件数据
     */
    List<OrganizationSettlementFileVO> pageListSettlementFileData(@Param("partition") String partition,
                                                                  @Param("organizationNo") String organizationNo,
                                                                  @Param("fileDate") String fileDate,
                                                                  @Param("startId") String startId);

    /**
     * 分页获取机构释放文件数据
     */
    List<OrganizationReleaseFileVO> pageListReleaseFileData(@Param("startReleaseTime") LocalDateTime startReleaseTime,
                                                            @Param("endReleaseTime") LocalDateTime endReleaseTime,
                                                            @Param("startCreateTime") Date startCreateTime,
                                                            @Param("endCreateTime") Date endCreateTime,
                                                            @Param("organizationNo") String organizationNo,
                                                            @Param("startId") String startId);


}
