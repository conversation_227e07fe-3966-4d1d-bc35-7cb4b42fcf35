package com.kun.linkage.clearing.service.kunlinkage.organizationFile;

import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.aws.AwsS3Util;
import com.kun.common.util.aws.AwzS3Properties;
import com.kun.common.util.lark.LarkAlarmUtil;
import com.kun.linkage.clearing.facade.constant.OrganizationFileTypeEnum;
import com.kun.linkage.common.base.enums.OperationStatusEnum;
import com.kun.linkage.common.base.enums.ValidStatusEnum;
import com.kun.linkage.common.db.entity.OrganizationBasicInfo;
import com.kun.linkage.common.db.entity.OrganizationFileRecord;
import com.kun.linkage.common.db.mapper.OrganizationBasicInfoMapper;
import com.kun.linkage.common.db.mapper.OrganizationFileRecordMapper;
import com.kun.linkage.customer.facade.enums.OrganizationModeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public abstract class AbstractOrganizationFileService {
    private static final Logger log = LoggerFactory.getLogger(AbstractOrganizationFileService.class);
    @Resource
    private OrganizationFileRecordMapper organizationFileRecordMapper;
    @Resource
    private OrganizationBasicInfoMapper organizationBasicInfoMapper;
    @Resource
    private AwsS3Util awsS3Util;
    @Resource
    private AwzS3Properties awzS3Properties;
    @Resource
    private LarkAlarmUtil larkAlarmUtil;
    @Value("${kun.aws.s3.fileFolder:}")
    private String rootPath;
    /**
     * 机构文件文件根目录
     */
    private static final String organizationFileRootPath = "organizationFile";

    /**
     * 生成机构文件
     *
     * @param fileTypeEnum
     * @param fileDate
     * @return
     */
    public boolean generate(OrganizationFileTypeEnum fileTypeEnum, String fileDate) {
        log.info("开始生成机构数据文件,文件类型:{},文件日期:{}", fileTypeEnum.getValue(), fileDate);
        // 获取转三方授权有效的机构信息
        List<OrganizationBasicInfo> organizationBasicInfoList = organizationBasicInfoMapper.selectList(Wrappers.<OrganizationBasicInfo>lambdaQuery()
                        .eq(OrganizationBasicInfo::getMode, OrganizationModeEnum.TRANSFER_TO_THIRD_PARTY_AUTHORIZATION.getValue())
                        .eq(OrganizationBasicInfo::getStatus, ValidStatusEnum.VALID.getValue()));
        if (organizationBasicInfoList == null || organizationBasicInfoList.isEmpty()) {
            log.warn("没有有效的转三方授权机构数据,直接返回成功");
            return true;
        }
        List<String> failOrganizationNoList = new ArrayList<>();
        organizationBasicInfoList.forEach(organizationBasicInfo -> {
            boolean result = this.generateByOrganization(fileTypeEnum, fileDate, organizationBasicInfo.getOrganizationNo());
            if (!result) {
                failOrganizationNoList.add(organizationBasicInfo.getOrganizationNo());
            }
        });
        // 如果生成失败就发送lark告警
        if (!failOrganizationNoList.isEmpty()) {
            this.sendLarkAlarm(fileTypeEnum, fileDate, failOrganizationNoList);
            return false;
        }
        log.info("机构数据文件生成执行完成,文件类型:{},文件日期:{}", fileTypeEnum.getValue(), fileDate);
        return true;
    }

    /**
     * 生成机构文件
     *
     * @param fileTypeEnum
     * @param fileDate
     * @return
     */
    public boolean generateByOrganization(OrganizationFileTypeEnum fileTypeEnum, String fileDate, String organizationNo) {
        log.info("开始生成机构数据文件,文件类型:{},文件日期:{},机构号:{}", fileTypeEnum.getValue(), fileDate, organizationNo);
        // 初始化机构文件生成记录
        OrganizationFileRecord record = this.initFileRecord(fileTypeEnum, fileDate, organizationNo);
        if (record == null) {
            return true;
        }
        boolean result = true;
        File tempFile = null;
        try {
            // 生成机构文件数据文件
            tempFile = this.generateFile(fileDate, organizationNo);
            log.info("机构数据临时文件生成完成,开始进行上传到S3文件服务器中,文件类型:{},文件日期:{},机构号:{}", fileTypeEnum.getValue(), fileDate, organizationNo);
            // 上传文件到S3服务器中
            String url = awsS3Util.uploadChunkedFile(record.getFileName(), tempFile, awzS3Properties.getBucket(),
                    this.getFilePath(fileTypeEnum), CannedAccessControlList.PublicReadWrite);
            if (StringUtils.isBlank(url)) {
                log.error("机构数据文件上传失败,机构文件类型:{},文件日期:{},机构号:{}", fileTypeEnum.getValue(), fileDate, organizationNo);
                record.setFileGenerateStatus(OperationStatusEnum.FAIL.getStatus());
                result = false;
            } else {
                log.info("机构数据文件上传成功,机构文件类型:{},文件日期:{},机构号:{}", fileTypeEnum.getValue(), fileDate, organizationNo);
                record.setFileFullPath(url);
                record.setFileGenerateStatus(OperationStatusEnum.SUCCESS.getStatus());
            }
        } catch (Exception e) {
            log.error("机构数据文件生成异常,机构文件类型:{},文件日期:{},机构号:{}", fileTypeEnum.getValue(), fileDate, organizationNo, e);
            record.setFileGenerateStatus(OperationStatusEnum.FAIL.getStatus());
            result = false;
        } finally {
            log.info("开始进行删除临时文件,机构文件类型:{},文件日期:{},机构号:{}", fileTypeEnum.getValue(), fileDate, organizationNo);
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
            // 更新结果
            record.setLastModifyTime(LocalDateTime.now());
            organizationFileRecordMapper.updateById(record);
        }
        return result;
    }

    /**
     * 生成机构数据文件
     *
     * @param fileDate
     * @param organizationNo
     */
    protected abstract File generateFile(String fileDate, String organizationNo);

    /**
     * 初始化文件记录
     *
     * @param fileTypeEnum
     * @param fileDate
     * @return (返回null为已存在生成成功的数据, 否则会返回初始化成功的记录)
     */
    private OrganizationFileRecord initFileRecord(OrganizationFileTypeEnum fileTypeEnum, String fileDate, String organizationNo) {
        OrganizationFileRecord record = organizationFileRecordMapper.selectOne(Wrappers.<OrganizationFileRecord>lambdaQuery()
                .eq(OrganizationFileRecord::getOrganizationNo, organizationNo)
                .eq(OrganizationFileRecord::getFileType, fileTypeEnum.getValue())
                .eq(OrganizationFileRecord::getFileDate, fileDate));
        if (record != null) {
            if (StringUtils.equals(record.getFileGenerateStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                log.warn("已存在生成成功的记录数据,请勿重复生成,文件日期:{}", fileDate);
                return null;
            } else {
                record.setFileGenerateStatus(OperationStatusEnum.PENDING.getStatus());
                record.setFileFullPath(null);
                LocalDateTime now = LocalDateTime.now();
                record.setCreateTime(now);
                record.setLastModifyTime(now);
                organizationFileRecordMapper.updateById(record);
            }
        } else {
            record = new OrganizationFileRecord();
            record.setOrganizationNo(organizationNo);
            record.setFileType(fileTypeEnum.getValue());
            record.setFileName(this.getFileName(fileTypeEnum, organizationNo, fileDate));
            record.setFilePathName(this.getFilePath(fileTypeEnum) + "/" + record.getFileName());
            record.setFileDate(fileDate);
            record.setFileGenerateStatus(OperationStatusEnum.PENDING.getStatus());
            LocalDateTime now = LocalDateTime.now();
            record.setCreateTime(now);
            record.setLastModifyTime(now);
            organizationFileRecordMapper.insert(record);
        }
        return record;
    }

    /**
     * 获取文件名称
     *
     * @param organizationNo
     * @param fileDate
     * @return
     */
    protected String getFileName(OrganizationFileTypeEnum fileTypeEnum, String organizationNo, String fileDate) {
        return String.format(fileTypeEnum.getFileNameFormat(), organizationNo, fileDate);
    }

    /**
     * 获取文件目录
     *
     * @param fileTypeEnum
     * @return
     */
    protected String getFilePath(OrganizationFileTypeEnum fileTypeEnum) {
        return rootPath + "/" + organizationFileRootPath + "/" + fileTypeEnum.getFileDir();
    }

    /**
     * 发送LARK告警
     * @param fileTypeEnum
     * @param fileDate
     */
    private void sendLarkAlarm(OrganizationFileTypeEnum fileTypeEnum, String fileDate, List<String> organizationNoList) {
        log.warn("机构文件生成失败开始发送lark告警,机构文件类型:{},文件日期:{},失败的机构号:{}", fileTypeEnum.getValue(), fileDate, organizationNoList);
        String msg = String.format("[机构文件生成失败]机构文件类型:%s, 文件日期:%s, 失败的机构号:%s", fileTypeEnum.getValue(), fileDate, organizationNoList);
        larkAlarmUtil.sendTextAlarm(msg);
    }
}
