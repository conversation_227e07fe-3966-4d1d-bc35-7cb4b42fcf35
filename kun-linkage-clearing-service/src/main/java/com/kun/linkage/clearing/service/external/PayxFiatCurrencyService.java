package com.kun.linkage.clearing.service.external;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.db.entity.OrganizationBasicInfo;
import com.kun.linkage.common.external.facade.api.kcard.KCardPayXAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXAskPriceReq;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXDebitSubRefundReq;
import com.kun.linkage.common.external.facade.api.kcard.req.PayXDebitSubReq;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXAskPriceRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXDebitSubRefundRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXDebitSubRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Slf4j
@Service
public class PayxFiatCurrencyService {

    @Resource
    private KCardPayXAccountFacade kCardPayXAccountFacade;

    /**
     * 请求法币兑换价格
     *
     * @param organizationBasicInfo 机构基本信息
     * @param amount                兑换金额
     * @param sourceCurrencyCode    法币币种
     * @param targetCurrencyCode    目标币种
     * @return
     */
    public BigDecimal askPrice(OrganizationBasicInfo organizationBasicInfo, BigDecimal amount,
        String sourceCurrencyCode, String targetCurrencyCode) {
        BigDecimal fxRate = BigDecimal.ONE;
        log.info("请求法币兑换价格, 机构号: {}, 金额: {}, 来源币种: {}, 目标币种: {}",
            organizationBasicInfo.getOrganizationNo(), amount, sourceCurrencyCode, targetCurrencyCode);
        PayXAskPriceReq payXAskPriceReq = new PayXAskPriceReq();
        payXAskPriceReq.setToken(organizationBasicInfo.getMpcToken());
        payXAskPriceReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        payXAskPriceReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        payXAskPriceReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        payXAskPriceReq.setSourceCurrency(sourceCurrencyCode);
        payXAskPriceReq.setTargetCurrency(targetCurrencyCode);
        log.info("调用PAYX汇率查询接口开始,请求参数:{}", JSON.toJSONString(payXAskPriceReq));
        Result<PayXAskPriceRsp> payXAskPriceRspResult = kCardPayXAccountFacade.payXExchangeRate(payXAskPriceReq);
        log.info("调用PAYX汇率查询接口开始,响应参数:{}", JSON.toJSONString(payXAskPriceRspResult));
        if (payXAskPriceRspResult != null && StringUtils.equals(payXAskPriceRspResult.getCode(), String.valueOf(
            HttpStatus.SC_OK)) && payXAskPriceRspResult.getData() != null && payXAskPriceRspResult.getData()
            .getExchangeRate() != null) {
            fxRate = payXAskPriceRspResult.getData().getExchangeRate();
        } else {
            log.error("调用KUN汇率查询接口失败");
            throw new BusinessException(KunLinkageAuthResponseCodeConstant.CALL_PAYX_FAIL.getCode());
        }
        return fxRate;
    }

    /**
     * 调用PAYX账户扣除法币
     *
     * @param mpcToken       MPC Token
     * @param mpcGroupCode   MPC Group Code
     * @param organizationNo 机构号
     * @param requestNo      请求号
     * @param direction      交易方向
     * @param currencyCode   法币币种
     * @param amount         扣除金额
     * @param remark         备注信息
     * @return
     */
    public Result<PayXDebitSubRsp> payxDebitSub(String mpcToken, String mpcGroupCode, String organizationNo,
        String requestNo, String direction, String currencyCode, BigDecimal amount, String remark) {
        PayXDebitSubReq payXDebitSubReq = new PayXDebitSubReq();
        payXDebitSubReq.setToken(mpcToken);
        payXDebitSubReq.setGroupProductCode(mpcGroupCode);
        payXDebitSubReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        payXDebitSubReq.setAccountNo(organizationNo);
        payXDebitSubReq.setDirection(direction);
        payXDebitSubReq.setRequestNo(requestNo);
        payXDebitSubReq.setCurrency(currencyCode);
        payXDebitSubReq.setAmount(amount);
        payXDebitSubReq.setRemark(remark);
        log.info("调用PAYX账户扣除法币开始,请求参数:{}", JSON.toJSONString(payXDebitSubReq));
        Result<PayXDebitSubRsp> result = kCardPayXAccountFacade.payXDebitSub(payXDebitSubReq);
        log.info("调用PAYX账户扣除法币结束,响应参数:{}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 调用PAYX账户法币冲账
     *
     * @param mpcToken          MPC Token
     * @param mpcGroupCode      MPC Group Code
     * @param organizationNo    机构号
     * @param originalRequestNo 原始请求号
     * @return
     */
    public Result<PayXDebitSubRefundRsp> payXDebitSubRefund(String mpcToken, String mpcGroupCode, String organizationNo,
        String originalRequestNo) {
        PayXDebitSubRefundReq payXDebitSubRefundReq = new PayXDebitSubRefundReq();
        payXDebitSubRefundReq.setToken(mpcToken);
        payXDebitSubRefundReq.setGroupProductCode(mpcGroupCode);
        payXDebitSubRefundReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        payXDebitSubRefundReq.setAccountNo(organizationNo);
        payXDebitSubRefundReq.setSourceRequestNo(originalRequestNo);
        log.info("调用PAYX账户法币冲账开始,请求参数:{}", JSON.toJSONString(payXDebitSubRefundReq));
        Result<PayXDebitSubRefundRsp> result = kCardPayXAccountFacade.payXDebitSubRefund(payXDebitSubRefundReq);
        log.info("调用PAYX账户法币冲账结束,响应参数:{}", JSON.toJSONString(result));
        return result;
    }
}
