package com.kun.linkage.common.redis.config;

import com.kun.linkage.common.redis.utils.RedissonCacheUtil;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedissonAutoConfiguration {
    @Bean
    public RedissonCacheUtil redissonCacheUtil() {
        return new RedissonCacheUtil();
    }
    @Bean
    public RedissonLockUtil redissonLockUtil() {
        return new RedissonLockUtil();
    }
}
