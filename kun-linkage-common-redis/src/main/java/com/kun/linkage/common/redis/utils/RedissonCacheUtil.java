package com.kun.linkage.common.redis.utils;

import org.redisson.api.*;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.concurrent.TimeUnit;

@Component
public class RedissonCacheUtil {
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private Environment environment;


    /**
     * 设置缓存值，无过期时间
     *
     * @param key   缓存键
     * @param value 缓存值
     * @param <T>   值的类型
     */
    public <T> void set(String key, T value) {
        RBucket<T> bucket = redissonClient.getBucket(this.formatKey(key));
        bucket.set(value);
    }

    /**
     * 设置缓存值，并指定过期时间
     *
     * @param key      缓存键
     * @param value    缓存值
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     * @param <T>      值的类型
     */
    public <T> void set(String key, T value, long timeout, TimeUnit timeUnit) {
        RBucket<T> bucket = redissonClient.getBucket(this.formatKey(key));
        bucket.set(value, timeout, timeUnit);
    }

    /**
     * 获取缓存值
     *
     * @param key 缓存键
     * @param <T> 值的类型
     * @return 缓存值
     */
    public <T> T get(String key) {
        RBucket<T> bucket = redissonClient.getBucket(this.formatKey(key));
        return bucket.get();
    }

    /**
     * 删除缓存值
     *
     * @param key 缓存键
     */
    public void delete(String key) {
        RBucket<?> bucket = redissonClient.getBucket(this.formatKey(key));
        bucket.delete();
    }

    /**
     * 判断缓存是否存在
     *
     * @param key 缓存键
     * @return 是否存在
     */
    public boolean exists(String key) {
        RBucket<?> bucket = redissonClient.getBucket(this.formatKey(key));
        return bucket.isExists();
    }

    // 操作 RMap 的方法

    /**
     * 获取 RMap 对象
     *
     * @param key 缓存键
     * @param <K> 键的类型
     * @param <V> 值的类型
     * @return RMap 对象
     */
    public <K, V> RMap<K, V> getMap(String key) {
        return redissonClient.getMap(this.formatKey(key));
    }

    /**
     * 向 Map 中添加键值对
     *
     * @param key      缓存键
     * @param mapKey   Map 的键
     * @param mapValue Map 的值
     * @param <K>      Map 键的类型
     * @param <V>      Map 值的类型
     */
    public <K, V> void putToMap(String key, K mapKey, V mapValue) {
        RMap<K, V> map = redissonClient.getMap(this.formatKey(key));
        map.put(mapKey, mapValue);
    }

    /**
     * 从 Map 中获取值
     *
     * @param key    缓存键
     * @param mapKey Map 的键
     * @param <K>    Map 键的类型
     * @param <V>    Map 值的类型
     * @return Map 中的值
     */
    public <K, V> V getFromMap(String key, K mapKey) {
        RMap<K, V> map = redissonClient.getMap(this.formatKey(key));
        return map.get(mapKey);
    }

    /**
     * 从 Map 中移除键值对
     *
     * @param key    缓存键
     * @param mapKey Map 的键
     * @param <K>    Map 键的类型
     * @param <V>    Map 值的类型
     */
    public <K, V> void removeFromMap(String key, K mapKey) {
        RMap<K, V> map = redissonClient.getMap(this.formatKey(key));
        map.remove(mapKey);
    }

    /**
     * 设置 Map 的过期时间
     *
     * @param key      缓存键
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     */
    public void expireMap(String key, long timeout, TimeUnit timeUnit) {
        RMap<?, ?> map = redissonClient.getMap(this.formatKey(key));
        map.expire(timeout, timeUnit);
    }

    // 操作 RList 的方法

    /**
     * 获取 RList 对象
     *
     * @param key 缓存键
     * @param <T> 列表元素的类型
     * @return RList 对象
     */
    public <T> RList<T> getList(String key) {
        return redissonClient.getList(this.formatKey(key));
    }

    /**
     * 向 List 中添加元素
     *
     * @param key     缓存键
     * @param element 要添加的元素
     * @param <T>     列表元素的类型
     */
    public <T> void addToList(String key, T element) {
        RList<T> list = redissonClient.getList(this.formatKey(key));
        list.add(element);
    }

    /**
     * 向 List 中批量添加元素
     *
     * @param key      缓存键
     * @param elements 要添加的元素集合
     * @param <T>      列表元素的类型
     */
    public <T> void addAllToList(String key, Collection<T> elements) {
        RList<T> list = redissonClient.getList(this.formatKey(key));
        list.addAll(elements);
    }

    /**
     * 从 List 中获取指定位置的元素
     *
     * @param key   缓存键
     * @param index 元素的索引
     * @param <T>   列表元素的类型
     * @return 指定位置的元素
     */
    public <T> T getFromList(String key, int index) {
        RList<T> list = redissonClient.getList(this.formatKey(key));
        return list.get(index);
    }

    /**
     * 从 List 中移除指定位置的元素
     *
     * @param key   缓存键
     * @param index 元素的索引
     * @param <T>   列表元素的类型
     * @return 被移除的元素
     */
    public <T> T removeFromList(String key, int index) {
        RList<T> list = redissonClient.getList(this.formatKey(key));
        return list.remove(index);
    }

    /**
     * 设置 List 的过期时间
     *
     * @param key      缓存键
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     */
    public void expireList(String key, long timeout, TimeUnit timeUnit) {
        RList<?> list = redissonClient.getList(this.formatKey(key));
        list.expire(timeout, timeUnit);
    }

    // 操作 RSet 的方法

    /**
     * 获取 RSet 对象
     *
     * @param key 缓存键
     * @param <T> 集合元素的类型
     * @return RSet 对象
     */
    public <T> RSet<T> getSet(String key) {
        return redissonClient.getSet(this.formatKey(key));
    }

    /**
     * 向 Set 中添加元素
     *
     * @param key     缓存键
     * @param element 要添加的元素
     * @param <T>     集合元素的类型
     */
    public <T> void addToSet(String key, T element) {
        RSet<T> set = redissonClient.getSet(this.formatKey(key));
        set.add(element);
    }

    /**
     * 向 Set 中批量添加元素
     *
     * @param key      缓存键
     * @param elements 要添加的元素集合
     * @param <T>      集合元素的类型
     */
    public <T> void addAllToSet(String key, Collection<T> elements) {
        RSet<T> set = redissonClient.getSet(this.formatKey(key));
        set.addAll(elements);
    }

    /**
     * 判断 Set 中是否包含指定元素
     *
     * @param key     缓存键
     * @param element 要检查的元素
     * @param <T>     集合元素的类型
     * @return 是否包含
     */
    public <T> boolean containsInSet(String key, T element) {
        RSet<T> set = redissonClient.getSet(this.formatKey(key));
        return set.contains(element);
    }

    /**
     * 从 Set 中移除指定元素
     *
     * @param key     缓存键
     * @param element 要移除的元素
     * @param <T>     集合元素的类型
     */
    public <T> void removeFromSet(String key, T element) {
        RSet<T> set = redissonClient.getSet(this.formatKey(key));
        set.remove(element);
    }

    /**
     * 设置 Set 的过期时间
     *
     * @param key      缓存键
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     */
    public void expireSet(String key, long timeout, TimeUnit timeUnit) {
        RSet<?> set = redissonClient.getSet(this.formatKey(key));
        set.expire(timeout, timeUnit);
    }

    /**
     * 自增并返回结果（原子操作）
     * @param key Redis键
     * @return 自增后的值
     */
    public long atomicIncrement(String key) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(this.formatKey(key));
        return atomicLong.incrementAndGet();
    }

    /**
     * 自增指定步长并返回结果
     * @param key Redis键
     * @param delta 步长
     * @return 自增后的值
     */
    public long atomicIncrement(String key, long delta) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(this.formatKey(key));
        return atomicLong.addAndGet(delta);
    }

    /**
     * 自减并返回结果
     * @param key Redis键
     * @return 自减后的值
     */
    public long atomicDecrement(String key) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(this.formatKey(key));
        return atomicLong.decrementAndGet();
    }

    /**
     * 自减指定步长并返回结果
     * @param key Redis键
     * @param delta 步长
     * @return 自减后的值
     */
    public long atomicDecrement(String key, long delta) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(this.formatKey(key));
        return atomicLong.addAndGet(-delta);
    }

    /**
     * 设置初始值（如果不存在）
     * @param key Redis键
     * @param initialValue 初始值
     * @return 是否成功设置（true表示键不存在并成功设置，false表示键已存在）
     */
    public boolean atomicTrySet(String key, long initialValue) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(this.formatKey(key));
        return atomicLong.compareAndSet(0, initialValue);
    }

    /**
     * 设置带过期时间的值
     * @param key Redis键
     * @param value 值
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    public void atomicSet(String key, long value, long timeout, TimeUnit unit) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(this.formatKey(key));
        atomicLong.set(value);
        atomicLong.expire(timeout, unit);
    }

    /**
     * 获取Atomic的值
     * @param key Redis键
     */
    public Long getAtomicLong(String key) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(this.formatKey(key));
        return atomicLong.get();
    }

    /**
     * 格式化key(服务名+前缀+key)
     * @param key
     * @return
     */
    private String formatKey(String key) {
        return environment.getProperty("spring.application.name") + ":cache_key:" + key;
    }
}
