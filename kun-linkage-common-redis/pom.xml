<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.kun.linkage</groupId>
        <artifactId>kun-linkage-common</artifactId>
        <version>1.1.0-SNAPSHOT</version>
    </parent>
    <version>${project.parent.version}</version>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>kun-linkage-common-redis</artifactId>
    <name>kun-linkage-common-redis</name>
    <packaging>jar</packaging>

    <dependencies>
        <!-- Redisson -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>