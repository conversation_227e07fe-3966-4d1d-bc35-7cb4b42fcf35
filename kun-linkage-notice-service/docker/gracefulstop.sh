#!/bin/bash

if [ "X$ENABLE_SCRIPT_DEBUG" == "XY" -o "X$PIPELINESTYPE" == "XK8S" ]; then
    set -x
fi
LOCAL_IP=`ifconfig eth0 | grep "inet " | awk '{print $2}' | cut -d: -f2`
for i in {1..3}
do

if [ -n "${NACOS_URL}" ]; then
    code=`curl -I -X PUT --connect-timeout 15  -m 10 -o /dev/null -s -w %{http_code} ${NACOS_URL}/nacos/v1/ns/instance?groupName=COMMON_QA\&clusterName=${NAAS_DATA_CENTER}\&serviceName=${NACOS_APP_NAME}\&ip=${LOCAL_IP}\&port=8080\&weight=0\&enabled=false\&ephemeral=true `
else
    code=`curl -I -X PUT --connect-timeout 15  -m 10 -o /dev/null -s -w %{http_code} http://nacos.yeeverse.cn:8848/nacos/v1/ns/instance?groupName=COMMON_QA\&serviceName=${NACOS_APP_NAME}\&ip=${LOCAL_IP}\&port=8080\&weight=0\&enabled=false\&ephemeral=true `
fi

if [ $code -eq 200 ];then
echo "$code"
echo "success";
break
else
echo "faild";
sleep 3
fi
done

sleep 60