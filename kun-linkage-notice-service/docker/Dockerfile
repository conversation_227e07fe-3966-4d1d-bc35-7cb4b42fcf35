FROM openjdk:8

EXPOSE 8080
ADD {APP_NAME}/{APP_NAME}.jar /apps/kun-linkage/app.jar


RUN  mkdir -p /apps/kun-linkage/log \
    && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone \
    && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

USER root
WORKDIR /apps/kun-linkage
ENV TZ=PRC
ENTRYPOINT ["sh","-c","java -jar  ${JAVA_OPTS}  -Dlog4j2.formatMsgNoLookups=true  -Dserver.port=8080  /apps/kun-linkage/app.jar ${JAVA_OPTS2}"]
