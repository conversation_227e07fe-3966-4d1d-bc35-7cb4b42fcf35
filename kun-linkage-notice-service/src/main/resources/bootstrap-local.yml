server:
  port: 9020

spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ***********:8848
        # 命名空间
        namespace: 724e1c9e-8c3b-46ef-8170-fa656b1ef3a5
        # 服务分组
        group: dev
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 100MB
  redis:
    host: redis.qa.kun
    port: 6379
    database: 0
    timeout: 10000
  shardingsphere:
    enabled: true
    props:
      #是否输出sql
      sql-show: true
      default-data-source-name: ds0
    datasource:
      names: ds0
      ds0:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ********************************************************************************
        username: kcard_dev
        password: kcard_devABC123!
        #特别提示:配置数据库加密 config这个不能忘掉
        filters: stat,wall,config
        use-global-data-source-stat: true
        # 开启解密config.decrypt=true; 公钥:config.decrypt.key
        #connectionProperties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKHGwq7q2RmwuRgKxBypQHw0mYu4BQZ3eMsTrdK8E6igRcxsobUC7uT0SoxIjl1WveWniCASejoQtn/BY6hVKWsCAwEAAQ==
        # 连接池的配置信息
        # 初始化大小，最小空闲连接数，最大活跃数
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
    # 配置分片分表策略
    rules:
      sharding:
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: com.kun.linkage.common.db.entity
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl



springdoc:
  api-docs:
    # 开启api-docs
    enabled: true

kun:
  mail:
    open: true
    length: 6
    host: smtphz.qiye.163.com
    port: 465
    auth: true
    from: <EMAIL>
    password: Z36NY5h7BrtAst5A
    periodOfValidity: 2

otp-notice:
  smsTemplateNo: 001
  emailTemplateNo: 001

sms:
  alibaba:
    accessKeyId: LTAI5tEFiFZjZ3wKemdgMACo
    accessKeySecret: ******************************
    globeEndpoint: dysmsapi.ap-southeast-1.aliyuncs.co
    endpoint: dysmsapi.aliyuncs.com
    signature: test

mail:
  pool:
    uplus:
      host: imap.larksuite.com
      port: 465
      from: smtp.larksuite.com
      account: <EMAIL>
      password: 7te2JWedswJ282LQ
      sslEnable:  true
      sendLimit: 420

vcc:
  otpNotify: http://vcc-boss-svc.default.svc.cluster.local:8080/api/otpNotify

