package com.kun.linkage.notice.config;

import cn.hutool.extra.mail.MailAccount;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * 邮件发送池uplus配置
 * <AUTHOR>
 * @date 2025/6/24
 **/
@Configuration
@ConfigurationProperties(prefix = "mail.pool.uplus")
public class MailPoolUPlusConfig {

    private String host;
    private int port;
    private String from;
    private String account; // 多个账号用逗号分隔
    private String password; // 多个密码用逗号分隔，与账号一一对应
    private boolean sslEnable;
    private int sendLimit; // 单账号每日发送限制

    // 解析配置生成账号列表
    public List<MailAccount> getEmailAccounts() {
        List<MailAccount> accounts = new ArrayList<>();
        String[] accountArray = account.split(",");
        String[] passwordArray = password.split(",");

        if (accountArray.length != passwordArray.length) {
            throw new RuntimeException("邮件账号与密码数量不匹配");
        }

        for (int i = 0; i < accountArray.length; i++) {
            MailAccount emailAccount = new MailAccount();
            emailAccount.setHost(host);
            emailAccount.setPort(port);
            emailAccount.setFrom(from);
            emailAccount.setUser(accountArray[i].trim());
            emailAccount.setPass(passwordArray[i].trim());
            emailAccount.setSslEnable(sslEnable);
            accounts.add(emailAccount);
        }
        return accounts;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isSslEnable() {
        return sslEnable;
    }

    public void setSslEnable(boolean sslEnable) {
        this.sslEnable = sslEnable;
    }

    public int getSendLimit() {
        return sendLimit;
    }

    public void setSendLimit(int sendLimit) {
        this.sendLimit = sendLimit;
    }
}
