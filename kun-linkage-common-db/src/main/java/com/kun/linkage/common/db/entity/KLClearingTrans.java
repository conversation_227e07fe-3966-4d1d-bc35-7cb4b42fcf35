package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 交易清分表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@TableName("kl_clearing_trans")
public class KLClearingTrans implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 清算ID
     */
    @TableId("clearing_id")
    private String clearingId;

    /**
     * 授权流水ID
     */
    @TableField("auth_flow_id")
    private String authFlowId;

    /**
     * Processor
     */
    @TableField("processor")
    private String processor;

    /**
     * 网关清算请求流水号
     */
    @TableField("gateway_request_id")
    private String gatewayRequestId;

    /**
     * 网关清算流水号
     */
    @TableField("gateway_clearing_id")
    private String gatewayClearingId;

    /**
     * 原网关清算流水号
     */
    @TableField("original_gateway_clearing_id")
    private String originalGatewayClearingId;

    /**
     * 请求ID
     */
    @TableField("processor_request_id")
    private String processorRequestId;

    /**
     * processor交易ID
     */
    @TableField("processor_trans_id")
    private String processorTransId;

    /**
     * processor原交易ID
     */
    @TableField("original_processor_trans_id")
    private String originalProcessorTransId;

    /**
     * 商户号
     */
    @TableField("merchant_no")
    private String merchantNo;

    /**
     * 商户名称
     */
    @TableField("merchant_name")
    private String merchantName;

    /**
     * 客户ID
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 状态,如：PENDING:处理中;SUCCESS:成功;FAIL:失败;
     */
    @TableField("status")
    private String status;

    /**
     * MTI
     */
    @TableField("mti")
    private String mti;

    /**
     * Processing code
     */
    @TableField("processing_code")
    private String processingCode;

    /**
     * 系统跟踪审计号
     */
    @TableField("systems_trace_audit_number")
    private String systemsTraceAuditNumber;

    /**
     * 网关卡id
     */
    @TableField("gateway_card_id")
    private String gatewayCardId;

    /**
     * 通道卡id
     */
    @TableField("processor_card_id")
    private String processorCardId;

    /**
     * 发卡方卡id
     */
    @TableField("issuer_card_id")
    private String issuerCardId;

    /**
     * 脱敏卡号
     */
    @TableField("masked_card_no")
    private String maskedCardNo;

    /**
     * 交易类型
     */
    @TableField("trans_type")
    private String transType;

    /**
     * 清算类型
     */
    @TableField("clearing_type")
    private String clearingType;

    /**
     * 卡产品编号
     */
    @TableField("card_product_code")
    private String cardProductCode;

    /**
     * 交易币种
     */
    @TableField("trans_currency")
    private String transCurrency;

    /**
     * 交易币种的小数位数
     */
    @TableField("trans_currency_exponent")
    private Integer transCurrencyExponent;

    /**
     * 交易金额
     */
    @TableField("trans_amount")
    private BigDecimal transAmount;

    /**
     * 交易手续费
     */
    @TableField("trans_fee")
    private BigDecimal transFee;

    /**
     * 持卡人账单币种
     */
    @TableField("cardholder_billing_currency")
    private String cardholderBillingCurrency;

    /**
     * 持卡人账单币种的小数位数
     */
    @TableField("cardholder_currency_exponent")
    private Integer cardholderCurrencyExponent;

    /**
     * 持卡人账单金额(不含markup)
     */
    @TableField("cardholder_billing_amount")
    private BigDecimal cardholderBillingAmount;

    /**
     * 持卡人账单金额(含markup)
     */
    @TableField("cardholder_markup_billing_amount")
    private BigDecimal cardholderMarkupBillingAmount;

    /**
     * markup费率
     */
    @TableField("markup_rate")
    private BigDecimal markupRate;

    /**
     * markup金额
     */
    @TableField("markup_amount")
    private BigDecimal markupAmount;

    /**
     * POS输入方式
     */
    @TableField("pos_entry_mode")
    private String posEntryMode;

    /**
     * 交易发生地时间
     */
    @TableField("transaction_local_datetime")
    private String transactionLocalDatetime;

    /**
     * 持卡人账单币种与交易币种的汇率
     */
    @TableField("conversion_rate_cardholder_billing")
    private BigDecimal conversionRateCardholderBilling;

    /**
     * 授权码
     */
    @TableField("approve_code")
    private String approveCode;

    /**
     * 参考号
     */
    @TableField("acquire_reference_no")
    private String acquireReferenceNo;

    /**
     * 收单商户名称
     */
    @TableField("card_acceptor_name")
    private String cardAcceptorName;

    /**
     * 收单商户号
     */
    @TableField("card_acceptor_id")
    private String cardAcceptorId;

    /**
     * 收单商户终端号
     */
    @TableField("card_acceptor_tid")
    private String cardAcceptorTid;

    /**
     * 收单商户国家代码
     */
    @TableField("card_acceptor_country_code")
    private String cardAcceptorCountryCode;

    /**
     * 收单商户邮政编码
     */
    @TableField("card_acceptor_postal_code")
    private String cardAcceptorPostalCode;

    /**
     * 收单商户地区代码
     */
    @TableField("card_acceptor_region")
    private String cardAcceptorRegion;

    /**
     * 收单商户城市
     */
    @TableField("card_acceptor_city")
    private String cardAcceptorCity;

    /**
     * 收单商户街道地址
     */
    @TableField("card_acceptor_street")
    private String cardAcceptorStreet;

    /**
     * MCC
     */
    @TableField("mcc")
    private String mcc;

    /**
     * processor扩展字段1
     */
    @TableField("processor_ext_1")
    private String processorExt1;

    /**
     * 原KL交易ID
     */
    @TableField("original_auth_flow_id")
    private String originalAuthFlowId;

    /**
     * 原交易processor请求ID
     */
    @TableField("original_processor_request_id")
    private String originalProcessorRequestId;

    /**
     * 原交易时间
     */
    @TableField("original_trans_time")
    private String originalTransTime;

    /**
     * 清分标记
     */
    @TableField("clear_flag")
    private String clearFlag;

    /**
     * 交易日期
     */
    @TableField("trans_date")
    private String transDate;

    /**
     * 清分日期
     */
    @TableField("clearing_date")
    private String clearingDate;

    /**
     * 交易时间
     */
    @TableField("trans_time")
    private Date transTime;

    /**
     * 清算金额
     */
    @TableField("clear_amount")
    private BigDecimal clearAmount;

    /**
     * 清算时间
     */
    @TableField("clear_time")
    private Date clearTime;

    /**
     * IC费用方向
     */
    @TableField("fee_interchange_sign")
    private String feeInterchangeSign;

    /**
     * IC费用金额
     */
    @TableField("fee_interchange_amount")
    private BigDecimal feeInterchangeAmount;

    /**
     * 发卡方费用标记
     */
    @TableField("issuer_charge")
    private String issuerCharge;

    /**
     * National Reimbursement Fee
     */
    @TableField("national_reimb_fee")
    private String nationalReimbFee;

    /**
     * 交换费ID
     */
    @TableField("ing_fee_id")
    private Integer ingFeeId;

    /**
     * 国际费用标记
     */
    @TableField("inter_fee_indicator")
    private String interFeeIndicator;

    /**
     * 收费计划标记
     */
    @TableField("fee_program_indicator")
    private String feeProgramIndicator;

    /**
     * 是否跨境标记
     */
    @TableField("overseas_flag")
    private String overseasFlag;

    /**
     * 汇率日期
     */
    @TableField("conversion_date")
    private String conversionDate;

    /**
     * 撤销标记
     */
    @TableField("reversal_flag")
    private String reversalFlag;

    /**
     * 收单行参考号
     */
    @TableField("arn")
    private String arn;

    /**
     * 收单行BIN
     */
    @TableField("acq_bin")
    private String acqBin;

    /**
     * 用途代码
     */
    @TableField("usage_code")
    private String usageCode;

    /**
     * 原因代码
     */
    @TableField("reason_code")
    private String reasonCode;

    /**
     * 卡组产品ID(VISA产品ID)
     */
    @TableField("card_schema_product_id")
    private String cardSchemaProductId;

    /**
     * 原清分id
     */
    @TableField("original_clearing_id")
    private String originalClearingId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    public String getClearingId() {
        return clearingId;
    }

    public void setClearingId(String clearingId) {
        this.clearingId = clearingId;
    }
    public String getAuthFlowId() {
        return authFlowId;
    }

    public void setAuthFlowId(String authFlowId) {
        this.authFlowId = authFlowId;
    }
    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }
    public String getGatewayRequestId() {
        return gatewayRequestId;
    }

    public void setGatewayRequestId(String gatewayRequestId) {
        this.gatewayRequestId = gatewayRequestId;
    }
    public String getGatewayClearingId() {
        return gatewayClearingId;
    }

    public void setGatewayClearingId(String gatewayClearingId) {
        this.gatewayClearingId = gatewayClearingId;
    }
    public String getOriginalGatewayClearingId() {
        return originalGatewayClearingId;
    }

    public void setOriginalGatewayClearingId(String originalGatewayClearingId) {
        this.originalGatewayClearingId = originalGatewayClearingId;
    }
    public String getProcessorRequestId() {
        return processorRequestId;
    }

    public void setProcessorRequestId(String processorRequestId) {
        this.processorRequestId = processorRequestId;
    }
    public String getProcessorTransId() {
        return processorTransId;
    }

    public void setProcessorTransId(String processorTransId) {
        this.processorTransId = processorTransId;
    }
    public String getOriginalProcessorTransId() {
        return originalProcessorTransId;
    }

    public void setOriginalProcessorTransId(String originalProcessorTransId) {
        this.originalProcessorTransId = originalProcessorTransId;
    }
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }
    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }
    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getMti() {
        return mti;
    }

    public void setMti(String mti) {
        this.mti = mti;
    }
    public String getProcessingCode() {
        return processingCode;
    }

    public void setProcessingCode(String processingCode) {
        this.processingCode = processingCode;
    }
    public String getSystemsTraceAuditNumber() {
        return systemsTraceAuditNumber;
    }

    public void setSystemsTraceAuditNumber(String systemsTraceAuditNumber) {
        this.systemsTraceAuditNumber = systemsTraceAuditNumber;
    }
    public String getGatewayCardId() {
        return gatewayCardId;
    }

    public void setGatewayCardId(String gatewayCardId) {
        this.gatewayCardId = gatewayCardId;
    }
    public String getProcessorCardId() {
        return processorCardId;
    }

    public void setProcessorCardId(String processorCardId) {
        this.processorCardId = processorCardId;
    }
    public String getIssuerCardId() {
        return issuerCardId;
    }

    public void setIssuerCardId(String issuerCardId) {
        this.issuerCardId = issuerCardId;
    }
    public String getMaskedCardNo() {
        return maskedCardNo;
    }

    public void setMaskedCardNo(String maskedCardNo) {
        this.maskedCardNo = maskedCardNo;
    }
    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }
    public String getClearingType() {
        return clearingType;
    }

    public void setClearingType(String clearingType) {
        this.clearingType = clearingType;
    }
    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }
    public String getTransCurrency() {
        return transCurrency;
    }

    public void setTransCurrency(String transCurrency) {
        this.transCurrency = transCurrency;
    }
    public Integer getTransCurrencyExponent() {
        return transCurrencyExponent;
    }

    public void setTransCurrencyExponent(Integer transCurrencyExponent) {
        this.transCurrencyExponent = transCurrencyExponent;
    }
    public BigDecimal getTransAmount() {
        return transAmount;
    }

    public void setTransAmount(BigDecimal transAmount) {
        this.transAmount = transAmount;
    }
    public BigDecimal getTransFee() {
        return transFee;
    }

    public void setTransFee(BigDecimal transFee) {
        this.transFee = transFee;
    }
    public String getCardholderBillingCurrency() {
        return cardholderBillingCurrency;
    }

    public void setCardholderBillingCurrency(String cardholderBillingCurrency) {
        this.cardholderBillingCurrency = cardholderBillingCurrency;
    }
    public Integer getCardholderCurrencyExponent() {
        return cardholderCurrencyExponent;
    }

    public void setCardholderCurrencyExponent(Integer cardholderCurrencyExponent) {
        this.cardholderCurrencyExponent = cardholderCurrencyExponent;
    }
    public BigDecimal getCardholderBillingAmount() {
        return cardholderBillingAmount;
    }

    public void setCardholderBillingAmount(BigDecimal cardholderBillingAmount) {
        this.cardholderBillingAmount = cardholderBillingAmount;
    }
    public BigDecimal getCardholderMarkupBillingAmount() {
        return cardholderMarkupBillingAmount;
    }

    public void setCardholderMarkupBillingAmount(BigDecimal cardholderMarkupBillingAmount) {
        this.cardholderMarkupBillingAmount = cardholderMarkupBillingAmount;
    }
    public BigDecimal getMarkupRate() {
        return markupRate;
    }

    public void setMarkupRate(BigDecimal markupRate) {
        this.markupRate = markupRate;
    }
    public BigDecimal getMarkupAmount() {
        return markupAmount;
    }

    public void setMarkupAmount(BigDecimal markupAmount) {
        this.markupAmount = markupAmount;
    }
    public String getPosEntryMode() {
        return posEntryMode;
    }

    public void setPosEntryMode(String posEntryMode) {
        this.posEntryMode = posEntryMode;
    }
    public String getTransactionLocalDatetime() {
        return transactionLocalDatetime;
    }

    public void setTransactionLocalDatetime(String transactionLocalDatetime) {
        this.transactionLocalDatetime = transactionLocalDatetime;
    }
    public BigDecimal getConversionRateCardholderBilling() {
        return conversionRateCardholderBilling;
    }

    public void setConversionRateCardholderBilling(BigDecimal conversionRateCardholderBilling) {
        this.conversionRateCardholderBilling = conversionRateCardholderBilling;
    }
    public String getApproveCode() {
        return approveCode;
    }

    public void setApproveCode(String approveCode) {
        this.approveCode = approveCode;
    }
    public String getAcquireReferenceNo() {
        return acquireReferenceNo;
    }

    public void setAcquireReferenceNo(String acquireReferenceNo) {
        this.acquireReferenceNo = acquireReferenceNo;
    }
    public String getCardAcceptorName() {
        return cardAcceptorName;
    }

    public void setCardAcceptorName(String cardAcceptorName) {
        this.cardAcceptorName = cardAcceptorName;
    }
    public String getCardAcceptorId() {
        return cardAcceptorId;
    }

    public void setCardAcceptorId(String cardAcceptorId) {
        this.cardAcceptorId = cardAcceptorId;
    }
    public String getCardAcceptorTid() {
        return cardAcceptorTid;
    }

    public void setCardAcceptorTid(String cardAcceptorTid) {
        this.cardAcceptorTid = cardAcceptorTid;
    }
    public String getCardAcceptorCountryCode() {
        return cardAcceptorCountryCode;
    }

    public void setCardAcceptorCountryCode(String cardAcceptorCountryCode) {
        this.cardAcceptorCountryCode = cardAcceptorCountryCode;
    }
    public String getCardAcceptorPostalCode() {
        return cardAcceptorPostalCode;
    }

    public void setCardAcceptorPostalCode(String cardAcceptorPostalCode) {
        this.cardAcceptorPostalCode = cardAcceptorPostalCode;
    }
    public String getCardAcceptorRegion() {
        return cardAcceptorRegion;
    }

    public void setCardAcceptorRegion(String cardAcceptorRegion) {
        this.cardAcceptorRegion = cardAcceptorRegion;
    }
    public String getCardAcceptorCity() {
        return cardAcceptorCity;
    }

    public void setCardAcceptorCity(String cardAcceptorCity) {
        this.cardAcceptorCity = cardAcceptorCity;
    }
    public String getCardAcceptorStreet() {
        return cardAcceptorStreet;
    }

    public void setCardAcceptorStreet(String cardAcceptorStreet) {
        this.cardAcceptorStreet = cardAcceptorStreet;
    }
    public String getMcc() {
        return mcc;
    }

    public void setMcc(String mcc) {
        this.mcc = mcc;
    }
    public String getProcessorExt1() {
        return processorExt1;
    }

    public void setProcessorExt1(String processorExt1) {
        this.processorExt1 = processorExt1;
    }
    public String getOriginalAuthFlowId() {
        return originalAuthFlowId;
    }

    public void setOriginalAuthFlowId(String originalAuthFlowId) {
        this.originalAuthFlowId = originalAuthFlowId;
    }
    public String getOriginalProcessorRequestId() {
        return originalProcessorRequestId;
    }

    public void setOriginalProcessorRequestId(String originalProcessorRequestId) {
        this.originalProcessorRequestId = originalProcessorRequestId;
    }
    public String getOriginalTransTime() {
        return originalTransTime;
    }

    public void setOriginalTransTime(String originalTransTime) {
        this.originalTransTime = originalTransTime;
    }
    public String getClearFlag() {
        return clearFlag;
    }

    public void setClearFlag(String clearFlag) {
        this.clearFlag = clearFlag;
    }
    public String getTransDate() {
        return transDate;
    }

    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }
    public String getClearingDate() {
        return clearingDate;
    }

    public void setClearingDate(String clearingDate) {
        this.clearingDate = clearingDate;
    }
    public Date getTransTime() {
        return transTime;
    }

    public void setTransTime(Date transTime) {
        this.transTime = transTime;
    }
    public BigDecimal getClearAmount() {
        return clearAmount;
    }

    public void setClearAmount(BigDecimal clearAmount) {
        this.clearAmount = clearAmount;
    }
    public Date getClearTime() {
        return clearTime;
    }

    public void setClearTime(Date clearTime) {
        this.clearTime = clearTime;
    }
    public String getFeeInterchangeSign() {
        return feeInterchangeSign;
    }

    public void setFeeInterchangeSign(String feeInterchangeSign) {
        this.feeInterchangeSign = feeInterchangeSign;
    }
    public BigDecimal getFeeInterchangeAmount() {
        return feeInterchangeAmount;
    }

    public void setFeeInterchangeAmount(BigDecimal feeInterchangeAmount) {
        this.feeInterchangeAmount = feeInterchangeAmount;
    }
    public String getIssuerCharge() {
        return issuerCharge;
    }

    public void setIssuerCharge(String issuerCharge) {
        this.issuerCharge = issuerCharge;
    }
    public String getNationalReimbFee() {
        return nationalReimbFee;
    }

    public void setNationalReimbFee(String nationalReimbFee) {
        this.nationalReimbFee = nationalReimbFee;
    }
    public Integer getIngFeeId() {
        return ingFeeId;
    }

    public void setIngFeeId(Integer ingFeeId) {
        this.ingFeeId = ingFeeId;
    }
    public String getInterFeeIndicator() {
        return interFeeIndicator;
    }

    public void setInterFeeIndicator(String interFeeIndicator) {
        this.interFeeIndicator = interFeeIndicator;
    }
    public String getFeeProgramIndicator() {
        return feeProgramIndicator;
    }

    public void setFeeProgramIndicator(String feeProgramIndicator) {
        this.feeProgramIndicator = feeProgramIndicator;
    }
    public String getOverseasFlag() {
        return overseasFlag;
    }

    public void setOverseasFlag(String overseasFlag) {
        this.overseasFlag = overseasFlag;
    }
    public String getConversionDate() {
        return conversionDate;
    }

    public void setConversionDate(String conversionDate) {
        this.conversionDate = conversionDate;
    }
    public String getReversalFlag() {
        return reversalFlag;
    }

    public void setReversalFlag(String reversalFlag) {
        this.reversalFlag = reversalFlag;
    }
    public String getArn() {
        return arn;
    }

    public void setArn(String arn) {
        this.arn = arn;
    }
    public String getAcqBin() {
        return acqBin;
    }

    public void setAcqBin(String acqBin) {
        this.acqBin = acqBin;
    }
    public String getUsageCode() {
        return usageCode;
    }

    public void setUsageCode(String usageCode) {
        this.usageCode = usageCode;
    }
    public String getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }
    public String getCardSchemaProductId() {
        return cardSchemaProductId;
    }

    public void setCardSchemaProductId(String cardSchemaProductId) {
        this.cardSchemaProductId = cardSchemaProductId;
    }
    public String getOriginalClearingId() {
        return originalClearingId;
    }

    public void setOriginalClearingId(String originalClearingId) {
        this.originalClearingId = originalClearingId;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "KLClearingTrans{" +
            "clearingId=" + clearingId +
            ", authFlowId=" + authFlowId +
            ", processor=" + processor +
            ", gatewayRequestId=" + gatewayRequestId +
            ", gatewayClearingId=" + gatewayClearingId +
            ", originalGatewayClearingId=" + originalGatewayClearingId +
            ", processorRequestId=" + processorRequestId +
            ", processorTransId=" + processorTransId +
            ", originalProcessorTransId=" + originalProcessorTransId +
            ", merchantNo=" + merchantNo +
            ", merchantName=" + merchantName +
            ", customerId=" + customerId +
            ", status=" + status +
            ", mti=" + mti +
            ", processingCode=" + processingCode +
            ", systemsTraceAuditNumber=" + systemsTraceAuditNumber +
            ", gatewayCardId=" + gatewayCardId +
            ", processorCardId=" + processorCardId +
            ", issuerCardId=" + issuerCardId +
            ", maskedCardNo=" + maskedCardNo +
            ", transType=" + transType +
            ", clearingType=" + clearingType +
            ", cardProductCode=" + cardProductCode +
            ", transCurrency=" + transCurrency +
            ", transCurrencyExponent=" + transCurrencyExponent +
            ", transAmount=" + transAmount +
            ", transFee=" + transFee +
            ", cardholderBillingCurrency=" + cardholderBillingCurrency +
            ", cardholderCurrencyExponent=" + cardholderCurrencyExponent +
            ", cardholderBillingAmount=" + cardholderBillingAmount +
            ", cardholderMarkupBillingAmount=" + cardholderMarkupBillingAmount +
            ", markupRate=" + markupRate +
            ", markupAmount=" + markupAmount +
            ", posEntryMode=" + posEntryMode +
            ", transactionLocalDatetime=" + transactionLocalDatetime +
            ", conversionRateCardholderBilling=" + conversionRateCardholderBilling +
            ", approveCode=" + approveCode +
            ", acquireReferenceNo=" + acquireReferenceNo +
            ", cardAcceptorName=" + cardAcceptorName +
            ", cardAcceptorId=" + cardAcceptorId +
            ", cardAcceptorTid=" + cardAcceptorTid +
            ", cardAcceptorCountryCode=" + cardAcceptorCountryCode +
            ", cardAcceptorPostalCode=" + cardAcceptorPostalCode +
            ", cardAcceptorRegion=" + cardAcceptorRegion +
            ", cardAcceptorCity=" + cardAcceptorCity +
            ", cardAcceptorStreet=" + cardAcceptorStreet +
            ", mcc=" + mcc +
            ", processorExt1=" + processorExt1 +
            ", originalAuthFlowId=" + originalAuthFlowId +
            ", originalProcessorRequestId=" + originalProcessorRequestId +
            ", originalTransTime=" + originalTransTime +
            ", clearFlag=" + clearFlag +
            ", transDate=" + transDate +
            ", clearingDate=" + clearingDate +
            ", transTime=" + transTime +
            ", clearAmount=" + clearAmount +
            ", clearTime=" + clearTime +
            ", feeInterchangeSign=" + feeInterchangeSign +
            ", feeInterchangeAmount=" + feeInterchangeAmount +
            ", issuerCharge=" + issuerCharge +
            ", nationalReimbFee=" + nationalReimbFee +
            ", ingFeeId=" + ingFeeId +
            ", interFeeIndicator=" + interFeeIndicator +
            ", feeProgramIndicator=" + feeProgramIndicator +
            ", overseasFlag=" + overseasFlag +
            ", conversionDate=" + conversionDate +
            ", reversalFlag=" + reversalFlag +
            ", arn=" + arn +
            ", acqBin=" + acqBin +
            ", usageCode=" + usageCode +
            ", reasonCode=" + reasonCode +
            ", cardSchemaProductId=" + cardSchemaProductId +
            ", originalClearingId=" + originalClearingId +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
        "}";
    }
}
