package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 商户记账明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
@TableName("kl_organization_accounting_detail")
public class OrganizationAccountingDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("id")
    private String id;

    /**
     * 业务类型(AUTH:交易;CLEARING:清分)
     */
    @TableField("biz_type")
    private String bizType;

    /**
     * 业务用途(pricipal:本金;fee:手续费)
     */
    @TableField("biz_usage")
    private String bizUsage;

    /**
     * 机构号
     */
    @TableField("organization_no")
    private String organizationNo;

    /**
     * 客户号
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 记账请求流水号
     */
    @TableField("request_no")
    private String requestNo;

    /**
     * 额外请求属性
     */
    @TableField("external_request_attr")
    private String externalRequestAttr;

    /**
     * 卡id
     */
    @TableField("card_id")
    private String cardId;

    /**
     * 业务发生时间
     */
    @TableField("biz_time")
    private Date bizTime;

    /**
     * 业务金额(业务币种)
     */
    @TableField("biz_amount")
    private BigDecimal bizAmount;

    /**
     * 业务币种(业务币种)
     */
    @TableField("biz_currency_code")
    private String bizCurrencyCode;

    /**
     * 业务币种精度(业务币种)
     */
    @TableField("biz_currency_precision")
    private Integer bizCurrencyPrecision;

    /**
     * 业务流水号
     */
    @TableField("biz_id")
    private String bizId;

    /**
     * 换汇汇率
     */
    @TableField("fx_rate")
    private BigDecimal fxRate;

    /**
     * 记账币种
     */
    @TableField("accounting_currency_code")
    private String accountingCurrencyCode;

    /**
     * 记账处理方(KUN,PAYX,CARDHOLDER)
     */
    @TableField("accounting_processor")
    private String accountingProcessor;

    /**
     * 记账币种精度
     */
    @TableField("accounting_currency_precision")
    private Integer accountingCurrencyPrecision;

    /**
     * 记账金额
     */
    @TableField("accounting_amount")
    private BigDecimal accountingAmount;

    /**
     * 记账状态:SUCCESS,FAIL,PENDING
     */
    @TableField("accounting_status")
    private String accountingStatus;

    /**
     * 失败信息
     */
    @TableField("fail_message")
    private String failMessage;

    /**
     * 记账冲正次数
     */
    @TableField("accounting_reversal_count")
    private Integer accountingReversalCount;

    /**
     * 记账冲正状态:SUCCESS,FAIL,PENDING
     */
    @TableField("accounting_reversal_status")
    private String accountingReversalStatus;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 最后一次修改时间
     */
    @TableField("last_modify_time")
    private Date lastModifyTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }
    public String getBizUsage() {
        return bizUsage;
    }

    public void setBizUsage(String bizUsage) {
        this.bizUsage = bizUsage;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }
    public String getExternalRequestAttr() {
        return externalRequestAttr;
    }

    public void setExternalRequestAttr(String externalRequestAttr) {
        this.externalRequestAttr = externalRequestAttr;
    }
    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }
    public Date getBizTime() {
        return bizTime;
    }

    public void setBizTime(Date bizTime) {
        this.bizTime = bizTime;
    }
    public BigDecimal getBizAmount() {
        return bizAmount;
    }

    public void setBizAmount(BigDecimal bizAmount) {
        this.bizAmount = bizAmount;
    }
    public String getBizCurrencyCode() {
        return bizCurrencyCode;
    }

    public void setBizCurrencyCode(String bizCurrencyCode) {
        this.bizCurrencyCode = bizCurrencyCode;
    }
    public Integer getBizCurrencyPrecision() {
        return bizCurrencyPrecision;
    }

    public void setBizCurrencyPrecision(Integer bizCurrencyPrecision) {
        this.bizCurrencyPrecision = bizCurrencyPrecision;
    }
    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }
    public BigDecimal getFxRate() {
        return fxRate;
    }

    public void setFxRate(BigDecimal fxRate) {
        this.fxRate = fxRate;
    }
    public String getAccountingCurrencyCode() {
        return accountingCurrencyCode;
    }

    public void setAccountingCurrencyCode(String accountingCurrencyCode) {
        this.accountingCurrencyCode = accountingCurrencyCode;
    }
    public String getAccountingProcessor() {
        return accountingProcessor;
    }

    public void setAccountingProcessor(String accountingProcessor) {
        this.accountingProcessor = accountingProcessor;
    }
    public Integer getAccountingCurrencyPrecision() {
        return accountingCurrencyPrecision;
    }

    public void setAccountingCurrencyPrecision(Integer accountingCurrencyPrecision) {
        this.accountingCurrencyPrecision = accountingCurrencyPrecision;
    }
    public BigDecimal getAccountingAmount() {
        return accountingAmount;
    }

    public void setAccountingAmount(BigDecimal accountingAmount) {
        this.accountingAmount = accountingAmount;
    }
    public String getAccountingStatus() {
        return accountingStatus;
    }

    public void setAccountingStatus(String accountingStatus) {
        this.accountingStatus = accountingStatus;
    }
    public String getFailMessage() {
        return failMessage;
    }

    public void setFailMessage(String failMessage) {
        this.failMessage = failMessage;
    }
    public Integer getAccountingReversalCount() {
        return accountingReversalCount;
    }

    public void setAccountingReversalCount(Integer accountingReversalCount) {
        this.accountingReversalCount = accountingReversalCount;
    }
    public String getAccountingReversalStatus() {
        return accountingReversalStatus;
    }

    public void setAccountingReversalStatus(String accountingReversalStatus) {
        this.accountingReversalStatus = accountingReversalStatus;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    @Override
    public String toString() {
        return "OrganizationAccountingDetail{" +
            "id=" + id +
            ", bizType=" + bizType +
            ", bizUsage=" + bizUsage +
            ", organizationNo=" + organizationNo +
            ", customerId=" + customerId +
            ", requestNo=" + requestNo +
            ", externalRequestAttr=" + externalRequestAttr +
            ", cardId=" + cardId +
            ", bizTime=" + bizTime +
            ", bizAmount=" + bizAmount +
            ", bizCurrencyCode=" + bizCurrencyCode +
            ", bizCurrencyPrecision=" + bizCurrencyPrecision +
            ", bizId=" + bizId +
            ", fxRate=" + fxRate +
            ", accountingCurrencyCode=" + accountingCurrencyCode +
            ", accountingProcessor=" + accountingProcessor +
            ", accountingCurrencyPrecision=" + accountingCurrencyPrecision +
            ", accountingAmount=" + accountingAmount +
            ", accountingStatus=" + accountingStatus +
            ", failMessage=" + failMessage +
            ", accountingReversalCount=" + accountingReversalCount +
            ", accountingReversalStatus=" + accountingReversalStatus +
            ", createTime=" + createTime +
            ", lastModifyTime=" + lastModifyTime +
        "}";
    }
}
