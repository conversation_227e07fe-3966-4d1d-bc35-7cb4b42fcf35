package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 交易清分异常表,用于记录清分异常的交易,只保留3个月，超过3个月的交易挪入历史表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-03
 */
@TableName("kl_clearing_exception_trans")
public class KLClearingExceptionTrans implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId("id")
    private String id;

    /**
     * 清算ID
     */
    @TableField("clearing_id")
    private String clearingId;

    /**
     * 原清分id
     */
    @TableField("original_clearing_id")
    private String originalClearingId;

    /**
     * 授权流水ID
     */
    @TableField("auth_flow_id")
    private String authFlowId;

    /**
     * Processor
     */
    @TableField("processor")
    private String processor;

    /**
     * 请求ID
     */
    @TableField("processor_request_id")
    private String processorRequestId;

    /**
     * processor交易ID
     */
    @TableField("processor_trans_id")
    private String processorTransId;

    /**
     * processor原交易ID
     */
    @TableField("original_processor_trans_id")
    private String originalProcessorTransId;

    /**
     * 商户号
     */
    @TableField("merchant_no")
    private String merchantNo;

    /**
     * 商户名称
     */
    @TableField("merchant_name")
    private String merchantName;

    /**
     * 客户ID
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 状态,如：PENDING:处理中;SUCCESS:成功;FAIL:失败;
     */
    @TableField("status")
    private String status;

    /**
     * MTI
     */
    @TableField("mti")
    private String mti;

    /**
     * Processing code
     */
    @TableField("processing_code")
    private String processingCode;

    /**
     * 网关卡id
     */
    @TableField("gateway_card_id")
    private String gatewayCardId;

    /**
     * 通道卡id
     */
    @TableField("processor_card_id")
    private String processorCardId;

    /**
     * 发卡方卡id
     */
    @TableField("issuer_card_id")
    private String issuerCardId;

    /**
     * 脱敏卡号
     */
    @TableField("masked_card_no")
    private String maskedCardNo;

    /**
     * 交易类型
     */
    @TableField("trans_type")
    private String transType;

    /**
     * 卡产品编号
     */
    @TableField("card_product_code")
    private String cardProductCode;

    /**
     * 交易币种
     */
    @TableField("trans_currency")
    private String transCurrency;

    /**
     * 交易币种的小数位数
     */
    @TableField("trans_currency_exponent")
    private Integer transCurrencyExponent;

    /**
     * 交易金额
     */
    @TableField("trans_amount")
    private BigDecimal transAmount;

    /**
     * 交易手续费
     */
    @TableField("trans_fee")
    private BigDecimal transFee;

    /**
     * 持卡人账单币种
     */
    @TableField("cardholder_billing_currency")
    private String cardholderBillingCurrency;

    /**
     * 持卡人账单币种的小数位数
     */
    @TableField("cardholder_currency_exponent")
    private Integer cardholderCurrencyExponent;

    /**
     * 持卡人账单金额(不含markup)
     */
    @TableField("cardholder_billing_amount")
    private BigDecimal cardholderBillingAmount;

    /**
     * 持卡人账单金额(含markup)
     */
    @TableField("cardholder_markup_billing_amount")
    private BigDecimal cardholderMarkupBillingAmount;

    /**
     * 持卡人markup费率
     */
    @TableField("markup_rate")
    private BigDecimal markupRate;

    /**
     * markup金额
     */
    @TableField("markup_amount")
    private BigDecimal markupAmount;

    /**
     * POS输入方式
     */
    @TableField("pos_entry_mode")
    private String posEntryMode;

    /**
     * 交易发生地时间
     */
    @TableField("transaction_local_datetime")
    private String transactionLocalDatetime;

    /**
     * 持卡人账单币种与交易币种的汇率
     */
    @TableField("conversion_rate_cardholder_billing")
    private BigDecimal conversionRateCardholderBilling;

    /**
     * 授权码
     */
    @TableField("approve_code")
    private String approveCode;

    /**
     * 参考号
     */
    @TableField("acquire_reference_no")
    private String acquireReferenceNo;

    /**
     * 收单商户名称
     */
    @TableField("card_acceptor_name")
    private String cardAcceptorName;

    /**
     * 收单商户号
     */
    @TableField("card_acceptor_id")
    private String cardAcceptorId;

    /**
     * 收单商户终端号
     */
    @TableField("card_acceptor_tid")
    private String cardAcceptorTid;

    /**
     * 收单商户国家代码
     */
    @TableField("card_acceptor_country_code")
    private String cardAcceptorCountryCode;

    /**
     * 收单商户邮政编码
     */
    @TableField("card_acceptor_postal_code")
    private String cardAcceptorPostalCode;

    /**
     * 收单商户地区代码
     */
    @TableField("card_acceptor_region")
    private String cardAcceptorRegion;

    /**
     * 收单商户城市
     */
    @TableField("card_acceptor_city")
    private String cardAcceptorCity;

    /**
     * 收单商户街道地址
     */
    @TableField("card_acceptor_street")
    private String cardAcceptorStreet;

    /**
     * MCC
     */
    @TableField("mcc")
    private String mcc;

    /**
     * 收单行参考号
     */
    @TableField("arn")
    private String arn;

    /**
     * processor扩展字段1
     */
    @TableField("processor_ext_1")
    private String processorExt1;

    /**
     * 原KL交易ID
     */
    @TableField("original_auth_flow_id")
    private String originalAuthFlowId;

    /**
     * 原交易processor请求ID
     */
    @TableField("original_processor_request_id")
    private String originalProcessorRequestId;

    /**
     * 原交易时间
     */
    @TableField("original_trans_time")
    private String originalTransTime;

    /**
     * 异常类型
     */
    @TableField("exception_type")
    private String exceptionType;

    /**
     * 异常原因
     */
    @TableField("exception_reason")
    private String exceptionReason;

    /**
     * 交易日期
     */
    @TableField("trans_date")
    private String transDate;

    /**
     * 交易时间
     */
    @TableField("trans_time")
    private Date transTime;

    /**
     * 清分日期
     */
    @TableField("clearing_date")
    private String clearingDate;

    /**
     * 清算金额
     */
    @TableField("clear_amount")
    private BigDecimal clearAmount;

    /**
     * 处理标记, Y:已处理;N:未处理
     */
    @TableField("process_flag")
    private String processFlag;

    /**
     * 处理时间
     */
    @TableField("process_time")
    private Date processTime;

    /**
     * 处理人
     */
    @TableField("process_by")
    private String processBy;

    /**
     * 处理备注
     */
    @TableField("process_remark")
    private String processRemark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getClearingId() {
        return clearingId;
    }

    public void setClearingId(String clearingId) {
        this.clearingId = clearingId;
    }
    public String getOriginalClearingId() {
        return originalClearingId;
    }

    public void setOriginalClearingId(String originalClearingId) {
        this.originalClearingId = originalClearingId;
    }
    public String getAuthFlowId() {
        return authFlowId;
    }

    public void setAuthFlowId(String authFlowId) {
        this.authFlowId = authFlowId;
    }
    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }
    public String getProcessorRequestId() {
        return processorRequestId;
    }

    public void setProcessorRequestId(String processorRequestId) {
        this.processorRequestId = processorRequestId;
    }
    public String getProcessorTransId() {
        return processorTransId;
    }

    public void setProcessorTransId(String processorTransId) {
        this.processorTransId = processorTransId;
    }
    public String getOriginalProcessorTransId() {
        return originalProcessorTransId;
    }

    public void setOriginalProcessorTransId(String originalProcessorTransId) {
        this.originalProcessorTransId = originalProcessorTransId;
    }
    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }
    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }
    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getMti() {
        return mti;
    }

    public void setMti(String mti) {
        this.mti = mti;
    }
    public String getProcessingCode() {
        return processingCode;
    }

    public void setProcessingCode(String processingCode) {
        this.processingCode = processingCode;
    }
    public String getGatewayCardId() {
        return gatewayCardId;
    }

    public void setGatewayCardId(String gatewayCardId) {
        this.gatewayCardId = gatewayCardId;
    }
    public String getProcessorCardId() {
        return processorCardId;
    }

    public void setProcessorCardId(String processorCardId) {
        this.processorCardId = processorCardId;
    }
    public String getIssuerCardId() {
        return issuerCardId;
    }

    public void setIssuerCardId(String issuerCardId) {
        this.issuerCardId = issuerCardId;
    }
    public String getMaskedCardNo() {
        return maskedCardNo;
    }

    public void setMaskedCardNo(String maskedCardNo) {
        this.maskedCardNo = maskedCardNo;
    }
    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }
    public String getCardProductCode() {
        return cardProductCode;
    }

    public void setCardProductCode(String cardProductCode) {
        this.cardProductCode = cardProductCode;
    }
    public String getTransCurrency() {
        return transCurrency;
    }

    public void setTransCurrency(String transCurrency) {
        this.transCurrency = transCurrency;
    }
    public Integer getTransCurrencyExponent() {
        return transCurrencyExponent;
    }

    public void setTransCurrencyExponent(Integer transCurrencyExponent) {
        this.transCurrencyExponent = transCurrencyExponent;
    }
    public BigDecimal getTransAmount() {
        return transAmount;
    }

    public void setTransAmount(BigDecimal transAmount) {
        this.transAmount = transAmount;
    }
    public BigDecimal getTransFee() {
        return transFee;
    }

    public void setTransFee(BigDecimal transFee) {
        this.transFee = transFee;
    }
    public String getCardholderBillingCurrency() {
        return cardholderBillingCurrency;
    }

    public void setCardholderBillingCurrency(String cardholderBillingCurrency) {
        this.cardholderBillingCurrency = cardholderBillingCurrency;
    }
    public Integer getCardholderCurrencyExponent() {
        return cardholderCurrencyExponent;
    }

    public void setCardholderCurrencyExponent(Integer cardholderCurrencyExponent) {
        this.cardholderCurrencyExponent = cardholderCurrencyExponent;
    }
    public BigDecimal getCardholderBillingAmount() {
        return cardholderBillingAmount;
    }

    public void setCardholderBillingAmount(BigDecimal cardholderBillingAmount) {
        this.cardholderBillingAmount = cardholderBillingAmount;
    }
    public BigDecimal getCardholderMarkupBillingAmount() {
        return cardholderMarkupBillingAmount;
    }

    public void setCardholderMarkupBillingAmount(BigDecimal cardholderMarkupBillingAmount) {
        this.cardholderMarkupBillingAmount = cardholderMarkupBillingAmount;
    }
    public BigDecimal getMarkupRate() {
        return markupRate;
    }

    public void setMarkupRate(BigDecimal markupRate) {
        this.markupRate = markupRate;
    }
    public BigDecimal getMarkupAmount() {
        return markupAmount;
    }

    public void setMarkupAmount(BigDecimal markupAmount) {
        this.markupAmount = markupAmount;
    }
    public String getPosEntryMode() {
        return posEntryMode;
    }

    public void setPosEntryMode(String posEntryMode) {
        this.posEntryMode = posEntryMode;
    }
    public String getTransactionLocalDatetime() {
        return transactionLocalDatetime;
    }

    public void setTransactionLocalDatetime(String transactionLocalDatetime) {
        this.transactionLocalDatetime = transactionLocalDatetime;
    }
    public BigDecimal getConversionRateCardholderBilling() {
        return conversionRateCardholderBilling;
    }

    public void setConversionRateCardholderBilling(BigDecimal conversionRateCardholderBilling) {
        this.conversionRateCardholderBilling = conversionRateCardholderBilling;
    }
    public String getApproveCode() {
        return approveCode;
    }

    public void setApproveCode(String approveCode) {
        this.approveCode = approveCode;
    }
    public String getAcquireReferenceNo() {
        return acquireReferenceNo;
    }

    public void setAcquireReferenceNo(String acquireReferenceNo) {
        this.acquireReferenceNo = acquireReferenceNo;
    }
    public String getCardAcceptorName() {
        return cardAcceptorName;
    }

    public void setCardAcceptorName(String cardAcceptorName) {
        this.cardAcceptorName = cardAcceptorName;
    }
    public String getCardAcceptorId() {
        return cardAcceptorId;
    }

    public void setCardAcceptorId(String cardAcceptorId) {
        this.cardAcceptorId = cardAcceptorId;
    }
    public String getCardAcceptorTid() {
        return cardAcceptorTid;
    }

    public void setCardAcceptorTid(String cardAcceptorTid) {
        this.cardAcceptorTid = cardAcceptorTid;
    }
    public String getCardAcceptorCountryCode() {
        return cardAcceptorCountryCode;
    }

    public void setCardAcceptorCountryCode(String cardAcceptorCountryCode) {
        this.cardAcceptorCountryCode = cardAcceptorCountryCode;
    }
    public String getCardAcceptorPostalCode() {
        return cardAcceptorPostalCode;
    }

    public void setCardAcceptorPostalCode(String cardAcceptorPostalCode) {
        this.cardAcceptorPostalCode = cardAcceptorPostalCode;
    }
    public String getCardAcceptorRegion() {
        return cardAcceptorRegion;
    }

    public void setCardAcceptorRegion(String cardAcceptorRegion) {
        this.cardAcceptorRegion = cardAcceptorRegion;
    }
    public String getCardAcceptorCity() {
        return cardAcceptorCity;
    }

    public void setCardAcceptorCity(String cardAcceptorCity) {
        this.cardAcceptorCity = cardAcceptorCity;
    }
    public String getCardAcceptorStreet() {
        return cardAcceptorStreet;
    }

    public void setCardAcceptorStreet(String cardAcceptorStreet) {
        this.cardAcceptorStreet = cardAcceptorStreet;
    }
    public String getMcc() {
        return mcc;
    }

    public void setMcc(String mcc) {
        this.mcc = mcc;
    }
    public String getArn() {
        return arn;
    }

    public void setArn(String arn) {
        this.arn = arn;
    }
    public String getProcessorExt1() {
        return processorExt1;
    }

    public void setProcessorExt1(String processorExt1) {
        this.processorExt1 = processorExt1;
    }
    public String getOriginalAuthFlowId() {
        return originalAuthFlowId;
    }

    public void setOriginalAuthFlowId(String originalAuthFlowId) {
        this.originalAuthFlowId = originalAuthFlowId;
    }
    public String getOriginalProcessorRequestId() {
        return originalProcessorRequestId;
    }

    public void setOriginalProcessorRequestId(String originalProcessorRequestId) {
        this.originalProcessorRequestId = originalProcessorRequestId;
    }
    public String getOriginalTransTime() {
        return originalTransTime;
    }

    public void setOriginalTransTime(String originalTransTime) {
        this.originalTransTime = originalTransTime;
    }
    public String getExceptionType() {
        return exceptionType;
    }

    public void setExceptionType(String exceptionType) {
        this.exceptionType = exceptionType;
    }
    public String getExceptionReason() {
        return exceptionReason;
    }

    public void setExceptionReason(String exceptionReason) {
        this.exceptionReason = exceptionReason;
    }
    public String getTransDate() {
        return transDate;
    }

    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }
    public Date getTransTime() {
        return transTime;
    }

    public void setTransTime(Date transTime) {
        this.transTime = transTime;
    }
    public String getClearingDate() {
        return clearingDate;
    }

    public void setClearingDate(String clearingDate) {
        this.clearingDate = clearingDate;
    }
    public BigDecimal getClearAmount() {
        return clearAmount;
    }

    public void setClearAmount(BigDecimal clearAmount) {
        this.clearAmount = clearAmount;
    }
    public String getProcessFlag() {
        return processFlag;
    }

    public void setProcessFlag(String processFlag) {
        this.processFlag = processFlag;
    }
    public Date getProcessTime() {
        return processTime;
    }

    public void setProcessTime(Date processTime) {
        this.processTime = processTime;
    }
    public String getProcessBy() {
        return processBy;
    }

    public void setProcessBy(String processBy) {
        this.processBy = processBy;
    }
    public String getProcessRemark() {
        return processRemark;
    }

    public void setProcessRemark(String processRemark) {
        this.processRemark = processRemark;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "KLClearingExceptionTrans{" +
            "id=" + id +
            ", clearingId=" + clearingId +
            ", originalClearingId=" + originalClearingId +
            ", authFlowId=" + authFlowId +
            ", processor=" + processor +
            ", processorRequestId=" + processorRequestId +
            ", processorTransId=" + processorTransId +
            ", originalProcessorTransId=" + originalProcessorTransId +
            ", merchantNo=" + merchantNo +
            ", merchantName=" + merchantName +
            ", customerId=" + customerId +
            ", status=" + status +
            ", mti=" + mti +
            ", processingCode=" + processingCode +
            ", gatewayCardId=" + gatewayCardId +
            ", processorCardId=" + processorCardId +
            ", issuerCardId=" + issuerCardId +
            ", maskedCardNo=" + maskedCardNo +
            ", transType=" + transType +
            ", cardProductCode=" + cardProductCode +
            ", transCurrency=" + transCurrency +
            ", transCurrencyExponent=" + transCurrencyExponent +
            ", transAmount=" + transAmount +
            ", transFee=" + transFee +
            ", cardholderBillingCurrency=" + cardholderBillingCurrency +
            ", cardholderCurrencyExponent=" + cardholderCurrencyExponent +
            ", cardholderBillingAmount=" + cardholderBillingAmount +
            ", cardholderMarkupBillingAmount=" + cardholderMarkupBillingAmount +
            ", markupRate=" + markupRate +
            ", markupAmount=" + markupAmount +
            ", posEntryMode=" + posEntryMode +
            ", transactionLocalDatetime=" + transactionLocalDatetime +
            ", conversionRateCardholderBilling=" + conversionRateCardholderBilling +
            ", approveCode=" + approveCode +
            ", acquireReferenceNo=" + acquireReferenceNo +
            ", cardAcceptorName=" + cardAcceptorName +
            ", cardAcceptorId=" + cardAcceptorId +
            ", cardAcceptorTid=" + cardAcceptorTid +
            ", cardAcceptorCountryCode=" + cardAcceptorCountryCode +
            ", cardAcceptorPostalCode=" + cardAcceptorPostalCode +
            ", cardAcceptorRegion=" + cardAcceptorRegion +
            ", cardAcceptorCity=" + cardAcceptorCity +
            ", cardAcceptorStreet=" + cardAcceptorStreet +
            ", mcc=" + mcc +
            ", arn=" + arn +
            ", processorExt1=" + processorExt1 +
            ", originalAuthFlowId=" + originalAuthFlowId +
            ", originalProcessorRequestId=" + originalProcessorRequestId +
            ", originalTransTime=" + originalTransTime +
            ", exceptionType=" + exceptionType +
            ", exceptionReason=" + exceptionReason +
            ", transDate=" + transDate +
            ", transTime=" + transTime +
            ", clearingDate=" + clearingDate +
            ", clearAmount=" + clearAmount +
            ", processFlag=" + processFlag +
            ", processTime=" + processTime +
            ", processBy=" + processBy +
            ", processRemark=" + processRemark +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
        "}";
    }
}
