package com.kun.linkage.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 机构文件记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-02
 */
@TableName("kl_organization_file_record")
public class OrganizationFileRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId("id")
    private String id;

    /**
     * 机构号
     */
    @TableField("organization_no")
    private String organizationNo;

    /**
     * 文件日期
     */
    @TableField("file_date")
    private String fileDate;

    /**
     * 文件类型
     */
    @TableField("file_type")
    private String fileType;

    /**
     * 文件名
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 文件路径及名称
     */
    @TableField("file_path_name")
    private String filePathName;

    /**
     * 文件全路径(外部可直接访问的路径)
     */
    @TableField("file_full_path")
    private String fileFullPath;

    /**
     * 文件生成状态
     */
    @TableField("file_generate_status")
    private String fileGenerateStatus;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 最后一次修改时间
     */
    @TableField("last_modify_time")
    private LocalDateTime lastModifyTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getOrganizationNo() {
        return organizationNo;
    }

    public void setOrganizationNo(String organizationNo) {
        this.organizationNo = organizationNo;
    }
    public String getFileDate() {
        return fileDate;
    }

    public void setFileDate(String fileDate) {
        this.fileDate = fileDate;
    }
    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    public String getFilePathName() {
        return filePathName;
    }

    public void setFilePathName(String filePathName) {
        this.filePathName = filePathName;
    }
    public String getFileFullPath() {
        return fileFullPath;
    }

    public void setFileFullPath(String fileFullPath) {
        this.fileFullPath = fileFullPath;
    }
    public String getFileGenerateStatus() {
        return fileGenerateStatus;
    }

    public void setFileGenerateStatus(String fileGenerateStatus) {
        this.fileGenerateStatus = fileGenerateStatus;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    @Override
    public String toString() {
        return "OrganizationFileRecord{" +
            "id=" + id +
            ", organizationNo=" + organizationNo +
            ", fileDate=" + fileDate +
            ", fileType=" + fileType +
            ", fileName=" + fileName +
            ", filePathName=" + filePathName +
            ", fileFullPath=" + fileFullPath +
            ", fileGenerateStatus=" + fileGenerateStatus +
            ", createTime=" + createTime +
            ", lastModifyTime=" + lastModifyTime +
        "}";
    }
}
