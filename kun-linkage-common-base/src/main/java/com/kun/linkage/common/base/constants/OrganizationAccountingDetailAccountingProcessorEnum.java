package com.kun.linkage.common.base.constants;

public enum OrganizationAccountingDetailAccountingProcessorEnum {

    /**
     * KUN space, for accounting digital currency transactions. @also {@link com.kun.linkage.customer.facade.enums.DeductProcessorEnum#KUN}
     */
    KUN("KUN", "Kun Linkage"),

    /**
     * PAYX space, for accounting fiat currency transactions. @also {@link com.kun.linkage.customer.facade.enums.DeductProcessorEnum#PAYX}
     */
    PAYX("PAYX", "PayX"),

    /**
     * CARDHOLDER space, for accounting cardholder transactions.
     */
    CARDHOLDER("CARDHOLDER", "Card Holder"),
    ;

    private final String value;
    private final String description;

    OrganizationAccountingDetailAccountingProcessorEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static OrganizationAccountingDetailAccountingProcessorEnum fromValue(String value) {
        for (OrganizationAccountingDetailAccountingProcessorEnum processor : OrganizationAccountingDetailAccountingProcessorEnum.values()) {
            if (processor.value.equalsIgnoreCase(value)) {
                return processor;
            }
        }
        return null;
    }
}
