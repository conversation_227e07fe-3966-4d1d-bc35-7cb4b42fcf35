package com.kun.linkage.common.base.constants;

public enum OrganizationAccountingDetailBizUsageEnum {

    /**
     * principal: 本金
     */
    PRINCIPAL("principal", "本金"),

    /**
     * fee: 手续费
     */
    FEE("fee", "手续费");

    private String value;
    private String desc;

    OrganizationAccountingDetailBizUsageEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
