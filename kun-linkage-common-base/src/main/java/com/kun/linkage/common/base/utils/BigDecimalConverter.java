package com.kun.linkage.common.base.utils;

import java.math.BigDecimal;

public class BigDecimalConverter {

    /**
     * @description: 数字转换工具类  69985022 =》9.985022
     * @param: input
     * @return: java.math.BigDecimal
     *
     * <AUTHOR>
     * @since 2025/6/11 11:07
     */
    public static BigDecimal convert(String input) {
        if (input == null || input.length() < 2) {
            throw new IllegalArgumentException("输入格式不合法，至少需要两位数字");
        }

        try {
            int decimalPlaces = Integer.parseInt(input.substring(0, 1));
            String numberStr = input.substring(1);
            BigDecimal number = new BigDecimal(numberStr);
            BigDecimal divisor = BigDecimal.TEN.pow(decimalPlaces);
            return number.divide(divisor);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("输入字符串包含非法数字", e);
        }
    }
}
