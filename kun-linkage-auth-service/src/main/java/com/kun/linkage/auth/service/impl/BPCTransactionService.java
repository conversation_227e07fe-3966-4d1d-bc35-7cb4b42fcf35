package com.kun.linkage.auth.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.kun.common.util.uid.DateUtils;
import com.kun.linkage.account.facade.api.bean.req.AccountChangeBalanceReq;
import com.kun.linkage.account.facade.api.bean.req.AccountReversalReq;
import com.kun.linkage.account.facade.api.bean.res.AccountChangeBalanceRes;
import com.kun.linkage.account.facade.constants.AccountTipConstant;
import com.kun.linkage.account.facade.enums.AccountingActionEnum;
import com.kun.linkage.account.facade.enums.BusinessActionEnum;
import com.kun.linkage.account.facade.enums.BusinessSystemEnum;
import com.kun.linkage.account.facade.enums.BusinessTypeEnum;
import com.kun.linkage.auth.dto.AuthTransactionContextDTO;
import com.kun.linkage.auth.facade.constant.*;
import com.kun.linkage.auth.facade.vo.BaseAuthResponseVO;
import com.kun.linkage.auth.facade.vo.bpc.BPCAuthVO;
import com.kun.linkage.auth.facade.vo.bpc.BPCRequestVO;
import com.kun.linkage.auth.service.AbstractTransactionService;
import com.kun.linkage.auth.service.TransactionService;
import com.kun.linkage.auth.utils.CacheKeyUtil;
import com.kun.linkage.auth.utils.ResponseMessageUtil;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.CommonTipConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.base.enums.DirectionEnum;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.AuthFlow;
import com.kun.linkage.common.db.entity.AuthFlowExt;
import com.kun.linkage.common.db.entity.OrganizationBasicInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;

/**
 * BPC授权交易服务实现
 * <p>
 * 负责BPC渠道的授权交易处理，包括交易落库、授权码生成、状态更新等。
 * 主要功能：
 * 1. 处理BPC授权交易主流程
 * 2. 管理交易状态和金额计算
 * 3. 处理交易失败情况
 * 4. 生成和管理授权码
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
public class BPCTransactionService extends AbstractTransactionService implements TransactionService {

    /**
     * 处理BPC授权交易主流程
     * <p>
     * 包括交易数据落库、授权码生成、状态更新等。主要步骤：
     * 1. 解析请求数据
     * 2. 验证必要参数
     * 3. 处理失败交易
     * 4. 保存交易流水
     * 5. 更新原交易剩余金额
     * 6. 更新交易状态
     *
     * @param authTransactionContextDTO 交易上下文，包含交易请求和响应信息
     * @return 处理结果响应VO，正常返回null（结果已写入上下文）
     * @throws BusinessException 当参数缺失或交易处理失败时抛出
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseAuthResponseVO processTransaction(AuthTransactionContextDTO authTransactionContextDTO) {
        RLock lock = null;
        try {
            // 获取分布式锁，防止并发处理同一卡交易
            String lockKey = CacheKeyUtil.getCardTransactionKey(
                authTransactionContextDTO.getBaseAuthRequestVO().getMID(), authTransactionContextDTO.getBaseAuthRequestVO().getProcessor(), authTransactionContextDTO.getBaseAuthRequestVO().getGatewayCardId());
            lock = super.acquireLock(lockKey);
            if (lock == null) {
                log.warn("获取锁失败，可能存在并发处理同一卡交易，lockKey: {}", lockKey);
                authTransactionContextDTO.getBaseAuthResponseVO().setReturnCode(KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode());
                return authTransactionContextDTO.getBaseAuthResponseVO();
            }
            // 解析BPC请求数据
            BPCRequestVO bpcRequestVO = this.parseBPCRequest(authTransactionContextDTO);

            // 验证必要参数
            this.validateBPCRequest(bpcRequestVO);

            // 处理失败交易
            if (!this.isSuccessfulTransaction(authTransactionContextDTO)) {
                this.saveFailedTransaction(authTransactionContextDTO, bpcRequestVO);
                return authTransactionContextDTO.getBaseAuthResponseVO();
            }

            // 保存交易流水
            AuthFlow authFlow = this.buildAndSaveAuthFlow(authTransactionContextDTO, bpcRequestVO);
            AuthFlowExt authFlowExt = this.buildAndSaveAuthFlowExt(authTransactionContextDTO, authFlow);
            authTransactionContextDTO.setAuthFlow(authFlow);
            authTransactionContextDTO.setAuthFlowExt(authFlowExt);
            try {
                organizationCustomerLimitBizService.checkCustomerLimit(
                    authTransactionContextDTO.getOrganizationBasicInfo().getOrganizationNo(),
                    authTransactionContextDTO.getOrganizationCustomerCardInfo().getCustomerId(),
                    authTransactionContextDTO.getBaseAuthRequestVO().getCardholderMarkupAmount(),
                    authTransactionContextDTO.getBaseAuthRequestVO().getCardholderCurrency());
            } catch (BusinessException e) {
                log.error("持卡人限额检查异常，交易ID: {}, 错误信息: {}",
                    authTransactionContextDTO.getTransactionId(),
                    e.getMessage(),
                    e);
                createErrorResponse(authTransactionContextDTO, e.getErrorCode());
                return authTransactionContextDTO.getBaseAuthResponseVO();
            }

            if (authTransactionContextDTO.getBaseAuthRequestVO().getTransAmount()
                .compareTo(BigDecimal.ZERO) != -1 && this.accountingTransaction(authTransactionContextDTO, bpcRequestVO) != null) {
                // 交易金额不小于0并且记账失败直接返回失败响应
                log.error("记账失败，交易ID: {}", authTransactionContextDTO.getTransactionId());
                return  authTransactionContextDTO.getBaseAuthResponseVO();
            }

            // 更新原交易剩余金额
            //----------- !!!!!!!!从此之后的步骤要么回滚事务要么还原更新金额否则第一笔交易的剩余金额就不对了------- !!!!!!!!!!!!! //
            if (authTransactionContextDTO.getFirstOriginalAuthFlow() != null) {
                updateOriginalRemainingTransAmount(authTransactionContextDTO);
            }

            // 更新交易状态
            updateTransactionStatus(authFlow, authTransactionContextDTO);
            return null;
        } catch (Exception e) {
            // 不做处理，只是为了防止锁异常，出现异常直接抛出
            throw e;
        } finally {
            super.releaseLock(lock);
        }
    }

    /**
     * 交易记账
     *
     * @param authTransactionContextDTO
     * @param bpcRequestVO
     * @return
     */
    private BaseAuthResponseVO accountingTransaction(AuthTransactionContextDTO authTransactionContextDTO,
        BPCRequestVO bpcRequestVO) {
        AuthFlowExt authFlowExt = authTransactionContextDTO.getAuthFlowExt();
        if (authFlowExt == null) {
            log.warn("AuthFlowExt is null, cannot proceed with accounting for transaction ID: {}",
                authTransactionContextDTO.getTransactionId());
            return null;
        }

        boolean accountingMerchantAccount = isFlagEnabled(authFlowExt.getMerchantAccountFlag());
        boolean thirdPartyAuthorizationFlag = isFlagEnabled(authFlowExt.getThirdPartyAuthorizationFlag());
        authTransactionContextDTO.setThirdPartyAuthorizationFlag(thirdPartyAuthorizationFlag);
        boolean accountingCustomerAccount = isFlagEnabled(authFlowExt.getCardholderAccountFlag());

        if (accountingMerchantAccount) {
            log.info("Processing merchant account accounting for transaction ID: {}",
                authTransactionContextDTO.getTransactionId());
            BaseAuthResponseVO baseAuthResponseVO =
                transactionAccountingService.transactionAccounting(authTransactionContextDTO);
            if (baseAuthResponseVO != null) {
                log.error("商户账户记账失败，交易ID: {}, 错误码: {}, 错误信息: {}",
                    authTransactionContextDTO.getTransactionId(),
                    baseAuthResponseVO.getReturnCode(),
                    baseAuthResponseVO.getErrorMessage());
                return createErrorResponse(authTransactionContextDTO,
                    KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
            } else {
                log.info("商户账户记账成功，交易ID: {}", authTransactionContextDTO.getTransactionId());
            }
        }

        if (thirdPartyAuthorizationFlag) {
            log.info("Processing third-party authorization accounting for transaction ID: {}",
                authTransactionContextDTO.getTransactionId());
            partnerAuthWebHookService.partnerAuthBPCWebHook(authTransactionContextDTO, bpcRequestVO);
        }

        if (accountingCustomerAccount) {
            return processCustomerAccountAccounting(authTransactionContextDTO);
        }

        if (!accountingMerchantAccount && !thirdPartyAuthorizationFlag && !accountingCustomerAccount) {
            log.error("配置有误，无法进行记账，transaction ID: {}", authTransactionContextDTO.getTransactionId());
            return createErrorResponse(authTransactionContextDTO,
                KunLinkageAuthResponseCodeConstant.CAN_NOT_BE_ACCOUNTING.getCode());
        }

        return null;
    }

    private boolean isFlagEnabled(Integer flag) {
        return flag != null && flag == 1;
    }

    /**
     * 处理持卡人账户记账
     *
     * @param context
     * @return
     */
    private BaseAuthResponseVO processCustomerAccountAccounting(AuthTransactionContextDTO context) {
        TransactionTypeEnum transactionType = context.getTransactionTypeEnum();

        if (isRefundAccountType(transactionType)) {
            log.warn("退货等到清分记账, transaction ID: {}", context.getTransactionId());
            return null;
        }
        if (isRefundReversalAccountType(transactionType)) {
            log.warn("退货冲账等到清分合并处理, transaction ID: {}", context.getTransactionId());
            return null;
        }
        // 如果是撤销或者冲正交易且原交易不存在则不记账
        if (isVoidOrReversalType(transactionType) && context.getOriginalAuthFlow() == null) {
            log.warn("撤销或冲正交易原交易不存在，不进行记账，transaction ID: {}", context.getTransactionId());
            return null;
        }
        // 交易必须金额大于0才记账
        if (context.getBaseAuthRequestVO().getTransAmount().compareTo(BigDecimal.ZERO) != 1) {
            log.warn("交易金额小于等于0，不进行记账，transaction ID: {}", context.getTransactionId());
            return  null;
        }

        AccountChangeBalanceReq changeBalanceReq = buildChangeBalanceReq(context);
        if (changeBalanceReq == null) {
            return null;
        }

        // 调用账户服务
        Result<AccountChangeBalanceRes> result = accountTransactionFacade.changeBalance(changeBalanceReq);
        log.info("Account change balance result: {}", JSON.toJSONString(result));
        authAccountLogService.saveAccountLog(context, changeBalanceReq, result);

        if (!result.isSuccess()) {
            log.error("持卡人账户记账失败，交易ID: {}, 错误码: {}, 错误信息: {}",
                context.getTransactionId(),
                result.getCode(),
                result.getMessage()
            );
            return handleCardholderAccountingFailure(context, result);
        }

        // 更新交易扩展记录
        authFlowExtService.updateAuthFlowExt(context, changeBalanceReq.getBusinessTransactionNo());

        log.info("持卡人账户记账成功，交易ID: {}", context.getTransactionId());
        return null;
    }

    private boolean isRefundAccountType(TransactionTypeEnum type) {
        return type == TransactionTypeEnum.REFUND;
    }

    private boolean isRefundReversalAccountType(TransactionTypeEnum type) {
        return type == TransactionTypeEnum.REFUND_REVERSAL
            || type == TransactionTypeEnum.REFUND_VOID
            || type == TransactionTypeEnum.REFUND_VOID_REVERSAL;
    }

    /**
     * 构建持卡人账户变更请求
     *
     * @param authTransactionContextDTO
     * @return
     */
    private AccountChangeBalanceReq buildChangeBalanceReq(AuthTransactionContextDTO authTransactionContextDTO) {
        TransactionTypeEnum transactionType = authTransactionContextDTO.getTransactionTypeEnum();
        BusinessActionEnum businessAction = getBusinessAction(transactionType);
        if (businessAction == null) {
            return null;
        }

        AccountChangeBalanceReq req = new AccountChangeBalanceReq();
        req.setBusinessSystem(BusinessSystemEnum.KL.getValue());
        req.setBusinessOrganizationNo(authTransactionContextDTO.getBaseAuthRequestVO().getMID());
        req.setAccountNo(authTransactionContextDTO.getCustomerBasicAccount().getAccountNo());
        req.setBusinessType(BusinessTypeEnum.AUTH.getValue());
        req.setBusinessAction(businessAction.getValue());
        req.setAccountingAction(getAccountingAction(authTransactionContextDTO));

        String requestNo = String.valueOf(uidGenerator.getUID());
        authFlowExtService.updateAuthFlowExt(authTransactionContextDTO, requestNo);

        req.setRequestNo(requestNo);
        req.setBusinessTransactionNo(authTransactionContextDTO.getTransactionId());
        req.setCurrencyCode(authTransactionContextDTO.getBaseAuthRequestVO().getCardholderCurrency());
        req.setAmount(authTransactionContextDTO.getBaseAuthRequestVO().getCardholderMarkupAmount());
        req.setRemark(authTransactionContextDTO.getTransactionId());

        return req;
    }

    private BusinessActionEnum getBusinessAction(TransactionTypeEnum type) {
        switch (type) {
            case AUTHORIZATION:
            case PRE_AUTH:
            case CARD_VERIFICATION:
                return BusinessActionEnum.SALES;
            case TRANSFER_OUT:
                return BusinessActionEnum.AFT;
            case TRANSFER_IN:
                return BusinessActionEnum.OCT;
            case CASH_ADVANCE:
                return BusinessActionEnum.CASHOUT;
            case REFUND:
                return BusinessActionEnum.REFUND;
            case PRE_AUTH_COMPLETION:
                return BusinessActionEnum.PRE_AUTH_COMPLETION;
            default:
                if (isVoidOrReversalType(type)) {
                    return BusinessActionEnum.VOID;
                }
                return null;
        }
    }

    private boolean isVoidOrReversalType(TransactionTypeEnum type) {
        return type.name().contains("VOID") || type.name().contains("REVERSAL");
    }

    private String getAccountingAction(AuthTransactionContextDTO authTransactionContextDTO) {
        return DirectionEnum.CREDIT.equals(authTransactionContextDTO.getTransactionTypeEnum().getDirection())
            ? AccountingActionEnum.CREDIT.name()
            : AccountingActionEnum.DEBIT.name();
    }

    /**
     * 处理持卡人账户记账失败
     *
     * @param authTransactionContextDTO
     * @param result
     * @return
     */
    private BaseAuthResponseVO handleCardholderAccountingFailure(AuthTransactionContextDTO authTransactionContextDTO,
        Result<AccountChangeBalanceRes> result) {
        if (AccountTipConstant.INSUFFICIENT_ACCOUNT_BALANCE.equals(result.getCode())) {
            log.error("持卡人账户余额不足，交易ID: {}", authTransactionContextDTO.getTransactionId());
//            throw new BusinessException(KunLinkageAuthResponseCodeConstant.INSUFFICIENT_AVAILABLE_BALANCE.getCode());
            return createErrorResponse(authTransactionContextDTO, KunLinkageAuthResponseCodeConstant.INSUFFICIENT_AVAILABLE_BALANCE.getCode());
        } else if (CommonTipConstant.REQUEST_TIMEOUT.equals(result.getCode())) {
            log.error("持卡人账户记账请求超时，交易ID: {}", authTransactionContextDTO.getTransactionId());
            // 发送冲账消息到RocketMQ
            try {
                authTransactionContextDTO.getLogContext().setTraceId(tracer.currentSpan().context().traceIdString());
                authTransactionContextDTO.getLogContext().setSpanId(tracer.currentSpan().context().spanIdString());
                // 构建冲账请求
                AccountReversalReq accountReversalReq = new AccountReversalReq();
                accountReversalReq.setBusinessSystem(BusinessSystemEnum.KL.getValue());
                accountReversalReq.setBusinessOrganizationNo(authTransactionContextDTO.getBaseAuthRequestVO().getMID());
                // 冲账请求号不能变
                accountReversalReq.setRequestNo(String.valueOf(uidGenerator.getUID()));
                accountReversalReq.setOriginalBusinessTransactionNo(
                    authTransactionContextDTO.getAuthFlowExt().getCardholderAccountRequestNo()
                );
                accountReversalReq.setOriginalBusinessTransactionNo(authTransactionContextDTO.getAuthFlowExt().getCardholderAccountRequestNo());
                accountReversalReq.setRemark(authTransactionContextDTO.getTransactionId());
                authTransactionContextDTO.setAccountReversalReq(accountReversalReq);
                authTransactionContextDTO.getAuthFlowExt().setCardholderAccountReversalNo(accountReversalReq.getRequestNo());
                AuthFlowExt updatedAuthFlowExt = new AuthFlowExt();
                updatedAuthFlowExt.setCardholderAccountReversalNo(accountReversalReq.getRequestNo());
                int updateRows = authFlowExtService.updateAuthFlowExtByIdAndCreateTime(updatedAuthFlowExt, authTransactionContextDTO.getAuthFlowExt().getAuthFlowId(), authTransactionContextDTO.getAuthFlowExt().getCreateTime());
                log.info("更新交易扩展记录冲账流水号: {}, 影响行数: {}", JSON.toJSONString(authTransactionContextDTO.getAuthFlowExt()), updateRows);
                rocketMqService.asyncSend(
                    MqTopicConstant.AUTH_ACCOUNTING_REVERSAL_TOPIC,
                    authTransactionContextDTO
                );
                log.info("发送冲账消息成功, transactionId: {}",
                    authTransactionContextDTO.getTransactionId());
            } catch (Exception e) {
                log.error("发送冲账消息失败, transactionId: {}, error: {}",
                    authTransactionContextDTO.getTransactionId(),
                    e.getMessage(),
                    e
                );
            }
            return createErrorResponse(authTransactionContextDTO, KunLinkageAuthResponseCodeConstant.TIMEOUT.getCode());
        } else {
            log.error("持卡人账户记账失败，交易ID: {}, 错误码: {}, 错误信息: {}",
                authTransactionContextDTO.getTransactionId(),
                result.getCode(),
                result.getMessage()
            );
            return createErrorResponse(authTransactionContextDTO, KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
        }
    }

    private BaseAuthResponseVO createErrorResponse(AuthTransactionContextDTO authTransactionContextDTO, String errorCode) {
        authTransactionContextDTO.getBaseAuthResponseVO().setReturnCode(errorCode);
        authTransactionContextDTO.getBaseAuthResponseVO().setErrorMessage(
            ResponseMessageUtil.getResponseMsg(authTransactionContextDTO.getBaseAuthResponseVO().getReturnCode(), null,
                i18nMessageService));
        return authTransactionContextDTO.getBaseAuthResponseVO();
    }

    private AuthFlowExt buildAndSaveAuthFlowExt(AuthTransactionContextDTO authTransactionContextDTO, AuthFlow authFlow) {
        OrganizationBasicInfo organizationBasicInfo = authTransactionContextDTO.getOrganizationBasicInfo();
        if (organizationBasicInfo == null) {
            log.warn("OrganizationBasicInfo is null, cannot build AuthFlowExt for transaction ID: {}",
                authTransactionContextDTO.getTransactionId());
            return null;
        }

        AuthFlowExt authFlowExt = new AuthFlowExt();
        authFlowExt.setAuthFlowId(authFlow.getId());
        setOrganizationInfo(authFlowExt, organizationBasicInfo);
        authFlowExt.setCreateTime(DateTimeUtils.getCurrentDateTime());

        authFlowExtService.insertAuthFlowExt(authFlowExt);

        authTransactionContextDTO.setAuthFlowExt(authFlowExt);
        return authFlowExt;
    }

    private void setOrganizationInfo(AuthFlowExt authFlowExt, OrganizationBasicInfo organizationBasicInfo) {
        authFlowExt.setMerchantAccountFlag(organizationBasicInfo.getCheckOrganizationAccountFlag());
        authFlowExt.setCardholderAccountFlag(organizationBasicInfo.getCheckCustomerAccountFlag());
        authFlowExt.setKunAccountFlag(null);
        authFlowExt.setKunMid(organizationBasicInfo.getKunMid());
        authFlowExt.setKunAccountRequestNo(null);
        authFlowExt.setPayxAccountFlag(null);
        authFlowExt.setPayxMid(organizationBasicInfo.getPayxMid());
        authFlowExt.setPayxAccountRequestNo(null);
        authFlowExt.setThirdPartyAuthorizationFlag(organizationBasicInfo.getThirdPartyAuthorizationFlag());
        authFlowExt.setMpcTenantId(organizationBasicInfo.getMpcTenantId());
        authFlowExt.setMpcGroupCode(organizationBasicInfo.getMpcGroupCode());
    }

    /**
     * 解析BPC请求数据
     *
     * @param authTransactionContextDTO 交易上下文
     * @return BPC请求VO对象
     */
    private BPCRequestVO parseBPCRequest(AuthTransactionContextDTO authTransactionContextDTO) {
        return JSON.parseObject(
            JSON.toJSONString(authTransactionContextDTO.getBaseAuthRequestVO().getData()),
            new TypeReference<BPCRequestVO>() {}
        );
    }

    /**
     * 验证BPC请求参数
     *
     * @param bpcRequestVO BPC请求VO对象
     * @throws BusinessException 当必要参数缺失时抛出
     */
    private void validateBPCRequest(BPCRequestVO bpcRequestVO) {
        if (bpcRequestVO.getChannelData() == null) {
            log.info("BPC授权交易请求数据缺失channelData");
            throw new BusinessException(
                KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getMessage(),
                "channelData"
            );
        }
    }

    /**
     * 判断交易是否成功
     *
     * @param authTransactionContextDTO 交易上下文
     * @return true表示交易成功，false表示交易失败
     */
    private boolean isSuccessfulTransaction(AuthTransactionContextDTO authTransactionContextDTO) {
        return StringUtils.equals(
            authTransactionContextDTO.getBaseAuthRequestVO().getErrorCode(),
            KunLinkageAuthResponseCodeConstant.SUCCESS.getCode()
        );
    }

    /**
     * 构建并保存交易流水
     *
     * @param authTransactionContextDTO 交易上下文
     * @param bpcRequestVO BPC请求VO对象
     * @return 保存的交易流水对象
     */
    private AuthFlow buildAndSaveAuthFlow(AuthTransactionContextDTO authTransactionContextDTO, BPCRequestVO bpcRequestVO) {
        AuthFlow authFlow = this.buildAuthFlow(authTransactionContextDTO, bpcRequestVO);
        authFlowService.saveAuthFlow(authFlow);
        return authFlow;
    }

    /**
     * 构建交易流水对象
     *
     * @param authTransactionContextDTO 交易上下文
     * @param bpcRequestVO BPC请求VO对象
     * @return 交易流水对象
     */
    private AuthFlow buildAuthFlow(AuthTransactionContextDTO authTransactionContextDTO, BPCRequestVO bpcRequestVO) {
        AuthFlow authFlow = new AuthFlow();
        setBasicInfo(authFlow, authTransactionContextDTO, bpcRequestVO);
        setAmountInfo(authFlow, authTransactionContextDTO);
        setMerchantInfo(authFlow, authTransactionContextDTO, bpcRequestVO);
        setOtherInfo(authFlow, authTransactionContextDTO, bpcRequestVO);
        return authFlow;
    }

    private void setBasicInfo(AuthFlow authFlow, AuthTransactionContextDTO authTransactionContextDTO, BPCRequestVO bpcRequestVO) {
        authFlow.setId(authTransactionContextDTO.getTransactionId());
        authFlow.setProcessor(authTransactionContextDTO.getBaseAuthRequestVO().getProcessor());
        authFlow.setProcessorRequestId(authTransactionContextDTO.getBaseAuthRequestVO().getRequestId());
        authFlow.setProcessorTransId(authTransactionContextDTO.getBaseAuthRequestVO().getTransId());
        authFlow.setStatus(TransactionStatusEnum.PENDING.getCode());
        authFlow.setMti(bpcRequestVO.getChannelData().getMessageType());
        authFlow.setProcessingCode(bpcRequestVO.getChannelData().getProcessingCode());
        authFlow.setSystemsTraceAuditNumber(authTransactionContextDTO.getBaseAuthRequestVO().getSystemsTraceAuditNumber());
        authFlow.setReleaseFlag(authTransactionContextDTO.getOriginalAuthFlow() == null ? authTransactionContextDTO.getTransactionTypeEnum().getReleaseFlag().getValue(): AuthReleaseFlagEnum.NONE.getValue());
    }

    private void setAmountInfo(AuthFlow authFlow, AuthTransactionContextDTO authTransactionContextDTO) {
        authFlow.setTransCurrency(authTransactionContextDTO.getBaseAuthRequestVO().getTransCurrency());
        authFlow.setTransCurrencyExponent(authTransactionContextDTO.getBaseAuthRequestVO().getTransCurrencyExponent());
        authFlow.setTransAmount(authTransactionContextDTO.getBaseAuthRequestVO().getTransAmount());
        authFlow.setTransFee(BigDecimal.ZERO);
        authFlow.setCardholderBillingCurrency(authTransactionContextDTO.getBaseAuthRequestVO().getCardholderCurrency());
        authFlow.setCardholderCurrencyExponent(authTransactionContextDTO.getBaseAuthRequestVO().getCardholderCurrencyExponent());
        authFlow.setMarkupRate(getMarkupRate(authTransactionContextDTO));
        authFlow.setMarkupAmount(authTransactionContextDTO.getBaseAuthRequestVO().getMarkupAmount());
        authFlow.setCardholderBillingAmount(authTransactionContextDTO.getBaseAuthRequestVO().getCardholderAmount());
        authFlow.setCardholderMarkupBillingAmount(authTransactionContextDTO.getBaseAuthRequestVO().getCardholderMarkupAmount());
    }

    private BigDecimal getMarkupRate(AuthTransactionContextDTO authTransactionContextDTO) {
        if (authTransactionContextDTO.getBaseAuthRequestVO().getMarkupRate() != null) {
            return authTransactionContextDTO.getBaseAuthRequestVO().getMarkupRate();
        }
        if (authTransactionContextDTO.getOriginalAuthFlow() != null) {
            return authTransactionContextDTO.getOriginalAuthFlow().getMarkupRate();
        }
        return BigDecimal.ZERO;
    }

    private void setMerchantInfo(AuthFlow authFlow, AuthTransactionContextDTO authTransactionContextDTO, BPCRequestVO bpcRequestVO) {
        authFlow.setMerchantNo(authTransactionContextDTO.getBaseAuthRequestVO().getMID());
        if (authTransactionContextDTO.getOrganizationBasicInfo() != null) {
            authFlow.setMerchantName(authTransactionContextDTO.getOrganizationBasicInfo().getOrganizationName());
        }

        BPCAuthVO channelData = bpcRequestVO.getChannelData();
        if (channelData.getCardAcceptorNameAndLocation() != null) {
            authFlow.setCardAcceptorName(channelData.getCardAcceptorNameAndLocation().getMerchantName());
            authFlow.setCardAcceptorCountryCode(channelData.getCardAcceptorNameAndLocation().getCountryCode());
            authFlow.setCardAcceptorCity(channelData.getCardAcceptorNameAndLocation().getMerchantCity());
        } else {
            authFlow.setCardAcceptorName(StringUtils.EMPTY);
            authFlow.setCardAcceptorCountryCode(StringUtils.EMPTY);
            authFlow.setCardAcceptorCity(StringUtils.EMPTY);
        }

        authFlow.setCardAcceptorId(channelData.getCardAcceptorIdentificationCode());
        authFlow.setCardAcceptorTid(channelData.getCardAcceptorTerminalIdentification());
        authFlow.setCustomerId(authTransactionContextDTO.getOrganizationCustomerCardInfo().getCustomerId());
        authFlow.setThirdPartyAuthorizationFlag(authTransactionContextDTO.getOrganizationBasicInfo().getThirdPartyAuthorizationFlag());
    }

    private void setOtherInfo(AuthFlow authFlow, AuthTransactionContextDTO authTransactionContextDTO, BPCRequestVO bpcRequestVO) {
        setCardInfo(authFlow, authTransactionContextDTO);
        setTransactionInfo(authFlow, authTransactionContextDTO, bpcRequestVO);
        setRemainingAmounts(authFlow, authTransactionContextDTO);
        setOriginalTransactionInfo(authFlow, authTransactionContextDTO);
        setAccountingInfo(authFlow);
    }

    private void setCardInfo(AuthFlow authFlow, AuthTransactionContextDTO authTransactionContextDTO) {
        authFlow.setGatewayCardId(authTransactionContextDTO.getBaseAuthRequestVO().getGatewayCardId());
        authFlow.setProcessorCardId(authTransactionContextDTO.getBaseAuthRequestVO().getProcessorCardId());
        authFlow.setIssuerCardId(authTransactionContextDTO.getBaseAuthRequestVO().getIssuerCardId());

        if (authTransactionContextDTO.getOrganizationCustomerCardInfo() != null) {
            authFlow.setMaskedCardNo(authTransactionContextDTO.getOrganizationCustomerCardInfo().getMaskedCardNo());
            authFlow.setCardProductCode(authTransactionContextDTO.getOrganizationCustomerCardInfo().getCardProductCode());
        }
    }

    private void setTransactionInfo(AuthFlow authFlow, AuthTransactionContextDTO authTransactionContextDTO, BPCRequestVO bpcRequestVO) {
        authFlow.setTransType(authTransactionContextDTO.getBaseAuthRequestVO().getTransType());
        authFlow.setPosEntryMode(bpcRequestVO.getChannelData().getPointOfServiceDateCode() == null ? null :
            bpcRequestVO.getChannelData().getPointOfServiceDateCode().toPlainText());
        authFlow.setPointPinCode(null);
        authFlow.setPosConditionCode(null);
        authFlow.setTransactionLocalDatetime(bpcRequestVO.getChannelData().getDateTimeLocalTransaction());
        authFlow.setConversionRateCardholderBilling(authTransactionContextDTO.getBaseAuthRequestVO().getConversionRateCardholderBilling());
        authFlow.setApproveCode(generateApproveCode(authTransactionContextDTO, bpcRequestVO.getChannelData()));
        authFlow.setAcquireReferenceNo(bpcRequestVO.getChannelData().getRetrievalReferenceNumber());
        authFlow.setMcc(authTransactionContextDTO.getBaseAuthRequestVO().getMcc());
        authFlow.setProcessorExt1(authTransactionContextDTO.getBaseAuthRequestVO().getProcessorExt1());
        authFlow.setOriginalProcessorTransId(authTransactionContextDTO.getBaseAuthRequestVO().getOriginalTransId());
    }

    private void setRemainingAmounts(AuthFlow authFlow, AuthTransactionContextDTO authTransactionContextDTO) {
        TransactionTypeEnum transactionType = authTransactionContextDTO.getTransactionTypeEnum();
        switch (transactionType){
            case AUTHORIZATION_VOID:
            case PRE_AUTH_VOID:
            case PRE_AUTH_COMPLETION_VOID:
            case CASH_ADVANCE_VOID:
            case REFUND_VOID:
            case TRANSFER_OUT_VOID:
            case TRANSFER_IN_VOID:
            case AUTHORIZATION_REVERSAL:
            case PRE_AUTH_REVERSAL:
            case PRE_AUTH_COMPLETION_REVERSAL:
            case CASH_ADVANCE_REVERSAL:
            case REFUND_REVERSAL:
            case TRANSFER_OUT_REVERSAL:
            case TRANSFER_IN_REVERSAL:
            case AUTHORIZATION_VOID_REVERSAL:
            case PRE_AUTH_VOID_REVERSAL:
            case PRE_AUTH_COMPLETION_VOID_REVERSAL:
            case CASH_ADVANCE_VOID_REVERSAL:
            case REFUND_VOID_REVERSAL:
            case TRANSFER_OUT_VOID_REVERSAL:
            case TRANSFER_IN_VOID_REVERSAL:
            case CARD_VERIFICATION_REVERSAL:
                authFlow.setRemainingTransAmount(BigDecimal.ZERO);
                authFlow.setRemainingBillingAmount(BigDecimal.ZERO);
                authFlow.setRemainingMarkupBillingAmount(BigDecimal.ZERO);
                return;
        }
        boolean hasOriginalAuthFlow = authTransactionContextDTO.getOriginalAuthFlow() != null;

        authFlow.setRemainingTransAmount(calculateRemainingTransAmount(transactionType,
            authTransactionContextDTO.getBaseAuthRequestVO().getTransAmount(), hasOriginalAuthFlow));
        authFlow.setRemainingBillingAmount(calculateRemainingTransAmount(transactionType,
            authTransactionContextDTO.getBaseAuthRequestVO().getCardholderAmount(), hasOriginalAuthFlow));
        authFlow.setRemainingMarkupBillingAmount(calculateRemainingTransAmount(transactionType,
            authTransactionContextDTO.getBaseAuthRequestVO().getCardholderMarkupAmount(), hasOriginalAuthFlow));
    }

    private void setOriginalTransactionInfo(AuthFlow authFlow, AuthTransactionContextDTO authTransactionContextDTO) {
        if (authTransactionContextDTO.getOriginalAuthFlow() != null) {
            authFlow.setOriginalId(authTransactionContextDTO.getOriginalAuthFlow().getId());
            authFlow.setOriginalTransTime(DateUtils.formatDate(
                authTransactionContextDTO.getOriginalAuthFlow().getCreateTime(), "yyyyMMddHHmmss"));
            authFlow.setOriginalProcessorRequestId(authTransactionContextDTO.getOriginalAuthFlow().getProcessorRequestId());
        }
    }

    private void setAccountingInfo(AuthFlow authFlow) {
        authFlow.setClearFlag(ClearFlagEnum.TO_BE_CLEARED.getValue());
        authFlow.setTransAccountingDate(DateUtils.formatDate(new Date(), "yyyyMMdd"));
        authFlow.setCreateTime(DateTimeUtils.getCurrentDateTime());
    }

    /**
     * 更新交易状态
     *
     * @param authFlow 交易流水对象
     * @param authTransactionContextDTO 交易上下文
     */
    private void updateTransactionStatus(AuthFlow authFlow, AuthTransactionContextDTO authTransactionContextDTO) {
        AuthFlow updatedAuthFlow = new AuthFlow();
        updatedAuthFlow.setId(authFlow.getId());
        updatedAuthFlow.setStatus(TransactionStatusEnum.SUCCESS.getCode());
        updatedAuthFlow.setResponseCode(KunLinkageAuthResponseCodeConstant.SUCCESS.getCode());
        updatedAuthFlow.setResponseMsg(ResponseMessageUtil.getResponseMsg(updatedAuthFlow.getResponseCode(), null, i18nMessageService));
        updatedAuthFlow.setUpdateTime(DateTimeUtils.getCurrentDateTime());
        updatedAuthFlow.setTransDoneTime(DateTimeUtils.getCurrentDateTime());

        authFlowService.updateAuthFlow(updatedAuthFlow, authFlow.getId(), authFlow.getCreateTime());

        authTransactionContextDTO.setAuthFlow(authFlow);
        authTransactionContextDTO.getBaseAuthResponseVO()
            .setReturnCode(KunLinkageAuthResponseCodeConstant.SUCCESS.getCode());

        // 发送交易手续费处理消息到MQ
        sendTransactionFeeMessage(authTransactionContextDTO);
    }

    /**
     * 更新原交易剩余金额
     * <p>
     * 根据交易类型和原交易信息更新原交易的剩余金额。主要步骤：
     * 1. 计算交易金额（考虑借贷方向）
     * 2. 根据交易类型判断是否需要限制金额
     * 3. 更新原交易剩余金额
     * 4. 验证更新结果
     *
     * @param authTransactionContextDTO 交易上下文
     * @throws BusinessException 当原交易剩余金额为负或更新失败时抛出
     */
    private void updateOriginalRemainingTransAmount(AuthTransactionContextDTO authTransactionContextDTO) {
        BigDecimal transAmount = calculateAmountDirection(authTransactionContextDTO.getTransactionTypeEnum(), authTransactionContextDTO.getBaseAuthRequestVO().getTransAmount());
        BigDecimal billingAmount = calculateAmountDirection(authTransactionContextDTO.getTransactionTypeEnum(), authTransactionContextDTO.getBaseAuthRequestVO().getCardholderAmount());
        BigDecimal billingAmountWithMarkup = calculateAmountDirection(authTransactionContextDTO.getTransactionTypeEnum(), authTransactionContextDTO.getBaseAuthRequestVO().getCardholderMarkupAmount());
        boolean needLimitAmount = isNeedLimitAmount(authTransactionContextDTO.getTransactionTypeEnum());
        updateRemainingAmount(authTransactionContextDTO, transAmount, billingAmount, billingAmountWithMarkup, needLimitAmount);
    }


    private BigDecimal calculateAmountDirection(TransactionTypeEnum transactionTypeEnum, BigDecimal amount) {
        switch (transactionTypeEnum) {
            case AUTHORIZATION_VOID:
            case PRE_AUTH_VOID:
            case PRE_AUTH_COMPLETION_VOID:
            case CASH_ADVANCE_VOID:
            case REFUND_VOID:
            case TRANSFER_OUT_VOID:
            case TRANSFER_IN_VOID:
            case AUTHORIZATION_REVERSAL:
            case PRE_AUTH_REVERSAL:
            case PRE_AUTH_COMPLETION_REVERSAL:
            case CASH_ADVANCE_REVERSAL:
            case REFUND_REVERSAL:
            case TRANSFER_OUT_REVERSAL:
            case TRANSFER_IN_REVERSAL:
            case CARD_VERIFICATION_REVERSAL:
                return amount.negate();
            case AUTHORIZATION:// 授权追加
            case PRE_AUTH:// 授权追加
            case AUTHORIZATION_VOID_REVERSAL:
            case PRE_AUTH_VOID_REVERSAL:
            case PRE_AUTH_COMPLETION_VOID_REVERSAL:
            case CASH_ADVANCE_VOID_REVERSAL:
            case REFUND_VOID_REVERSAL:
            case TRANSFER_OUT_VOID_REVERSAL:
            case TRANSFER_IN_VOID_REVERSAL:
                return amount;
            default:
                return amount;
        }
    }

    private boolean isNeedLimitAmount(TransactionTypeEnum transactionTypeEnum) {
        if (transactionTypeEnum == TransactionTypeEnum.PRE_AUTH_COMPLETION) {
            return false;
        }
        return isVoidOrReversalType(transactionTypeEnum);
    }

    private void updateRemainingAmount(AuthTransactionContextDTO authTransactionContextDTO,
        BigDecimal transAmount, BigDecimal billingAmount, BigDecimal billingAmountWithMarkup, boolean needLimitAmount) {
        BigDecimal newRemainingTransAmount = calculateNewRemainingAmount(
            authTransactionContextDTO.getFirstOriginalAuthFlow().getRemainingTransAmount(),
            transAmount,
            needLimitAmount
        );
        BigDecimal newRemainingBillingAmount = calculateNewRemainingAmount(
            authTransactionContextDTO.getFirstOriginalAuthFlow().getRemainingBillingAmount(),
            billingAmount,
            needLimitAmount
        );
        BigDecimal newRemainingMarkupBillingAmount = calculateNewRemainingAmount(
            authTransactionContextDTO.getFirstOriginalAuthFlow().getRemainingMarkupBillingAmount(),
            billingAmountWithMarkup,
            needLimitAmount
        );
        authTransactionContextDTO.getFirstOriginalAuthFlow().setRemainingTransAmount(newRemainingTransAmount);
        authTransactionContextDTO.getFirstOriginalAuthFlow().setRemainingBillingAmount(newRemainingBillingAmount);
        authTransactionContextDTO.getFirstOriginalAuthFlow().setRemainingMarkupBillingAmount(newRemainingMarkupBillingAmount);

        int updateOriginal = authFlowExtService.updateRemainingAmountInDb(
            authTransactionContextDTO.getFirstOriginalAuthFlow(),
            transAmount,
            billingAmount,
            billingAmountWithMarkup,
            needLimitAmount
        );

        if (updateOriginal == 0) {
            handleUpdateFailure(authTransactionContextDTO);
        }
    }

    private BigDecimal calculateNewRemainingAmount(BigDecimal currentAmount, BigDecimal transAmount, boolean needLimitAmount) {
        BigDecimal newAmount = currentAmount.subtract(transAmount);
        if (!needLimitAmount && newAmount.compareTo(BigDecimal.ZERO) < 0) {
            return BigDecimal.ZERO;
        }
        return newAmount;
    }

    private void handleUpdateFailure(AuthTransactionContextDTO authTransactionContextDTO) {
        log.info("更新原交易剩余金额失败, 原交易ID: {}, 原交易创建时间: {}",
            authTransactionContextDTO.getFirstOriginalAuthFlow().getId(),
            authTransactionContextDTO.getFirstOriginalAuthFlow().getCreateTime());
        throw new BusinessException(
            KunLinkageAuthResponseCodeConstant.TRANSACTION_AMOUNT_EXCEEDS_LIMIT.getCode()
        );
    }

    /**
     * 计算剩余交易金额
     * <p>
     * 根据交易类型计算剩余交易金额。不同类型的交易有不同的计算规则：
     * 1. 授权类交易：使用原始交易金额
     * 2. 退款类交易：原交易剩余金额减去当前交易金额
     * 3. 撤销类交易：返回0
     *
     * @param transactionTypeEnum 交易类型枚举
     * @param amount 金额
     * @param hasOriginalAuthFlow 是否存在原始授权交易
     * @return 计算后的剩余交易金额
     */
    private BigDecimal calculateRemainingTransAmount(TransactionTypeEnum transactionTypeEnum, BigDecimal amount, boolean hasOriginalAuthFlow) {
        if (isAuthorizationType(transactionTypeEnum)) {
            return hasOriginalAuthFlow ? BigDecimal.ZERO : amount;
        }

        if (isRefundType(transactionTypeEnum)) {
            return amount;
        }

        if (isVoidOrReversalType(transactionTypeEnum)) {
            return BigDecimal.ZERO;
        }

        return BigDecimal.ZERO;
    }

    private boolean isAuthorizationType(TransactionTypeEnum type) {
        return type == TransactionTypeEnum.AUTHORIZATION
            || type == TransactionTypeEnum.PRE_AUTH
            || type == TransactionTypeEnum.PRE_AUTH_COMPLETION
            || type == TransactionTypeEnum.CASH_ADVANCE
            || type == TransactionTypeEnum.TRANSFER_OUT
            || type == TransactionTypeEnum.TRANSFER_IN
            || type == TransactionTypeEnum.CARD_VERIFICATION;
    }

    private boolean isRefundType(TransactionTypeEnum type) {
        return type == TransactionTypeEnum.REFUND;
    }

    /**
     * 保存失败的交易记录
     * <p>
     * 当BPC授权交易失败时，保存交易记录到数据库，并更新交易上下文中的响应信息。
     * 主要步骤：
     * 1. 构建失败交易记录
     * 2. 保存到数据库
     * 3. 更新交易上下文
     *
     * @param authTransactionContextDTO 交易上下文
     * @param bpcRequestVO BPC渠道专用VO
     */
    private void saveFailedTransaction(AuthTransactionContextDTO authTransactionContextDTO, BPCRequestVO bpcRequestVO) {
        AuthFlow authFlow = buildFailedAuthFlow(authTransactionContextDTO, bpcRequestVO);
        authFlowService.saveAuthFlow(authFlow);

        updateFailedTransactionContext(authFlow, authTransactionContextDTO);

        log.info("BPC授权交易失败: {}, 错误码: {}, 错误信息: {}",
            authFlow.getId(),
            authTransactionContextDTO.getBaseAuthRequestVO().getErrorCode(),
            authTransactionContextDTO.getBaseAuthRequestVO().getErrorMessage()
        );
    }

    private AuthFlow buildFailedAuthFlow(AuthTransactionContextDTO authTransactionContextDTO, BPCRequestVO bpcRequestVO) {
        AuthFlow authFlow = new AuthFlow();
        setBasicInfo(authFlow, authTransactionContextDTO, bpcRequestVO);
        setAmountInfo(authFlow, authTransactionContextDTO);
        setMerchantInfo(authFlow, authTransactionContextDTO, bpcRequestVO);
        setOtherInfo(authFlow, authTransactionContextDTO, bpcRequestVO);
        setFailedInfo(authFlow, authTransactionContextDTO);
        return authFlow;
    }

    private void setFailedInfo(AuthFlow authFlow, AuthTransactionContextDTO authTransactionContextDTO) {
        authFlow.setResponseCode(authTransactionContextDTO.getBaseAuthRequestVO().getErrorCode());
        authFlow.setResponseMsg(authTransactionContextDTO.getBaseAuthRequestVO().getErrorMessage());
        authFlow.setUpdateTime(authFlow.getCreateTime());
        authFlow.setTransDoneTime(DateTimeUtils.getCurrentDateTime());
        authFlow.setStatus(TransactionStatusEnum.FAILED.getCode());
    }

    private void updateFailedTransactionContext(AuthFlow authFlow, AuthTransactionContextDTO authTransactionContextDTO) {
        authTransactionContextDTO.setAuthFlow(authFlow);
        authTransactionContextDTO.getBaseAuthResponseVO()
            .setReturnCode(authTransactionContextDTO.getBaseAuthRequestVO().getErrorCode());
        authTransactionContextDTO.getBaseAuthResponseVO()
            .setErrorMessage(authTransactionContextDTO.getBaseAuthRequestVO().getErrorMessage());
    }

    /**
     * 生成授权码
     * <p>
     * 根据交易类型决定是否生成授权码或复用原交易授权码。规则如下：
     * 1. 销售和退款交易：生成新的授权码
     * 2. 撤销交易：复用原交易授权码
     * 3. 其他类型：抛出异常
     *
     * @param authTransactionContextDTO 交易上下文
     * @param bpcAuthVO BPC渠道专用VO
     * @return 授权码
     * @throws BusinessException 当交易类型不支持时抛出
     */
    private String generateApproveCode(AuthTransactionContextDTO authTransactionContextDTO, BPCAuthVO bpcAuthVO) {
        TransactionTypeCatogoryEnum catogoryEnum = authTransactionContextDTO.getTransactionTypeCatogoryEnum();

        if (catogoryEnum == null) {
            return null;
        }

        switch (catogoryEnum) {
            case SALES:
                if (StringUtils.isNotBlank(authTransactionContextDTO.getBaseAuthRequestVO().getOriginalTransId())){
                    // 如果有原交易ID，使用原交易的授权码
                    return authTransactionContextDTO.getOriginalAuthFlow() == null ? null :
                        authTransactionContextDTO.getOriginalAuthFlow().getApproveCode();
                }else {
                    return approveCodeGenerator.generate();
                }
            case REFUND:
                return approveCodeGenerator.generate();
            case REVERSAL:
            case CANCELLATION:
                return authTransactionContextDTO.getOriginalAuthFlow() == null ? null :
                    authTransactionContextDTO.getOriginalAuthFlow().getApproveCode();
            default:
                log.info("未处理的交易类型: {}", catogoryEnum);
                throw new BusinessException(
                    KunLinkageAuthResponseCodeConstant.TRANSACTION_NOT_SUPPORTED.getCode()
                );
        }
    }

    @Override
    public Result<Void> reverseTransactionAccounting(AuthTransactionContextDTO authTransactionContextDTO) {
        try {
            log.info("开始交易冲账, transactionId: {}", authTransactionContextDTO.getTransactionId());
            AccountReversalReq accountReversalReq = authTransactionContextDTO.getAccountReversalReq();
            // 调用账户服务执行冲账
            Result<Void> result = accountTransactionFacade.reversal(accountReversalReq);

            authAccountLogService.saveReversalAccountLog(authTransactionContextDTO, accountReversalReq, result);

            if (!result.isSuccess()) {
                log.error("冲正交易失败, transactionId: {}, error: {}",
                    authTransactionContextDTO.getTransactionId(),
                    result.getMessage()
                );
                return result;
            }

            // 更新交易状态为冲正成功
            log.info("交易冲账成功, transactionId: {}", authTransactionContextDTO.getTransactionId());
            return Result.success();
        } catch (Exception e) {
            log.error("交易冲账异常, transactionId: {}, error: {}",
                authTransactionContextDTO.getTransactionId(),
                e.getMessage(),
                e
            );
            return Result.fail("交易冲账异常: " + e.getMessage());
        }
    }

    /**
     * 发送交易手续费处理消息到MQ
     * <p>
     * 当授权交易成功完成后，异步发送消息到MQ进行手续费处理。
     * 注意：这里发送的是异步处理消息，用于处理一些不影响主交易流程的手续费操作，
     * 比如清算系统的手续费计算、报表生成等。
     * </p>
     *
     * @param authTransactionContextDTO 交易上下文
     */
    private void sendTransactionFeeMessage(AuthTransactionContextDTO authTransactionContextDTO) {
        try {
            // 发送异步消息到MQ进行手续费处理
            rocketMqService.asyncSend(
                MqTopicConstant.AUTH_TRANSACTION_FEE_TOPIC,
                authTransactionContextDTO
            );
            log.info("发送交易手续费处理消息成功, transactionId: {}",
                authTransactionContextDTO.getTransactionId());
        } catch (Exception e) {
            log.error("发送交易手续费处理消息失败, transactionId: {}, error: {}",
                authTransactionContextDTO.getTransactionId(),
                e.getMessage(),
                e
            );
        }
    }
}
