package com.kun.linkage.auth.utils;

import com.kun.linkage.auth.constant.AuthApplicationConstant;
import org.apache.commons.lang3.StringUtils;

public class CacheKeyUtil {

    /**
     * 获取授权交易锁的缓存键
     *
     * @param mid
     * @param processor
     * @param requestId
     * @return
     */
    public static String getAuthTransactionKey(String mid, String processor, String requestId) {
        return StringUtils.joinWith(AuthApplicationConstant.COLON_SYMBOL,
            AuthApplicationConstant.AUTH_TRANSACTION_LOCK_KEY_PREFIX, mid, processor, requestId);
    }

    /**
     * 获取卡片交易锁的缓存键
     *
     * @param mid
     * @param processor
     * @param cardId
     * @return
     */
    public static String getCardTransactionKey(String mid, String processor, String cardId) {
        return StringUtils.joinWith(AuthApplicationConstant.COLON_SYMBOL,
            AuthApplicationConstant.CARD_TRANSACTION_LOCK_KEY_PREFIX, mid, processor, cardId);
    }

    /**
     * 获取商户会计冲正锁的缓存键
     *
     * @param dataId
     * @return
     */
    public static String getMerchantAccountingReversalKey(String dataId) {
        return StringUtils.joinWith(AuthApplicationConstant.COLON_SYMBOL,
            AuthApplicationConstant.MERCHANT_ACCOUNTING_REVERSAL_LOCK_KEY_PREFIX, dataId);
    }
}
