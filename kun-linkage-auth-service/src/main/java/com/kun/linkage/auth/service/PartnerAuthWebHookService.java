package com.kun.linkage.auth.service;

import com.kun.common.util.uid.DateUtils;
import com.kun.linkage.auth.dto.AuthTransactionContextDTO;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.auth.facade.vo.BaseAuthResponseVO;
import com.kun.linkage.auth.facade.vo.bpc.BPCRequestVO;
import com.kun.linkage.auth.facade.vo.webhook.PartnerAuthWebHookRequestVO;
import com.kun.linkage.auth.facade.vo.webhook.PartnerAuthWebHookResponseVO;
import com.kun.linkage.auth.facade.vo.webhook.WebHookResult;
import com.kun.linkage.auth.utils.I18nMessageService;
import com.kun.linkage.auth.utils.ResponseMessageUtil;
import com.kun.linkage.common.base.enums.YesFlagEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class PartnerAuthWebHookService {

    @Resource
    private ExternalWebhookService externalWebhookService;

    /** 国际化消息服务 */
    @Resource
    private I18nMessageService i18nMessageService;

    /**
     * 调用第三方授权回调接口(BPC交易)
     *
     * @param authTransactionContextDTO
     * @param bpcRequestVO
     */
    public BaseAuthResponseVO partnerAuthBPCWebHook(AuthTransactionContextDTO authTransactionContextDTO,
        BPCRequestVO bpcRequestVO) {
        if (authTransactionContextDTO.getTechnicalParams() != null && YesFlagEnum.YES.getNumValue()
            .equals(authTransactionContextDTO.getTechnicalParams().getWebhookEnabled()) && StringUtils.isNotBlank(
            authTransactionContextDTO.getTechnicalParams().getWebhookUrlThirdPartyAuth())) {
            log.info("开始调用第三方授权回调接口，交易ID: {}", authTransactionContextDTO.getTransactionId());
            PartnerAuthWebHookRequestVO requestVO = new PartnerAuthWebHookRequestVO();
            requestVO.setOrganizationNo(authTransactionContextDTO.getBaseAuthRequestVO().getMID());
            requestVO.setTransactionId(authTransactionContextDTO.getTransactionId());
            requestVO.setCardId(authTransactionContextDTO.getBaseAuthRequestVO().getGatewayCardId());
            requestVO.setCustomerId(authTransactionContextDTO.getOrganizationCustomerCardInfo().getCustomerId());
            requestVO.setTransactionDirection(
                authTransactionContextDTO.getTransactionTypeEnum().getDirection().getValue());
            requestVO.setTransactionType(authTransactionContextDTO.getTransactionTypeEnum().getCode());
            requestVO.setTransactionCurrency(authTransactionContextDTO.getBaseAuthRequestVO().getTransCurrency());
            requestVO.setTransactionAmount(authTransactionContextDTO.getBaseAuthRequestVO().getTransAmount());
            requestVO.setCardholderCurrency(authTransactionContextDTO.getBaseAuthRequestVO().getCardholderCurrency());
            requestVO.setCardholderAmount(authTransactionContextDTO.getBaseAuthRequestVO().getCardholderAmount());
            if (authTransactionContextDTO.getMerchantAccountingDetail() != null) {
                requestVO.setRequestCurrency(
                    authTransactionContextDTO.getMerchantAccountingDetail().getAccountingCurrencyCode());
                requestVO.setRequestAmount(
                    authTransactionContextDTO.getMerchantAccountingDetail().getAccountingAmount());
            } else {
                log.warn("未找到商户记账详情，交易ID: {}", authTransactionContextDTO.getTransactionId());
                return createErrorResponse(authTransactionContextDTO,
                    KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
            }
            requestVO.setServicePointCardCode(authTransactionContextDTO.getAuthFlow().getPosEntryMode());
            requestVO.setServicePointPinCode(authTransactionContextDTO.getAuthFlow().getPointPinCode());
            requestVO.setServicePointConditionCode(authTransactionContextDTO.getAuthFlow().getPosConditionCode());
            requestVO.setTransmissionDateTime(
                DateUtils.formatDate(authTransactionContextDTO.getAuthFlow().getCreateTime(), "yyyyMMddHHmmss"));
            requestVO.setReferenceNumber(authTransactionContextDTO.getAuthFlow().getAcquireReferenceNo());
            requestVO.setApprovalCode(authTransactionContextDTO.getAuthFlow().getApproveCode());
            requestVO.setCardExpiryDate(bpcRequestVO.getChannelData().getDateExpiration());
            requestVO.setMerchantType(authTransactionContextDTO.getAuthFlow().getMcc());
            requestVO.setCardAcceptorTerminalCode(authTransactionContextDTO.getAuthFlow().getCardAcceptorTid());
            requestVO.setCardAcceptorIdentification(authTransactionContextDTO.getAuthFlow().getCardAcceptorTid());
            requestVO.setCardAcceptorNameLocation(
                bpcRequestVO.getChannelData().getCardAcceptorNameAndLocation().getCardAcceptorNameLocation());
            requestVO.setNetworkReferenceId(null);
            requestVO.setOriginalTransmissionDateTime(authTransactionContextDTO.getAuthFlow().getOriginalTransTime());
            requestVO.setOriginalTransactionId(authTransactionContextDTO.getAuthFlow().getOriginalId());
            WebHookResult<PartnerAuthWebHookResponseVO> partnerAuthWebHookResponseVOWebHookResult =
                externalWebhookService.callApiByUrl(
                    authTransactionContextDTO.getTechnicalParams().getWebhookUrlThirdPartyAuth(), requestVO, null,
                    PartnerAuthWebHookResponseVO.class);
            if (partnerAuthWebHookResponseVOWebHookResult == null) {
                log.error("第三方授权调用失败，交易ID: {}", authTransactionContextDTO.getTransactionId());
                return createErrorResponse(authTransactionContextDTO,
                    KunLinkageAuthResponseCodeConstant.UNKNOWN_ERROR.getCode());
            }
            if (KunLinkageAuthResponseCodeConstant.SUCCESS.getCode()
                .equals(partnerAuthWebHookResponseVOWebHookResult.getCode())) {
                log.info("第三方授权调用成功，交易ID: {}", authTransactionContextDTO.getTransactionId());
                return null;
            } else {
                log.error("第三方授权调用失败，交易ID: {}, 错误码: {}, 错误信息: {}",
                    authTransactionContextDTO.getTransactionId(), partnerAuthWebHookResponseVOWebHookResult.getCode(),
                    partnerAuthWebHookResponseVOWebHookResult.getMessage());
                return createErrorResponse(authTransactionContextDTO,
                    partnerAuthWebHookResponseVOWebHookResult.getCode());
            }
        } else {
            log.error("第三方授权回调未启用或未配置URL，交易ID: {}, 机构号:{}",
                authTransactionContextDTO.getTransactionId(),
                authTransactionContextDTO.getBaseAuthRequestVO().getMID());
            return createErrorResponse(authTransactionContextDTO,
                KunLinkageAuthResponseCodeConstant.THIRD_PARTY_AUTH_CONFIG_ERROR.getCode());
        }
    }

    private BaseAuthResponseVO createErrorResponse(AuthTransactionContextDTO authTransactionContextDTO,
        String errorCode) {
        authTransactionContextDTO.getBaseAuthResponseVO().setReturnCode(errorCode);
        authTransactionContextDTO.getBaseAuthResponseVO().setErrorMessage(
            ResponseMessageUtil.getResponseMsg(authTransactionContextDTO.getBaseAuthResponseVO().getReturnCode(), null,
                i18nMessageService));
        return authTransactionContextDTO.getBaseAuthResponseVO();
    }
}
