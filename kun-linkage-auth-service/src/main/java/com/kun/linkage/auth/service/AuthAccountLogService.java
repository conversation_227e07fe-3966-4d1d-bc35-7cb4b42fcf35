package com.kun.linkage.auth.service;

import com.alibaba.fastjson.JSON;
import com.kun.common.util.uid.UidGenerator;
import com.kun.linkage.account.facade.api.bean.req.AccountChangeBalanceReq;
import com.kun.linkage.account.facade.api.bean.req.AccountReversalReq;
import com.kun.linkage.account.facade.api.bean.res.AccountChangeBalanceRes;
import com.kun.linkage.auth.dto.AuthTransactionContextDTO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.AuthAccountLog;
import com.kun.linkage.common.db.mapper.AuthAccountLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Slf4j
@Service
public class AuthAccountLogService {

    @Resource
    protected AuthAccountLogMapper authAccountLogMapper;

    @Resource
    protected UidGenerator uidGenerator;

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void saveAccountLog(AuthTransactionContextDTO authTransactionContextDTO,
        AccountChangeBalanceReq changeBalanceReq, Result<AccountChangeBalanceRes> result) {
        AuthAccountLog accountLoglog = new AuthAccountLog();
        accountLoglog.setId(String.valueOf(uidGenerator.getUID()));
        accountLoglog.setAuthFlowId(authTransactionContextDTO.getAuthFlow().getId());
        accountLoglog.setAccountType(changeBalanceReq.getBusinessType());
        accountLoglog.setAccountNo(changeBalanceReq.getAccountNo());
        accountLoglog.setRequestJson(JSON.toJSONString(changeBalanceReq));
        accountLoglog.setRequestNo(changeBalanceReq.getBusinessTransactionNo());
        accountLoglog.setResponseCode(result.getCode());
        accountLoglog.setResponseMessage(result.getMessage());
        accountLoglog.setResponseJson(JSON.toJSONString(result));
        accountLoglog.setCreateTime(DateTimeUtils.getCurrentDateTime());
        accountLoglog.setUpdateTime(accountLoglog.getCreateTime());

        int insertRows = authAccountLogMapper.insert(accountLoglog);
        log.info("保存账户请求记录: {}, 影响行数: {}", JSON.toJSONString(accountLoglog), insertRows);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void saveReversalAccountLog(AuthTransactionContextDTO authTransactionContextDTO,
        AccountReversalReq accountReversalReq, Result<Void> result) {
        AuthAccountLog accountLoglog = new AuthAccountLog();
        accountLoglog.setId(String.valueOf(uidGenerator.getUID()));
        accountLoglog.setAuthFlowId(authTransactionContextDTO.getAuthFlow().getId());
        accountLoglog.setAccountType(authTransactionContextDTO.getCustomerBasicAccount().getAccountType());
        accountLoglog.setAccountNo(authTransactionContextDTO.getCustomerBasicAccount().getAccountNo());
        accountLoglog.setRequestJson(JSON.toJSONString(accountReversalReq));
        accountLoglog.setRequestNo(accountReversalReq.getRequestNo());
        accountLoglog.setResponseCode(result.getCode());
        accountLoglog.setResponseMessage(result.getMessage());
        accountLoglog.setResponseJson(JSON.toJSONString(result));
        accountLoglog.setCreateTime(DateTimeUtils.getCurrentDateTime());
        accountLoglog.setUpdateTime(accountLoglog.getCreateTime());

        int insertRows = authAccountLogMapper.insert(accountLoglog);
        log.info("保存账户请求记录: {}, 影响行数: {}", JSON.toJSONString(log), insertRows);
    }
}
