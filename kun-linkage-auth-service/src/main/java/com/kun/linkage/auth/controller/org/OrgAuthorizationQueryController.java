package com.kun.linkage.auth.controller.org;

import com.kun.linkage.auth.facade.constant.AuthApplicationRequestParamNameConstant;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationInquiryPageVO;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationInquiryReuqestVO;
import com.kun.linkage.auth.service.AuthFlowService;
import com.kun.linkage.auth.utils.I18nMessageService;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "OrgAuthorizationQueryController", description = "授权记录查询")
@RestController
@RequestMapping("/org/authorization")
public class OrgAuthorizationQueryController extends BaseVccBossController {

    @Resource
    private I18nMessageService i18nMessageService;

    @Resource
    private AuthFlowService authFlowService;

    /**
     * 分页查询授权记录
     *
     * @param requestVO
     * @return
     */
    @Operation(description = "分页查询授权记录", summary = "分页查询授权记录")
    @PostMapping("/pageList")
    public Result<PageResult<AuthorizationInquiryPageVO>> pageList(@RequestBody AuthorizationInquiryReuqestVO requestVO) {
        if (StringUtils.isBlank(requestVO.getAuthorizationDateFrom())) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(AuthApplicationRequestParamNameConstant.AUTHORIZATION_DATE_FROM)));
        }
        if (StringUtils.isBlank(requestVO.getAuthorizationDateUntil())) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(AuthApplicationRequestParamNameConstant.AUTHORIZATION_DATE_TO)));
        }
        PageResult<AuthorizationInquiryPageVO> pageList = authFlowService.pageList(requestVO);
        return Result.success(pageList);
    }
}
