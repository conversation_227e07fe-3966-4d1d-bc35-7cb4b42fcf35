package com.kun.linkage.auth.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.uid.DateUtils;
import com.kun.linkage.auth.facade.constant.TransactionStatusEnum;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationInquiryDetailVO;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationInquiryPageVO;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationInquiryReuqestVO;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.AuthFlow;
import com.kun.linkage.common.db.mapper.AuthFlowMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AuthFlowService {

    @Resource
    private AuthFlowMapper authFlowMapper;

    public List<AuthFlow> selectListInOneDay(String processorRequestId, String processor) {
        return authFlowMapper.selectList(
            new LambdaQueryWrapper<AuthFlow>().eq(AuthFlow::getProcessorRequestId, processorRequestId)
                .eq(AuthFlow::getProcessor, processor)
                .gt(AuthFlow::getCreateTime, DateUtils.addDays(DateTimeUtils.getCurrentDateTime(), -1))
                .lt(AuthFlow::getCreateTime, DateTimeUtils.getCurrentDateTime()));
    }

    public AuthFlow selectSuccessOneWithConditionDay(String organizationNo, String originalTransId, String processor,
        String processorCardId, Integer days) {
        return authFlowMapper.selectOne(new LambdaQueryWrapper<AuthFlow>().eq(AuthFlow::getMerchantNo, organizationNo)
            .eq(AuthFlow::getProcessorTransId, originalTransId).eq(AuthFlow::getProcessor, processor)
            .eq(AuthFlow::getProcessorCardId, processorCardId)
            .eq(AuthFlow::getStatus, TransactionStatusEnum.SUCCESS.getCode()).gt(AuthFlow::getCreateTime,
                DateUtils.addDays(DateTimeUtils.getCurrentDateTime(), days == null ? -180 : days))
            .lt(AuthFlow::getCreateTime, DateTimeUtils.getCurrentDateTime()));
    }

    /**
     * 根据其它字段重新查询原交易
     * <p>
     * 用于根据交易关键字段（如商户号、处理方卡ID、处理方等）查询原交易列表。 主要用于冲正交易时，当原交易ID不存在时，通过其他字段匹配原交易。
     *
     * @param organizationNo          商户号
     * @param processorCardId         处理方卡ID
     * @param processor               处理方
     * @param acquireReferenceNo      收单参考号
     * @param systemsTraceAuditNumber 系统跟踪审计号
     * @param processorExt1           处理方扩展字段1
     * @param cardAcceptorId          收单方商户号
     * @param days                    查询的天数范围，负数表示向前查询的天数
     * @return 原交易列表，按创建时间升序排序
     */
    public List<AuthFlow> getOriginalAuthFlowList(String organizationNo, String processorCardId, String processor,
        String acquireReferenceNo, String systemsTraceAuditNumber, String processorExt1, String cardAcceptorId,
        int days) {
        return authFlowMapper.selectList(new LambdaQueryWrapper<AuthFlow>().eq(AuthFlow::getMerchantNo, organizationNo)
            .eq(AuthFlow::getProcessorCardId, processorCardId).eq(AuthFlow::getProcessor, processor)
            .eq(AuthFlow::getAcquireReferenceNo, acquireReferenceNo)
            .eq(AuthFlow::getSystemsTraceAuditNumber, systemsTraceAuditNumber)
            .eq(AuthFlow::getProcessorExt1, processorExt1).eq(AuthFlow::getCardAcceptorId, cardAcceptorId)
            .eq(AuthFlow::getStatus, TransactionStatusEnum.SUCCESS.getCode())
            .gt(AuthFlow::getCreateTime, DateUtils.addDays(DateTimeUtils.getCurrentDateTime(), days))
            .lt(AuthFlow::getCreateTime, DateTimeUtils.getCurrentDateTime()).orderByAsc(AuthFlow::getCreateTime));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public int saveAuthFlow(AuthFlow authFlow) {
        int insertRows = authFlowMapper.insert(authFlow);
        log.info("保存授权交易记录: {}, 影响行数: {}", authFlow, insertRows);
        return insertRows;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public int updateAuthFlow(AuthFlow updatedAuthFlow, String authFlowId, Date createTime) {
        int updateRows = authFlowMapper.update(updatedAuthFlow,
            new LambdaQueryWrapper<AuthFlow>().eq(AuthFlow::getId, authFlowId).eq(AuthFlow::getCreateTime, createTime));
        log.info("更新授权交易记录: {}, 影响行数: {}", JSON.toJSONString(updatedAuthFlow), updateRows);
        return updateRows;
    }

    public void updateAuthFlowToFailAtFinal(AuthFlow authFlow) {
        AuthFlow updateAuthFlow = new AuthFlow();
        updateAuthFlow.setStatus(authFlow.getStatus());
        updateAuthFlow.setResponseCode(authFlow.getResponseCode());
        updateAuthFlow.setResponseMsg(authFlow.getResponseMsg());
        updateAuthFlow.setTransDoneTime(authFlow.getTransDoneTime());
        updateAuthFlow.setUpdateTime(DateTimeUtils.getCurrentDateTime());
        this.updateAuthFlow(updateAuthFlow, authFlow.getId(), authFlow.getCreateTime());
    }

    public PageResult<AuthorizationInquiryPageVO> pageList(AuthorizationInquiryReuqestVO requestVO) {
        LambdaQueryWrapper<AuthFlow> wrapper = Wrappers.lambdaQuery();
        wrapper.ge(AuthFlow::getCreateTime, DateTimeUtils.truncateToSecond(
            DateUtils.parseDate(requestVO.getAuthorizationDateFrom().trim() + " 00:00:00",
                DateUtils.DATETIME_PATTERN)));
        wrapper.le(AuthFlow::getCreateTime, DateTimeUtils.truncateToSecond(
            DateUtils.parseDate(requestVO.getAuthorizationDateUntil().trim() + " 23:59:59",
                DateUtils.DATETIME_PATTERN)));
        if (requestVO.getTransAmountFrom() != null) {
            wrapper.ge(AuthFlow::getTransAmount, requestVO.getTransAmountFrom());
        }
        if (requestVO.getTransAmountTo() != null) {
            wrapper.le(AuthFlow::getTransAmount, requestVO.getTransAmountTo());
        }
        if (requestVO.getMerchantNo() != null) {
            wrapper.eq(AuthFlow::getMerchantNo, requestVO.getMerchantNo());
        }
        if (requestVO.getAcquirerMerchantNo() != null) {
            wrapper.eq(AuthFlow::getCardAcceptorId, requestVO.getAcquirerMerchantNo());
        }
        if (requestVO.getAuthTransId() != null) {
            wrapper.eq(AuthFlow::getId, requestVO.getAuthTransId());
        }
        if (requestVO.getAuthTransType() != null) {
            wrapper.eq(AuthFlow::getTransType, requestVO.getAuthTransType());
        }
        if (requestVO.getOriginalAuthTransId() != null) {
            wrapper.eq(AuthFlow::getOriginalId, requestVO.getOriginalAuthTransId());
        }
        if (requestVO.getAcquirerMerchantMcc() != null) {
            wrapper.eq(AuthFlow::getMcc, requestVO.getAcquirerMerchantMcc());
        }
        if (requestVO.getAcquireReferenceNo() != null) {
            wrapper.eq(AuthFlow::getAcquireReferenceNo, requestVO.getAcquireReferenceNo());
        }
        if (requestVO.getGatewayCardId() != null) {
            wrapper.eq(AuthFlow::getGatewayCardId, requestVO.getGatewayCardId());
        }
        if (requestVO.getAuthStatus() != null) {
            wrapper.eq(AuthFlow::getStatus, requestVO.getAuthStatus());
        }
        if (requestVO.getResponseCode() != null) {
            wrapper.eq(AuthFlow::getResponseCode, requestVO.getResponseCode());
        }
        if (requestVO.getSystemsTraceAuditNumber() != null) {
            wrapper.eq(AuthFlow::getSystemsTraceAuditNumber, requestVO.getSystemsTraceAuditNumber());
        }
        if (requestVO.getApproveCode() != null) {
            wrapper.eq(AuthFlow::getApproveCode, requestVO.getApproveCode());
        }
        if (requestVO.getProcessor() != null) {
            wrapper.eq(AuthFlow::getProcessor, requestVO.getProcessor());
        }
        if (requestVO.getCardNoSuffix() != null) {
            wrapper.eq(AuthFlow::getMaskedCardNo, requestVO.getCardNoSuffix());
        }
        wrapper.orderByDesc(AuthFlow::getCreateTime);
        PageResult<AuthFlow> pageResult = PageHelperUtil.getPage(requestVO, () -> authFlowMapper.selectList(wrapper));
        PageResult<AuthorizationInquiryPageVO> pageVOResult = convertToPageVO(pageResult);
        return pageVOResult;
    }

    private PageResult<AuthorizationInquiryPageVO> convertToPageVO(PageResult<AuthFlow> pageResult) {
        List<AuthorizationInquiryPageVO> list = pageResult.getData().stream().map(authFlow -> {
            AuthorizationInquiryPageVO vo = new AuthorizationInquiryPageVO();
            vo.setAuthTransId(authFlow.getId());
            vo.setProcessor(authFlow.getProcessor());
            vo.setAuthorizationTime(DateUtils.formatDate(authFlow.getCreateTime(), DateUtils.DATETIME_PATTERN));
            vo.setMerchantNo(authFlow.getMerchantNo());
            vo.setMerchantName(authFlow.getMerchantName());
            vo.setGatewayCardId(authFlow.getGatewayCardId());
            vo.setAuthTransType(authFlow.getTransType());
            vo.setTransCurrency(authFlow.getTransCurrency());
            vo.setTransCurrencyExponent(authFlow.getTransCurrencyExponent());
            vo.setTransAmount(authFlow.getTransAmount());
            vo.setCardholderCurrency(authFlow.getCardholderBillingCurrency());
            vo.setCardholderCurrencyExponent(authFlow.getCardholderCurrencyExponent());
            vo.setCardholderAmount(authFlow.getCardholderBillingAmount());
            vo.setCardholderMarkupAmount(authFlow.getCardholderMarkupBillingAmount());
            vo.setAuthStatus(authFlow.getStatus());
            vo.setAcquireReferenceNo(authFlow.getAcquireReferenceNo());
            vo.setApproveCode(authFlow.getApproveCode());
            vo.setProcessorTransId(authFlow.getProcessorTransId());
            vo.setOriginalProcessorTransId(authFlow.getOriginalProcessorTransId());
            vo.setAcquirerMerchantNo(authFlow.getCardAcceptorId());
            vo.setAcquirerMerchantName(authFlow.getCardAcceptorName());
            vo.setOriginalAuthTransId(authFlow.getOriginalId());
            vo.setAcquirerMerchantMcc(authFlow.getMcc());
            vo.setResponseCode(authFlow.getResponseCode());
            vo.setSystemsTraceAuditNumber(authFlow.getSystemsTraceAuditNumber());
            vo.setCardNoSuffix(authFlow.getMaskedCardNo());
            vo.setRemainingTransAmount(authFlow.getRemainingTransAmount());
            vo.setClearFlag(authFlow.getClearFlag());
            if (authFlow.getRemainingTransAmount() != null) {
                vo.setRemainingFrozenAmount(authFlow.getRemainingTransAmount()
                    .subtract(authFlow.getClearAmount() == null ? BigDecimal.ZERO : authFlow.getClearAmount()));
            }
            vo.setAcquirerCountryCode(authFlow.getCardAcceptorCountryCode());
            return vo;
        }).collect(Collectors.toList());
        return new PageResult(list, pageResult.getPageNum(), pageResult.getPageSize(), pageResult.getTotal(),
            pageResult.getExtraInfo());
    }

    public AuthorizationInquiryDetailVO detail(String authTransId, String authorizationTime) {
        LambdaQueryWrapper<AuthFlow> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AuthFlow::getId, authTransId);
        wrapper.eq(AuthFlow::getCreateTime,
            DateTimeUtils.truncateToSecond(DateUtils.parseDate(authorizationTime, DateUtils.DATETIME_PATTERN)));
        AuthFlow authFlow = authFlowMapper.selectOne(wrapper);
        if (authFlow != null) {
            AuthorizationInquiryDetailVO detailVO = new AuthorizationInquiryDetailVO();
            detailVO.setAuthTransId(authFlow.getId());
            detailVO.setProcessor(authFlow.getProcessor());
            detailVO.setAuthorizationTime(DateUtils.formatDate(authFlow.getCreateTime(), DateUtils.DATETIME_PATTERN));
            detailVO.setMerchantNo(authFlow.getMerchantNo());
            detailVO.setMerchantName(authFlow.getMerchantName());
            detailVO.setGatewayCardId(authFlow.getGatewayCardId());
            detailVO.setAuthTransType(authFlow.getTransType());
            detailVO.setTransCurrency(authFlow.getTransCurrency());
            detailVO.setTransCurrencyExponent(authFlow.getTransCurrencyExponent());
            detailVO.setTransAmount(authFlow.getTransAmount());
            detailVO.setCardholderCurrency(authFlow.getCardholderBillingCurrency());
            detailVO.setCardholderCurrencyExponent(authFlow.getCardholderCurrencyExponent());
            detailVO.setCardholderAmount(authFlow.getCardholderBillingAmount());
            detailVO.setCardholderMarkupAmount(authFlow.getCardholderMarkupBillingAmount());
            detailVO.setAuthStatus(authFlow.getStatus());
            detailVO.setAcquireReferenceNo(authFlow.getAcquireReferenceNo());
            detailVO.setApproveCode(authFlow.getApproveCode());
            detailVO.setProcessorTransId(authFlow.getProcessorTransId());
            detailVO.setOriginalProcessorTransId(authFlow.getOriginalProcessorTransId());
            detailVO.setAcquirerMerchantNo(authFlow.getCardAcceptorId());
            detailVO.setAcquirerMerchantName(authFlow.getCardAcceptorName());
            detailVO.setOriginalAuthTransId(authFlow.getOriginalId());
            detailVO.setAcquirerMerchantMcc(authFlow.getMcc());
            detailVO.setResponseCode(authFlow.getResponseCode());
            detailVO.setSystemsTraceAuditNumber(authFlow.getSystemsTraceAuditNumber());
            detailVO.setCardNoSuffix(authFlow.getMaskedCardNo());
            detailVO.setRemainingTransAmount(authFlow.getRemainingTransAmount());
            if (authFlow.getRemainingTransAmount() != null) {
                detailVO.setRemainingFrozenAmount(authFlow.getRemainingTransAmount()
                    .subtract(authFlow.getClearAmount() == null ? BigDecimal.ZERO : authFlow.getClearAmount()));
            }
            if (authFlow.getTransDoneTime() != null) {
                detailVO.setAuthorizationDoneTime(
                    DateUtils.formatDate(authFlow.getTransDoneTime(), DateUtils.DATETIME_PATTERN));
            }
            detailVO.setCustomerId(authFlow.getCustomerId());
            detailVO.setAcquirerTid(authFlow.getCardAcceptorTid());
            detailVO.setMti(authFlow.getMti());
            detailVO.setProcessingCode(authFlow.getProcessingCode());
            detailVO.setMarkupAmount(authFlow.getMarkupAmount());
            detailVO.setMarkupRate(authFlow.getMarkupRate());
            detailVO.setConversionRate(authFlow.getConversionRateCardholderBilling());
            detailVO.setClearFlag(authFlow.getClearFlag());
            detailVO.setClearDate(authFlow.getClearAccountingDate());
            detailVO.setCardProductCode(authFlow.getCardProductCode());
            detailVO.setTransFee(authFlow.getTransFee());
            detailVO.setRemainingTransAmount(authFlow.getRemainingTransAmount());
            detailVO.setRemainingBillingAmount(authFlow.getRemainingBillingAmount());
            detailVO.setRemainingMarkupBillingAmount(authFlow.getRemainingMarkupBillingAmount());
            return detailVO;
        }
        return null;
    }
}

