package com.kun.linkage.auth.dto;

import com.kun.common.util.mq.BaseMqMessage;
import com.kun.linkage.account.facade.api.bean.req.AccountReversalReq;
import com.kun.linkage.auth.facade.constant.TransactionTypeCatogoryEnum;
import com.kun.linkage.auth.facade.constant.TransactionTypeEnum;
import com.kun.linkage.auth.facade.vo.BaseAuthRequestVO;
import com.kun.linkage.auth.facade.vo.BaseAuthResponseVO;
import com.kun.linkage.common.db.entity.*;

import java.io.Serializable;

public class AuthTransactionContextDTO extends BaseMqMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Kun-Linkage交易ID
     */
    private String transactionId;

    private BaseAuthRequestVO baseAuthRequestVO;

    private BaseAuthResponseVO baseAuthResponseVO;

    /**
     * 交易类型枚举
     * <p>
     * 包含了各种交易类型的代码、方向和描述。
     */
    private TransactionTypeEnum transactionTypeEnum;

    /**
     * 交易类型分类枚举
     * <p>
     * 用于将交易类型分为授权、退货和冲正/撤销等类别。
     */
    private TransactionTypeCatogoryEnum transactionTypeCatogoryEnum;

    /**
     * 机构信息
     */
    private OrganizationBasicInfo organizationBasicInfo;

    /**
     * 技术参数配置
     */
    private TechnicalParams technicalParams;

    /**
     * 机构用户卡信息
     */
    private OrganizationCustomerCardInfo organizationCustomerCardInfo;

    /**
     * 机构客户基础账户信息
     */
    private OrganizationCustomerAccountInfo customerBasicAccount;

    /**
     * 机构客户数币账户信息
     */
    private OrganizationCustomerAccountInfo customerCryptoAccount;

    /**
     * 机构客户信用账户信息
     */
    private OrganizationCustomerAccountInfo customerCreditAccount;

    /**
     * 授权交易
     */
    private AuthFlow authFlow;

    /**
     * 授权交易扩展信息
     */
    private AuthFlowExt authFlowExt;

    /**
     * 原始授权交易
     */
    private AuthFlow originalAuthFlow;

    /**
     * 原始授权交易扩展信息
     */
    private AuthFlowExt originalAuthFlowExt;

    /**
     * 第一笔原交易
     */
    private AuthFlow firstOriginalAuthFlow;

    private AuthFlowExt firstOriginalAuthFlowExt;

    private AccountReversalReq accountReversalReq;

    /**
     * 重试次数
     */
    private Integer retryTimes;

    private OrganizationFeeTemplateDetail organizationFeeTemplateDetail;

    /**
     * 机构交易记账信息
     * <p> * 用于存储机构的交易记账信息，包括交易金额、币种等。
     */
    private OrganizationTransAccounting organizationTransAccounting;

    /**
     * 商户记账明细(交易本金)
     * <p>
     * 用于存储商户的记账明细信息。
     */
    private OrganizationAccountingDetail merchantAccountingDetail;

    private Boolean merchantAccountingDetailReversalFlag = true;

    public OrganizationFeeTemplateDetail getOrganizationFeeTemplateDetail() {
        return organizationFeeTemplateDetail;
    }

    public void setOrganizationFeeTemplateDetail(OrganizationFeeTemplateDetail organizationFeeTemplateDetail) {
        this.organizationFeeTemplateDetail = organizationFeeTemplateDetail;
    }

    /**
     * 第三方授权标志
     * <p>
     * 用于标识是否为第三方授权交易。
     */
    private boolean thirdPartyAuthorizationFlag;

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public BaseAuthRequestVO getBaseAuthRequestVO() {
        return baseAuthRequestVO;
    }

    public void setBaseAuthRequestVO(BaseAuthRequestVO baseAuthRequestVO) {
        this.baseAuthRequestVO = baseAuthRequestVO;
    }

    public BaseAuthResponseVO getBaseAuthResponseVO() {
        return baseAuthResponseVO;
    }

    public void setBaseAuthResponseVO(BaseAuthResponseVO baseAuthResponseVO) {
        this.baseAuthResponseVO = baseAuthResponseVO;
    }

    public TransactionTypeEnum getTransactionTypeEnum() {
        return transactionTypeEnum;
    }

    public void setTransactionTypeEnum(TransactionTypeEnum transactionTypeEnum) {
        this.transactionTypeEnum = transactionTypeEnum;
    }

    public TransactionTypeCatogoryEnum getTransactionTypeCatogoryEnum() {
        return transactionTypeCatogoryEnum;
    }

    public void setTransactionTypeCatogoryEnum(TransactionTypeCatogoryEnum transactionTypeCatogoryEnum) {
        this.transactionTypeCatogoryEnum = transactionTypeCatogoryEnum;
    }

    public OrganizationBasicInfo getOrganizationBasicInfo() {
        return organizationBasicInfo;
    }

    public void setOrganizationBasicInfo(OrganizationBasicInfo organizationBasicInfo) {
        this.organizationBasicInfo = organizationBasicInfo;
    }

    public TechnicalParams getTechnicalParams() {
        return technicalParams;
    }

    public void setTechnicalParams(TechnicalParams technicalParams) {
        this.technicalParams = technicalParams;
    }

    public OrganizationCustomerCardInfo getOrganizationCustomerCardInfo() {
        return organizationCustomerCardInfo;
    }

    public void setOrganizationCustomerCardInfo(OrganizationCustomerCardInfo organizationCustomerCardInfo) {
        this.organizationCustomerCardInfo = organizationCustomerCardInfo;
    }

    public OrganizationCustomerAccountInfo getCustomerBasicAccount() {
        return customerBasicAccount;
    }

    public void setCustomerBasicAccount(OrganizationCustomerAccountInfo customerBasicAccount) {
        this.customerBasicAccount = customerBasicAccount;
    }

    public OrganizationCustomerAccountInfo getCustomerCryptoAccount() {
        return customerCryptoAccount;
    }

    public void setCustomerCryptoAccount(OrganizationCustomerAccountInfo customerCryptoAccount) {
        this.customerCryptoAccount = customerCryptoAccount;
    }

    public OrganizationCustomerAccountInfo getCustomerCreditAccount() {
        return customerCreditAccount;
    }

    public void setCustomerCreditAccount(OrganizationCustomerAccountInfo customerCreditAccount) {
        this.customerCreditAccount = customerCreditAccount;
    }

    public AuthFlow getAuthFlow() {
        return authFlow;
    }

    public void setAuthFlow(AuthFlow authFlow) {
        this.authFlow = authFlow;
    }

    public AuthFlowExt getAuthFlowExt() {
        return authFlowExt;
    }

    public void setAuthFlowExt(AuthFlowExt authFlowExt) {
        this.authFlowExt = authFlowExt;
    }

    public AuthFlow getOriginalAuthFlow() {
        return originalAuthFlow;
    }

    public void setOriginalAuthFlow(AuthFlow originalAuthFlow) {
        this.originalAuthFlow = originalAuthFlow;
    }

    public AuthFlowExt getOriginalAuthFlowExt() {
        return originalAuthFlowExt;
    }

    public void setOriginalAuthFlowExt(AuthFlowExt originalAuthFlowExt) {
        this.originalAuthFlowExt = originalAuthFlowExt;
    }

    public AuthFlow getFirstOriginalAuthFlow() {
        return firstOriginalAuthFlow;
    }

    public void setFirstOriginalAuthFlow(AuthFlow firstOriginalAuthFlow) {
        this.firstOriginalAuthFlow = firstOriginalAuthFlow;
    }

    public AuthFlowExt getFirstOriginalAuthFlowExt() {
        return firstOriginalAuthFlowExt;
    }

    public void setFirstOriginalAuthFlowExt(AuthFlowExt firstOriginalAuthFlowExt) {
        this.firstOriginalAuthFlowExt = firstOriginalAuthFlowExt;
    }

    public AccountReversalReq getAccountReversalReq() {
        return accountReversalReq;
    }

    public void setAccountReversalReq(AccountReversalReq accountReversalReq) {
        this.accountReversalReq = accountReversalReq;
    }

    public Integer getRetryTimes() {
        return retryTimes;
    }

    public void setRetryTimes(Integer retryTimes) {
        this.retryTimes = retryTimes;
    }

    public boolean isThirdPartyAuthorizationFlag() {
        return thirdPartyAuthorizationFlag;
    }

    public void setThirdPartyAuthorizationFlag(boolean thirdPartyAuthorizationFlag) {
        this.thirdPartyAuthorizationFlag = thirdPartyAuthorizationFlag;
    }

    public OrganizationTransAccounting getOrganizationTransAccounting() {
        return organizationTransAccounting;
    }

    public void setOrganizationTransAccounting(OrganizationTransAccounting organizationTransAccounting) {
        this.organizationTransAccounting = organizationTransAccounting;
    }

    public OrganizationAccountingDetail getMerchantAccountingDetail() {
        return merchantAccountingDetail;
    }

    public void setMerchantAccountingDetail(OrganizationAccountingDetail merchantAccountingDetail) {
        this.merchantAccountingDetail = merchantAccountingDetail;
    }

    public Boolean getMerchantAccountingDetailReversalFlag() {
        return merchantAccountingDetailReversalFlag;
    }

    public void setMerchantAccountingDetailReversalFlag(Boolean merchantAccountingDetailReversalFlag) {
        this.merchantAccountingDetailReversalFlag = merchantAccountingDetailReversalFlag;
    }
}
