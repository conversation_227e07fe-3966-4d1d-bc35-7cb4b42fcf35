package com.kun.linkage.auth.mq;

import brave.Tracer;
import com.alibaba.fastjson.JSON;
import com.kun.common.util.lark.LarkAlarmUtil;
import com.kun.common.util.mq.RocketMqService;
import com.kun.linkage.auth.facade.vo.mq.OrganizationTransAccountingReversalVO;
import com.kun.linkage.auth.service.OrganizationAccountingDetailService;
import com.kun.linkage.auth.service.TransactionAccountingService;
import com.kun.linkage.auth.utils.CacheKeyUtil;
import com.kun.linkage.common.base.constants.MqConsumerGroupConstant;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.base.constants.OrganizationAccountingDetailAccountingProcessorEnum;
import com.kun.linkage.common.base.enums.OperationStatusEnum;
import com.kun.linkage.common.db.entity.OrganizationAccountingDetail;
import com.kun.linkage.common.redis.utils.RedissonLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RLock;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@RocketMQMessageListener(topic = MqTopicConstant.ORGANIZATION_TRANS_ACCOUNTING_REVERSAL_TOPIC,
    consumerGroup = MqConsumerGroupConstant.KL_AUTH_ORGANIZATION_ACCOUNTING_REVERSAL_GROUP,
    messageModel = MessageModel.CLUSTERING)
public class OrganizationTransAccountingReversalConsumer
    implements RocketMQListener<OrganizationTransAccountingReversalVO> {

    @Resource
    private RedissonLockUtil redissonLockUtil;
    @Resource
    private LarkAlarmUtil larkAlarmUtil;
    @Resource
    private Tracer tracer;

    @Resource
    private OrganizationAccountingDetailService organizationAccountingDetailService;

    @Resource
    private TransactionAccountingService transactionAccountingService;

    @Resource
    private RocketMqService rocketMqService;

    // 重试次数和延迟时间配置
    private static final int[] RETRY_DELAYS = {MqTopicConstant.DELAY_LEVEL_1S,    // 1秒
        MqTopicConstant.DELAY_LEVEL_10S,    // 10秒
        MqTopicConstant.DELAY_LEVEL_30S,    // 30秒
        MqTopicConstant.DELAY_LEVEL_1M,    // 1分钟
        MqTopicConstant.DELAY_LEVEL_5M, // 5分钟
        MqTopicConstant.DELAY_LEVEL_30M // 30分钟
    };
    private static final int MAX_RETRY_TIMES = RETRY_DELAYS.length;

    @NewSpan
    @Override
    public void onMessage(OrganizationTransAccountingReversalVO message) {
        // 设置日志追踪上下文
        if (tracer.currentSpan() != null && message.getLogContext() != null) {
            tracer.currentSpan().tag("traceId", message.getLogContext().getTraceId());
        }
        log.info("[商户交易账冲账]接收到消息: {}", JSON.toJSONString(message));
        RLock lock = null;
        boolean retryFlag = false;
        OrganizationAccountingDetail detail = null;
        try {
            if (StringUtils.isBlank(message.getOrganizationAccountingDetailId()) || message.getCreateTime() == null) {
                log.error("[商户交易账冲账]请求参数异常,请检查,请求参数:{}", JSON.toJSONString(message));
                return;
            }
            // 获取分布式锁
            String merchantAccountingReversalKey =
                CacheKeyUtil.getMerchantAccountingReversalKey(message.getOrganizationAccountingDetailId());
            lock = redissonLockUtil.getLock(merchantAccountingReversalKey);
            if (lock == null || !lock.tryLock()) {
                log.warn("[商户交易账冲账]获取锁失败, lockKey: {}", merchantAccountingReversalKey);
                // 如果获取锁失败，直接返回
                retryFlag = true;
            } else {
                detail = organizationAccountingDetailService.selectDataByIdAndCreateTime(
                    message.getOrganizationAccountingDetailId(), message.getCreateTime());
                if (detail == null) {
                    // 如果未找到对应的记账记录，直接返回
                    log.error("[商户交易账冲账]未找到对应的记账记录, 记账ID: {}, 创建时间: {}",
                        message.getOrganizationAccountingDetailId(), message.getCreateTime().toLocaleString());
                    return;
                }
                OrganizationAccountingDetailAccountingProcessorEnum processorEnum =
                    OrganizationAccountingDetailAccountingProcessorEnum.fromValue(detail.getAccountingProcessor());
                switch (processorEnum) {
                    case KUN:
                        // 处理数币兑换的冲账逻辑
                        transactionAccountingService.processingDigitalCurrencyAccountingReversal(detail);
                        break;
                    case PAYX:
                        // 处理法币兑换的冲账逻辑
                        transactionAccountingService.processingFiatCurrencyAccountingReversal(detail);
                        break;
                    default:
                        log.error("[商户交易账冲账]未知的处理方: {}", detail.getAccountingProcessor());
                        return;
                }
            }
        } catch (Exception e) {
            log.error("[商户交易账冲账]请求参数异常", e);
            retryFlag = true;
        } finally {
            if (detail != null && StringUtils.isNotBlank(detail.getAccountingReversalStatus()) && StringUtils.equals(
                detail.getAccountingReversalStatus(), OperationStatusEnum.PENDING.getStatus())) {
                organizationAccountingDetailService.updateAccountingReversalResult(detail);
            }
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                // 释放锁
                redissonLockUtil.unlock(lock);
            }
            // 如果需要重试且冲账次数小于最大重试次数，重新发送消息
            if (retryFlag && detail != null && detail.getAccountingReversalCount() < MAX_RETRY_TIMES) {
                // 如果需要重试且冲账次数小于最大重试次数，重新发送消息
                log.warn("[商户交易账冲账]事件处理失败, {}秒后进行重试",
                    RETRY_DELAYS[detail.getAccountingReversalCount()]);
                this.sendRetryToMq(message, detail.getOrganizationNo(), detail.getAccountingReversalCount());
            }
            // 发送告警
            if (retryFlag && (detail != null && detail.getAccountingReversalCount() >= MAX_RETRY_TIMES)) {
                log.warn("[商户交易账冲账]重试后依旧失败,发送LARK告警");
                String msg = String.format(
                    "[商户交易账冲账异常]机构号:%s, 记账ID:%s, 创建时间:%s, traceId:%s, 异常信息:重试次数超过最大限制",
                    detail.getOrganizationNo(), detail.getId(), detail.getCreateTime().toLocaleString(),
                    tracer.currentSpan().context().traceIdString());
                larkAlarmUtil.sendTextAlarm(msg);
            }
        }
    }

    /**
     * 发送商户交易账冲账消息到MQ
     *
     * @param message
     * @param organizationNo
     * @param accountingReversalCount
     */
    private void sendRetryToMq(OrganizationTransAccountingReversalVO message, String organizationNo,
        Integer accountingReversalCount) {
        try {
            if (message == null || StringUtils.isBlank(
                message.getOrganizationAccountingDetailId()) || message.getCreateTime() == null) {
                log.error("[商户交易账冲账]发送消息失败,请求参数异常,请检查,请求参数:{}", JSON.toJSONString(message));
                return;
            }
            message.getLogContext().setTraceId(tracer.currentSpan().context().traceIdString());
            message.getLogContext().setSpanId(tracer.currentSpan().context().spanIdString());
            rocketMqService.delayedSend(MqTopicConstant.ORGANIZATION_TRANS_ACCOUNTING_REVERSAL_TOPIC, message, 2000,
                MqTopicConstant.RETRY_DELAYS[accountingReversalCount]);
        } catch (Exception e) {
            log.error("[商户交易账冲账]发送消息失败,异常信息:{}", e.getMessage(), e);
            String errorMsg = e.getMessage();
            if (StringUtils.isBlank(errorMsg)) {
                errorMsg = "未知异常";
            }
            if (errorMsg.length() > 128) {
                errorMsg = errorMsg.substring(0, 128);
            }
            // 发送Lark告警
            String msg = String.format("[商户交易账冲账异常]机构号:%s, 记账ID:%s, 创建时间:%s, traceId:%s, 异常信息:%s",
                organizationNo, message.getOrganizationAccountingDetailId(), message.getCreateTime().toLocaleString(),
                tracer.currentSpan().context().traceIdString(), errorMsg);
            larkAlarmUtil.sendTextAlarm(msg);
        }
    }
}
