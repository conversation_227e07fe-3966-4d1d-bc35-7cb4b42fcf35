package com.kun.linkage.auth.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kun.common.util.uid.DateUtils;
import com.kun.linkage.auth.dto.AuthTransactionContextDTO;
import com.kun.linkage.auth.ext.mapper.AuthFlowMapperExt;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.AuthFlow;
import com.kun.linkage.common.db.entity.AuthFlowExt;
import com.kun.linkage.common.db.mapper.AuthFlowExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class AuthFlowExtService {

    @Resource
    private AuthFlowExtMapper authFlowExtMapper;

    @Resource
    protected AuthFlowMapperExt authFlowMapperExt;

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public int insertAuthFlowExt(AuthFlowExt authFlowExt) {
        int insertRows = authFlowExtMapper.insert(authFlowExt);
        log.info("插入授权交易扩展记录: {}, 影响行数: {}", JSON.toJSONString(authFlowExt), insertRows);
        return insertRows;
    }

    /**
     * 更新授权交易扩展记录
     *
     * @param authTransactionContextDTO
     * @param requestNo
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void updateAuthFlowExt(AuthTransactionContextDTO authTransactionContextDTO, String requestNo) {
        AuthFlowExt updatedAuthFlowExt = new AuthFlowExt();
        updatedAuthFlowExt.setAuthFlowId(authTransactionContextDTO.getAuthFlowExt().getAuthFlowId());
        updatedAuthFlowExt.setCardholderAccountRequestNo(requestNo);

        int updateRows = authFlowExtMapper.update(updatedAuthFlowExt,
            new LambdaQueryWrapper<AuthFlowExt>().eq(AuthFlowExt::getAuthFlowId,
                    authTransactionContextDTO.getAuthFlowExt().getAuthFlowId())
                .eq(AuthFlowExt::getCreateTime, authTransactionContextDTO.getAuthFlowExt().getCreateTime()));

        log.info("更新交易扩展记录: {}, 影响行数: {}", JSON.toJSONString(updatedAuthFlowExt), updateRows);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public int updateRemainingAmountInDb(AuthFlow originalAuthFlow, BigDecimal transAmount, BigDecimal billingAmount,
        BigDecimal billingAmountWithMarkup, boolean needLimitAmount) {
        return authFlowMapperExt.updateRemainingTransAmount(
            DateUtils.formatDate(originalAuthFlow.getCreateTime(), "yyyyMM"), originalAuthFlow.getId(), transAmount,
            billingAmount, billingAmountWithMarkup, DateTimeUtils.getCurrentDateTime(), needLimitAmount);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public int updateAuthFlowExtByIdAndCreateTime(AuthFlowExt updatedAuthFlowExt, String authFlowId, Date createTime) {
        return authFlowExtMapper.update(updatedAuthFlowExt,
            new LambdaQueryWrapper<AuthFlowExt>().eq(AuthFlowExt::getAuthFlowId, authFlowId)
                .eq(AuthFlowExt::getCreateTime, createTime));
    }

    public AuthFlowExt selectAuthFlowExtByOriginalAuthFlowId(String originalAuthFlowId, Date originalCreateTime) {
        authFlowExtMapper.selectOne(
            new LambdaQueryWrapper<AuthFlowExt>().eq(AuthFlowExt::getAuthFlowId, originalAuthFlowId)
                .between(AuthFlowExt::getCreateTime, originalCreateTime,
                    DateTimeUtils.truncateToSecond(DateUtils.addDays(originalCreateTime, 1))));
        return authFlowExtMapper.selectOne(
            new LambdaQueryWrapper<AuthFlowExt>().eq(AuthFlowExt::getAuthFlowId, originalAuthFlowId));
    }
}
