package com.kun.linkage.auth.constant;

public class AuthApplicationConstant {

    /**
     * 冒号:{@value}
     */
    public static final String COLON_SYMBOL = ":";

    /**
     * 减号/连接符:{@value}
     */
    public final static String MINUS_SYMBOL = "-";

    /**
     * 斜杠:{@value}
     */
    public final static String SLASH_SYMBOL = "/";

    /**
     * 应用名:{@value}
     */
    public final static String APPLICATION_NAME = "kun-linkage-auth";

    /**
     * 授权交易Lock Key前缀
     */
    public static final String AUTH_TRANSACTION_LOCK_KEY_PREFIX = "kun-linkage_auth:transaction_key";

    /**
     * 卡交易Lock Key前缀
     */
    public static final String CARD_TRANSACTION_LOCK_KEY_PREFIX = "kun-linkage_auth:card_transaction_key";

    /**
     * 授权码缓存的前缀
     */
    public static final String APPROVE_CODE_CACHE_KEY_PREFIX = "kun-linkage_auth:approve_code:";

    /**
     * 商户交易账冲正锁的前缀
     * <p>
     * 用于商户会计冲正操作的分布式锁，确保同一时间只有一个线程可以进行冲正操作。
     */
    public static final Object MERCHANT_ACCOUNTING_REVERSAL_LOCK_KEY_PREFIX = "kun-linkage_auth:merchant_accounting:";
}
