package com.kun.linkage.auth.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.db.entity.OrganizationBasicInfo;
import com.kun.linkage.common.external.facade.api.kcard.KCardKunAccountFacade;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXDirectionEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunSideTypeEnum;
import com.kun.linkage.common.external.facade.api.kcard.req.KunAskPriceReq;
import com.kun.linkage.common.external.facade.api.kcard.req.KunDebitSubRefundReq;
import com.kun.linkage.common.external.facade.api.kcard.req.KunDebitSubReq;
import com.kun.linkage.common.external.facade.api.kcard.res.KunAskPriceRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.KunDebitSubRefundRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.KunDebitSubRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

@Slf4j
@Service
public class KunDigitalCurrencyService {

    @Resource
    private KCardKunAccountFacade kCardKunAccountFacade;

    /**
     * 请求数币兑换价格
     *
     * @param organizationBasicInfo 机构基本信息
     * @param amount                兑换金额
     * @param sideType              交易类型
     *                              <p>
     *                              1. BUY: 买入 2. SELL: 卖出
     *                              </p>
     *                              <p>
     *                              注意：kun接口币对必须数币在前法币在后
     *                              </p>
     *                              <p>
     *                              例如：数币BTC兑换法币CNY，sideType为BUY时，kun接口的币对应为BTC_CNY；
     *                              数币CNY兑换法币BTC，sideType为SELL时，kun接口的币对应为CNY_BTC。
     *                              </p>
     *                              <p>
     *                              注意：kun接口币对必须数币在前法币在后
     *                              </p>
     *                              <p>
     *                              例如：数币BTC兑换法币CNY，sideType为BUY时，kun接口的币对应为BTC_CNY；
     *                              数币CNY兑换法币BTC，sideType为SELL时，kun接口的币对应为CNY_BTC。
     *                              </p>
     * @param dicitalCurrencyCode   数币币种
     * @param targetCurrencyCode    目标币种
     * @return
     */
    public BigDecimal askPrice(OrganizationBasicInfo organizationBasicInfo, BigDecimal amount, KunSideTypeEnum sideType,
        String dicitalCurrencyCode, String targetCurrencyCode) {
        log.info("请求数币兑换价格, 机构号: {}, 金额: {}, 交易类型: {}, 来源币种: {}, 目标币种: {}",
            organizationBasicInfo.getOrganizationNo(), amount, sideType, dicitalCurrencyCode, targetCurrencyCode);
        KunAskPriceReq kunAskPriceReq = new KunAskPriceReq();
        kunAskPriceReq.setToken(organizationBasicInfo.getMpcToken());
        kunAskPriceReq.setGroupProductCode(organizationBasicInfo.getMpcGroupCode());
        kunAskPriceReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        kunAskPriceReq.setAccountNo(organizationBasicInfo.getOrganizationNo());
        kunAskPriceReq.setPayAmount(amount);
        kunAskPriceReq.setSideType(sideType.getType());
        // kun接口币对必须数币在前法币在后
        kunAskPriceReq.setSymbol(dicitalCurrencyCode + "_" + targetCurrencyCode);
        log.info("调用KUN汇率查询接口开始,请求参数:{}", JSON.toJSONString(kunAskPriceReq));
        Result<KunAskPriceRsp> kunAskPriceRspResult = kCardKunAccountFacade.kunExchangeRate(kunAskPriceReq);
        log.info("调用KUN汇率查询接口结束,响应参数:{}", JSON.toJSONString(kunAskPriceRspResult));
        if (kunAskPriceRspResult != null && StringUtils.equals(kunAskPriceRspResult.getCode(), String.valueOf(
            HttpStatus.SC_OK)) && kunAskPriceRspResult.getData() != null && kunAskPriceRspResult.getData()
            .getPrice() != null) {
            // 此处应使用kun接口返回汇率的导数
            return BigDecimal.ONE.divide(kunAskPriceRspResult.getData().getPrice(), 5, RoundingMode.DOWN);
        } else {
            log.error("调用KUN汇率查询接口失败");
            throw new BusinessException(KunLinkageAuthResponseCodeConstant.CALL_KUN_FAIL.getCode());
        }
    }

    /**
     * 调用KUN账户扣除数币
     *
     * @param mpcToken       KUN MPC Token
     * @param mpcGroupCode   KUN MPC Group Code
     * @param organizationNo 机构号
     * @param requestNo      请求号
     * @param direction      {@link KunAndPayXDirectionEnum}
     * @param currencyCode   数币币种
     * @param amount         扣除金额
     * @param remark         备注信息
     * @return
     */
    public Result<KunDebitSubRsp> kunDebitSub(String mpcToken, String mpcGroupCode, String organizationNo,
        String requestNo, String direction, String currencyCode, BigDecimal amount, String remark) {
        KunDebitSubReq kunDebitSubReq = new KunDebitSubReq();
        kunDebitSubReq.setToken(mpcToken);
        kunDebitSubReq.setGroupProductCode(mpcGroupCode);
        kunDebitSubReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        kunDebitSubReq.setAccountNo(organizationNo);
        kunDebitSubReq.setDirection(direction);
        kunDebitSubReq.setRequestNo(requestNo);
        kunDebitSubReq.setCurrency(currencyCode);
        kunDebitSubReq.setAmount(amount);
        kunDebitSubReq.setRemark(remark);
        log.info("调用KUN账户扣除数币开始,请求参数:{}", JSON.toJSONString(kunDebitSubReq));
        Result<KunDebitSubRsp> result = kCardKunAccountFacade.kunDebitSub(kunDebitSubReq);
        log.info("调用KUN账户扣除数币结束,响应参数:{}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 调用KUN账户数币退款
     *
     * @param mpcToken          KUN MPC Token
     * @param mpcGroupCode      KUN MPC Group Code
     * @param organizationNo    机构号
     * @param originalRequestNo 原始请求号
     * @return
     */
    public Result<KunDebitSubRefundRsp> kunDebitSubRefund(String mpcToken, String mpcGroupCode, String organizationNo,
        String originalRequestNo) {
        KunDebitSubRefundReq kunDebitSubReq = new KunDebitSubRefundReq();
        kunDebitSubReq.setToken(mpcToken);
        kunDebitSubReq.setGroupProductCode(mpcGroupCode);
        kunDebitSubReq.setTransSeqNo(String.valueOf(IdWorker.getId()));
        kunDebitSubReq.setAccountNo(organizationNo);
        kunDebitSubReq.setSourceRequestNo(originalRequestNo);
        log.info("调用KUN账户数币退款开始,请求参数:{}", JSON.toJSONString(kunDebitSubReq));
        Result<KunDebitSubRefundRsp> result = kCardKunAccountFacade.kunDebitSubRefund(kunDebitSubReq);
        log.info("调用KUN账户数币退款结束,响应参数:{}", JSON.toJSONString(result));
        return result;
    }
}
