package com.kun.linkage.auth.service;

import brave.Tracer;
import com.alibaba.fastjson.JSON;
import com.kun.common.util.mq.RocketMqService;
import com.kun.common.util.uid.UidGenerator;
import com.kun.linkage.auth.dto.AuthTransactionContextDTO;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.auth.facade.vo.BaseAuthResponseVO;
import com.kun.linkage.auth.facade.vo.mq.OrganizationTransAccountingReversalVO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.constants.MqTopicConstant;
import com.kun.linkage.common.base.constants.OrganizationAccountingDetailAccountingProcessorEnum;
import com.kun.linkage.common.base.constants.OrganizationAccountingDetailBizTypeEnum;
import com.kun.linkage.common.base.constants.OrganizationAccountingDetailBizUsageEnum;
import com.kun.linkage.common.base.dto.OrganizationAccountingDetailExternalRequestAttr;
import com.kun.linkage.common.base.enums.DigitalCurrencyEnum;
import com.kun.linkage.common.base.enums.FiatCurrencyEnum;
import com.kun.linkage.common.base.enums.OperationStatusEnum;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.OrganizationAccountingDetail;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXDebitSubRefundStatusEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXDirectionEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunAndPayXRemarkEnum;
import com.kun.linkage.common.external.facade.api.kcard.enums.KunSideTypeEnum;
import com.kun.linkage.common.external.facade.api.kcard.res.KunDebitSubRefundRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.KunDebitSubRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXDebitSubRefundRsp;
import com.kun.linkage.common.external.facade.api.kcard.res.PayXDebitSubRsp;
import com.kun.linkage.customer.facade.enums.OrganizationPoolCurrencyCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

@Slf4j
@Service
public class TransactionAccountingService {

    /** 唯一ID生成器 */
    @Resource
    protected UidGenerator uidGenerator;

    @Resource
    private OrganizationAccountingDetailService organizationAccountingDetailService;

    @Resource
    private KunDigitalCurrencyService kunDigitalCurrencyService;

    @Resource
    private PayxFiatCurrencyService payxFiatCurrencyService;

    @Resource
    protected RocketMqService rocketMqService;

    @Resource
    private Tracer tracer;

    /**
     * 交易记账(商户账)
     *
     * @return 交易结果
     */
    public BaseAuthResponseVO transactionAccounting(AuthTransactionContextDTO authTransactionContextDTO) {
        // 初始化商户记账信息
        this.initializeOrganizationTransAccounting(authTransactionContextDTO);
        if (DigitalCurrencyEnum.contains(authTransactionContextDTO.getOrganizationBasicInfo().getPoolCurrencyCode())) {
            // 扣除币种是数币
            log.info("交易记商户数币账开始,扣除币种:{}",
                authTransactionContextDTO.getOrganizationBasicInfo().getPoolCurrencyCode());
            return this.handleDigitalCurrencyTransaction(authTransactionContextDTO);
        } else {
            log.info("交易记商户法币账开始,扣除币种:{}",
                authTransactionContextDTO.getOrganizationBasicInfo().getPoolCurrencyCode());
            // 扣除币种是法币
            return this.handleFiatCurrencyTransaction(authTransactionContextDTO);
        }
    }

    /**
     * 初始化商户记账信息
     *
     * @param authTransactionContextDTO 交易上下文
     * @return 商户记账信息
     */
    private OrganizationAccountingDetail initializeOrganizationTransAccounting(
        AuthTransactionContextDTO authTransactionContextDTO) {
        OrganizationAccountingDetail organizationAccountingDetail = new OrganizationAccountingDetail();
        organizationAccountingDetail.setId(String.valueOf(uidGenerator.getUID()));
        organizationAccountingDetail.setBizType(OrganizationAccountingDetailBizTypeEnum.AUTH.getValue());
        organizationAccountingDetail.setBizUsage(OrganizationAccountingDetailBizUsageEnum.PRINCIPAL.getValue());
        organizationAccountingDetail.setOrganizationNo(
            authTransactionContextDTO.getOrganizationBasicInfo().getOrganizationNo());
        organizationAccountingDetail.setCustomerId(
            authTransactionContextDTO.getOrganizationCustomerCardInfo().getCustomerId());
        // 请求时填入
        organizationAccountingDetail.setRequestNo(null);
        // 业务方填写
        organizationAccountingDetail.setExternalRequestAttr(null);
        organizationAccountingDetail.setCardId(authTransactionContextDTO.getOrganizationCustomerCardInfo().getCardId());
        organizationAccountingDetail.setBizTime(authTransactionContextDTO.getAuthFlow().getCreateTime());
        organizationAccountingDetail.setBizAmount(
            authTransactionContextDTO.getAuthFlow().getCardholderMarkupBillingAmount());
        organizationAccountingDetail.setBizCurrencyCode(
            authTransactionContextDTO.getAuthFlow().getCardholderBillingCurrency());
        organizationAccountingDetail.setBizCurrencyPrecision(
            authTransactionContextDTO.getAuthFlow().getCardholderCurrencyExponent());
        organizationAccountingDetail.setBizId(authTransactionContextDTO.getTransactionId());
        // 业务方填写
        organizationAccountingDetail.setFxRate(null);
        organizationAccountingDetail.setAccountingCurrencyCode(
            authTransactionContextDTO.getOrganizationBasicInfo().getPoolCurrencyCode());
        // 业务方填写
        organizationAccountingDetail.setAccountingProcessor(null);
        // 从币种表取精度
        organizationAccountingDetail.setAccountingCurrencyPrecision(
            OrganizationPoolCurrencyCodeEnum.getPrecisionByValue(
                organizationAccountingDetail.getAccountingCurrencyCode()));
        // 业务方填写
        organizationAccountingDetail.setAccountingAmount(null);
        organizationAccountingDetail.setAccountingStatus(OperationStatusEnum.PENDING.getStatus());
        organizationAccountingDetail.setAccountingReversalCount(0);
        organizationAccountingDetail.setCreateTime(DateTimeUtils.getCurrentDateTime());
        organizationAccountingDetail.setLastModifyTime(organizationAccountingDetail.getCreateTime());
        authTransactionContextDTO.setMerchantAccountingDetail(organizationAccountingDetail);
        return organizationAccountingDetail;
    }

    /**
     * 处理数币交易的记账逻辑
     *
     * @param authTransactionContextDTO
     * @return
     */
    private BaseAuthResponseVO handleDigitalCurrencyTransaction(AuthTransactionContextDTO authTransactionContextDTO) {
        OrganizationAccountingDetail merchantAccountingDetail = authTransactionContextDTO.getMerchantAccountingDetail();
        try {
            // 换汇并计算数币记账金额
            this.processingDigitalCurrencyExchange(authTransactionContextDTO);
            merchantAccountingDetail.setAccountingProcessor(
                OrganizationAccountingDetailAccountingProcessorEnum.KUN.getValue());
            // 获取KUN记账属性
            OrganizationAccountingDetailExternalRequestAttr kunAccountingAttr =
                this.getKunAndPayxAccountingAttr(authTransactionContextDTO, merchantAccountingDetail,
                    merchantAccountingDetail.getAccountingProcessor());
            // 插入记账记录
            organizationAccountingDetailService.insertOrganizationAccountingDetail(merchantAccountingDetail);
            try {
                // 更新请求流水号
                merchantAccountingDetail.setRequestNo(String.valueOf(uidGenerator.getUID()));
                log.info("更新记账请求流水号, 记账ID: {}, 请求流水号: {}", merchantAccountingDetail.getId(),
                    merchantAccountingDetail.getRequestNo());
                // 更新请求流水号
                organizationAccountingDetailService.updateRequestNo(merchantAccountingDetail);
                // 调用账户动账
                this.processingDigitalCurrencyAccountingTransaction(authTransactionContextDTO, kunAccountingAttr,
                    merchantAccountingDetail);
                // 更新记账状态
                merchantAccountingDetail.setLastModifyTime(DateTimeUtils.getCurrentDateTime());
                organizationAccountingDetailService.updateAccountingResult(merchantAccountingDetail);
                if (OperationStatusEnum.SUCCESS.getStatus().equals(merchantAccountingDetail.getAccountingStatus())) {
                    // 记账成功
                    return null;
                } else {
                    // 发送冲账事件到MQ
                    log.info("处理卡交易商户数币记账扣款失败,发送冲账事件到mq,交易ID:{}, 扣款币种:{}, 扣除金额:{}",
                        authTransactionContextDTO.getTransactionId(),
                        merchantAccountingDetail.getAccountingCurrencyCode(),
                        merchantAccountingDetail.getAccountingAmount());
                    if (authTransactionContextDTO.getMerchantAccountingDetailReversalFlag()) {
                        merchantAccountingDetail.setAccountingReversalStatus(OperationStatusEnum.PENDING.getStatus());
                        // 如果冲账标志为true, 需要发送冲账事件到MQ
                        this.sendOrganizationAccountingReversalToMq(merchantAccountingDetail);
                    }
                    authTransactionContextDTO.getBaseAuthResponseVO()
                        .setReturnCode(KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
                    return authTransactionContextDTO.getBaseAuthResponseVO();
                }
            } catch (BusinessException e) {
                log.error("处理卡交易商户数币记账扣款业务异常", e);
                if (authTransactionContextDTO.getMerchantAccountingDetailReversalFlag()) {
                    merchantAccountingDetail.setAccountingReversalStatus(OperationStatusEnum.PENDING.getStatus());
                    this.sendOrganizationAccountingReversalToMq(merchantAccountingDetail);
                }
                // 设置错误信息
                authTransactionContextDTO.getBaseAuthResponseVO().setReturnCode(e.getErrorCode());
                return authTransactionContextDTO.getBaseAuthResponseVO();
            } catch (Exception e) {
                log.error("处理卡交易商户数币记账扣款异常,发送冲账事件到mq", e);
                if (authTransactionContextDTO.getMerchantAccountingDetailReversalFlag()) {
                    merchantAccountingDetail.setAccountingReversalStatus(OperationStatusEnum.PENDING.getStatus());
                    this.sendOrganizationAccountingReversalToMq(merchantAccountingDetail);
                }
                authTransactionContextDTO.getBaseAuthResponseVO()
                    .setReturnCode(KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
                return authTransactionContextDTO.getBaseAuthResponseVO();
            }
        } catch (Exception e) {
            log.error("处理卡交易商户数币记账法币记账未知异常", e);
            authTransactionContextDTO.getBaseAuthResponseVO()
                .setReturnCode(KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
            return authTransactionContextDTO.getBaseAuthResponseVO();
        }
    }

    /**
     * 处理卡交易商户数币记账扣款
     *
     * @param authTransactionContextDTO
     * @param kunAccountingAttr
     * @param merchantAccountingDetail
     */
    private void processingDigitalCurrencyAccountingTransaction(AuthTransactionContextDTO authTransactionContextDTO,
        OrganizationAccountingDetailExternalRequestAttr kunAccountingAttr,
        OrganizationAccountingDetail merchantAccountingDetail) {
        Result<KunDebitSubRsp> kunDebitSubRspResult =
            kunDigitalCurrencyService.kunDebitSub(kunAccountingAttr.getMpcToken(), kunAccountingAttr.getMpcGroupCode(),
                merchantAccountingDetail.getOrganizationNo(), merchantAccountingDetail.getRequestNo(),
                merchantAccountingDetail.getAccountingCurrencyCode(), kunAccountingAttr.getDirection(),
                merchantAccountingDetail.getAccountingAmount(), KunAndPayXRemarkEnum.TRANSACTION_BPC.getRemark());
        // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
        if (kunDebitSubRspResult != null && StringUtils.equals(kunDebitSubRspResult.getCode(),
            String.valueOf(HttpStatus.SC_OK)) && kunDebitSubRspResult.getData() != null) {
            if (StringUtils.equals(kunDebitSubRspResult.getData().getStatus(),
                OperationStatusEnum.SUCCESS.getStatus())) {
                // 明确成功
                log.info("处理卡交易商户数币记账扣款成功,交易ID:{}, 扣款币种:{}, 扣除金额:{}",
                    authTransactionContextDTO.getTransactionId(), merchantAccountingDetail.getAccountingCurrencyCode(),
                    merchantAccountingDetail.getAccountingAmount());
                merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.SUCCESS.getStatus());
            } else if (StringUtils.equals(kunDebitSubRspResult.getData().getStatus(),
                OperationStatusEnum.FAIL.getStatus())) {
                // 明确失败不需要冲账
                authTransactionContextDTO.setMerchantAccountingDetailReversalFlag(false);
                log.info("处理卡交易商户数币记账扣款明确失败,交易ID:{}, 扣款币种:{}, 扣除金额:{}",
                    authTransactionContextDTO.getTransactionId(), merchantAccountingDetail.getAccountingCurrencyCode(),
                    merchantAccountingDetail.getAccountingAmount());
                merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.FAIL.getStatus());
                merchantAccountingDetail.setFailMessage(kunDebitSubRspResult.getMessage());
            } else {
                log.info("处理卡交易商户数币记账扣款状态未知,交易ID:{}, 扣款币种:{}, 扣除金额:{}",
                    authTransactionContextDTO.getTransactionId(), merchantAccountingDetail.getAccountingCurrencyCode(),
                    merchantAccountingDetail.getAccountingAmount());
                merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.FAIL.getStatus());
                merchantAccountingDetail.setFailMessage(kunDebitSubRspResult.getMessage());
                // 设置冲账标志为true, 需要冲账
                authTransactionContextDTO.setMerchantAccountingDetailReversalFlag(true);
                merchantAccountingDetail.setAccountingReversalStatus(OperationStatusEnum.PENDING.getStatus());
            }
        } else {
            log.info("处理卡交易商户数币记账扣款异常,交易ID:{}, 扣款币种:{}, 扣除金额:{}",
                authTransactionContextDTO.getTransactionId(), merchantAccountingDetail.getAccountingCurrencyCode(),
                merchantAccountingDetail.getAccountingAmount());
            merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.FAIL.getStatus());
            merchantAccountingDetail.setFailMessage("Unknown error occurred during accounting.");
            merchantAccountingDetail.setAccountingReversalStatus(OperationStatusEnum.PENDING.getStatus());
            // 设置冲账标志为true, 需要冲账
            authTransactionContextDTO.setMerchantAccountingDetailReversalFlag(true);
        }
    }

    /**
     * 处理卡交易商户法币记账扣款
     *
     * @param authTransactionContextDTO
     * @param merchantAccountingDetail
     */
    private OrganizationAccountingDetailExternalRequestAttr getKunAndPayxAccountingAttr(
        AuthTransactionContextDTO authTransactionContextDTO, OrganizationAccountingDetail merchantAccountingDetail,
        String processor) {
        merchantAccountingDetail.setAccountingProcessor(processor);
        OrganizationAccountingDetailExternalRequestAttr kunAccountingAttr =
            new OrganizationAccountingDetailExternalRequestAttr();
        kunAccountingAttr.setMpcToken(authTransactionContextDTO.getOrganizationBasicInfo().getMpcToken());
        kunAccountingAttr.setMpcGroupCode(authTransactionContextDTO.getOrganizationBasicInfo().getMpcGroupCode());
        kunAccountingAttr.setDirection(KunAndPayXDirectionEnum.TO_GROUP.getDirection());
        kunAccountingAttr.setRemark(KunAndPayXRemarkEnum.TRANSACTION_BPC.getRemark());
        merchantAccountingDetail.setExternalRequestAttr(JSON.toJSONString(kunAccountingAttr));
        return kunAccountingAttr;
    }

    /**
     * 处理卡交易商户法币记账法币转数币汇率计算
     *
     * @param authTransactionContextDTO
     */
    private void processingDigitalCurrencyExchange(AuthTransactionContextDTO authTransactionContextDTO) {
        log.info("处理卡交易商户数币记账法币转数币汇率计算,交易ID:{}", authTransactionContextDTO.getTransactionId());
        BigDecimal fxRate = BigDecimal.ONE;
        OrganizationAccountingDetail organizationTransAccounting =
            authTransactionContextDTO.getMerchantAccountingDetail();
        if (!(StringUtils.equals(organizationTransAccounting.getBizCurrencyCode(),
            FiatCurrencyEnum.USD.getCurrencyCode()) && StringUtils.equals(
            organizationTransAccounting.getAccountingCurrencyCode(), DigitalCurrencyEnum.USDT.getValue()))) {
            // 注意注意!!!!  kun的接口币对必须是数币在前、法币在后,返回的也是数币兑法币的汇率
            fxRate = kunDigitalCurrencyService.askPrice(authTransactionContextDTO.getOrganizationBasicInfo(),
                organizationTransAccounting.getBizAmount(), KunSideTypeEnum.BUY,
                organizationTransAccounting.getAccountingCurrencyCode(),
                organizationTransAccounting.getBizCurrencyCode());
        }
        organizationTransAccounting.setFxRate(fxRate);
        log.info("处理卡交易商户数币记账法币转数币汇率计算结束,交易ID:{}, 汇率:{}",
            authTransactionContextDTO.getTransactionId(), organizationTransAccounting.getFxRate());
        // 计算数币记账金额
        organizationTransAccounting.setAccountingAmount(organizationTransAccounting.getBizAmount().multiply(fxRate)
            .setScale(organizationTransAccounting.getAccountingCurrencyPrecision(), RoundingMode.UP));
        log.info("处理卡交易商户数币记账法币转数币汇率计算结束,交易ID:{}, 扣除金额:{}",
            authTransactionContextDTO.getTransactionId(), organizationTransAccounting.getAccountingAmount());
    }

    /**
     * 发送机构记账冲正通知事件到MQ
     *
     * @param organizationTransAccounting
     */
    public void sendOrganizationAccountingReversalToMq(OrganizationAccountingDetail organizationTransAccounting) {
        if (StringUtils.isBlank(organizationTransAccounting.getRequestNo())) {
            log.info("记账请求流水号为空, 无需冲账");
            return;
        }
        if (OperationStatusEnum.SUCCESS.getStatus().equals(organizationTransAccounting)){
            organizationAccountingDetailService.updateAccountingReversalPendingStatus(organizationTransAccounting);
        }
        OrganizationTransAccountingReversalVO organizationTransAccountingReversalVO =
            new OrganizationTransAccountingReversalVO();
        organizationTransAccountingReversalVO.setOrganizationAccountingDetailId(organizationTransAccounting.getId());
        organizationTransAccountingReversalVO.setCreateTime(organizationTransAccounting.getCreateTime());
        organizationTransAccountingReversalVO.getLogContext()
            .setTraceId(tracer.currentSpan().context().traceIdString());
        organizationTransAccountingReversalVO.getLogContext().setSpanId(tracer.currentSpan().context().spanIdString());
        log.info("发送机构记账冲正通知事件到mq, 组织号: {}, 交易ID: {}, 记账ID: {}",
            organizationTransAccounting.getOrganizationNo(), organizationTransAccounting.getBizId(),
            organizationTransAccounting.getId());
        // 发送MQ消息
        rocketMqService.delayedSend(MqTopicConstant.ORGANIZATION_TRANS_ACCOUNTING_REVERSAL_TOPIC,
            organizationTransAccountingReversalVO, 2000,
            MqTopicConstant.RETRY_DELAYS[organizationTransAccounting.getAccountingReversalCount()]);
        log.info("发送机构记账冲正通知事件到mq成功, 组织号: {}, 交易ID: {}, 记账ID: {}",
            organizationTransAccounting.getOrganizationNo(), organizationTransAccounting.getBizId(),
            organizationTransAccounting.getId());
    }

    /**
     * 处理法币交易的记账逻辑
     *
     * @param authTransactionContextDTO
     * @return
     */
    private BaseAuthResponseVO handleFiatCurrencyTransaction(AuthTransactionContextDTO authTransactionContextDTO) {
        OrganizationAccountingDetail merchantAccountingDetail = authTransactionContextDTO.getMerchantAccountingDetail();
        try {
            this.processingFiatCurrencyAccountingTransaction(authTransactionContextDTO);
            merchantAccountingDetail.setAccountingProcessor(
                OrganizationAccountingDetailAccountingProcessorEnum.PAYX.getValue());
            // 获取KUN记账属性
            OrganizationAccountingDetailExternalRequestAttr payxAccountingAttr =
                this.getKunAndPayxAccountingAttr(authTransactionContextDTO, merchantAccountingDetail,
                    merchantAccountingDetail.getAccountingProcessor());
            // 插入记账记录
            organizationAccountingDetailService.insertOrganizationAccountingDetail(merchantAccountingDetail);
            // 处理法币交易逻辑
            try {
                // 更新请求流水号
                merchantAccountingDetail.setRequestNo(String.valueOf(uidGenerator.getUID()));
                log.info("更新记账请求流水号, 记账ID: {}, 请求流水号: {}", merchantAccountingDetail.getId(),
                    merchantAccountingDetail.getRequestNo());
                organizationAccountingDetailService.updateRequestNo(merchantAccountingDetail);
                // 调用账户动账
                this.processingFiatCurrencyAccountingTransaction(authTransactionContextDTO, payxAccountingAttr,
                    merchantAccountingDetail);
                // 更新记账状态
                merchantAccountingDetail.setLastModifyTime(DateTimeUtils.getCurrentDateTime());
                organizationAccountingDetailService.updateAccountingResult(merchantAccountingDetail);
                if (OperationStatusEnum.SUCCESS.getStatus().equals(merchantAccountingDetail.getAccountingStatus())) {
                    // 记账成功
                    return null;
                } else {
                    // 发送冲账事件到MQ
                    log.info("处理卡交易商户法币记账扣款失败,发送冲账事件到mq,交易ID:{}, 扣款币种:{}, 扣除金额:{}",
                        authTransactionContextDTO.getTransactionId(),
                        merchantAccountingDetail.getAccountingCurrencyCode(),
                        merchantAccountingDetail.getAccountingAmount());
                    if (authTransactionContextDTO.getMerchantAccountingDetailReversalFlag()) {
                        // 如果冲账标志为true, 需要发送冲账事件到MQ
                        this.sendOrganizationAccountingReversalToMq(merchantAccountingDetail);
                    }
                    authTransactionContextDTO.getBaseAuthResponseVO()
                        .setReturnCode(KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
                    return authTransactionContextDTO.getBaseAuthResponseVO();
                }
            } catch (BusinessException e) {
                log.error("处理卡交易商户法币记账扣款业务异常", e);
                if (authTransactionContextDTO.getMerchantAccountingDetailReversalFlag()) {
                    this.sendOrganizationAccountingReversalToMq(merchantAccountingDetail);
                }
                // 设置错误信息
                authTransactionContextDTO.getBaseAuthResponseVO().setReturnCode(e.getErrorCode());
                return authTransactionContextDTO.getBaseAuthResponseVO();
            } catch (Exception e) {
                log.error("处理卡交易商户法币记账扣款异常,发送冲账事件到mq", e);
                if (authTransactionContextDTO.getMerchantAccountingDetailReversalFlag()) {
                    this.sendOrganizationAccountingReversalToMq(merchantAccountingDetail);
                }
                authTransactionContextDTO.getBaseAuthResponseVO()
                    .setReturnCode(KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
                return authTransactionContextDTO.getBaseAuthResponseVO();
            }
        } catch (Exception e) {
            log.error("处理卡交易商户法币记账法币记账未知异常", e);
            authTransactionContextDTO.getBaseAuthResponseVO()
                .setReturnCode(KunLinkageAuthResponseCodeConstant.ACCOUNTING_FAIL.getCode());
            return authTransactionContextDTO.getBaseAuthResponseVO();
        }
    }

    /**
     * 处理卡交易商户法币记账扣款
     *
     * @param authTransactionContextDTO
     * @param kunAccountingAttr
     * @param merchantAccountingDetail
     */
    private void processingFiatCurrencyAccountingTransaction(AuthTransactionContextDTO authTransactionContextDTO,
        OrganizationAccountingDetailExternalRequestAttr kunAccountingAttr,
        OrganizationAccountingDetail merchantAccountingDetail) {
        Result<PayXDebitSubRsp> result =
            payxFiatCurrencyService.payxDebitSub(kunAccountingAttr.getMpcToken(), kunAccountingAttr.getMpcGroupCode(),
                merchantAccountingDetail.getOrganizationNo(), merchantAccountingDetail.getRequestNo(),
                merchantAccountingDetail.getAccountingCurrencyCode(), kunAccountingAttr.getDirection(),
                merchantAccountingDetail.getAccountingAmount(), KunAndPayXRemarkEnum.TRANSACTION_BPC.getRemark());
        // 此处注意不能用Result中的isSuccess方法来校验是否成功,此处返回的code是kcard那边的200是成功
        if (result != null && StringUtils.equals(result.getCode(),
            String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
            if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.SUCCESS.getStatus())) {
                // 明确成功
                log.info("处理卡交易商户法币记账扣款成功,交易ID:{}, 扣款币种:{}, 扣除金额:{}",
                    authTransactionContextDTO.getTransactionId(), merchantAccountingDetail.getAccountingCurrencyCode(),
                    merchantAccountingDetail.getAccountingAmount());
                merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.SUCCESS.getStatus());
            } else if (StringUtils.equals(result.getData().getStatus(), OperationStatusEnum.FAIL.getStatus())) {
                // 明确失败不需要冲账
                authTransactionContextDTO.setMerchantAccountingDetailReversalFlag(false);
                log.info("处理卡交易商户法币记账扣款明确失败,交易ID:{}, 扣款币种:{}, 扣除金额:{}",
                    authTransactionContextDTO.getTransactionId(), merchantAccountingDetail.getAccountingCurrencyCode(),
                    merchantAccountingDetail.getAccountingAmount());
                merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.FAIL.getStatus());
                merchantAccountingDetail.setFailMessage(result.getMessage());
            } else {
                log.info("处理卡交易商户法币记账扣款状态未知,交易ID:{}, 扣款币种:{}, 扣除金额:{}",
                    authTransactionContextDTO.getTransactionId(), merchantAccountingDetail.getAccountingCurrencyCode(),
                    merchantAccountingDetail.getAccountingAmount());
                merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.FAIL.getStatus());
                merchantAccountingDetail.setFailMessage(result.getMessage());
                // 设置冲账标志为true, 需要冲账
                authTransactionContextDTO.setMerchantAccountingDetailReversalFlag(true);
            }
        } else {
            log.info("处理卡交易商户法币记账扣款异常,交易ID:{}, 扣款币种:{}, 扣除金额:{}",
                authTransactionContextDTO.getTransactionId(), merchantAccountingDetail.getAccountingCurrencyCode(),
                merchantAccountingDetail.getAccountingAmount());
            merchantAccountingDetail.setAccountingStatus(OperationStatusEnum.FAIL.getStatus());
            merchantAccountingDetail.setFailMessage("Unknown error occurred during accounting.");
            // 设置冲账标志为true, 需要冲账
            authTransactionContextDTO.setMerchantAccountingDetailReversalFlag(true);
        }
    }

    /**
     * 获取KUN和PAYX的记账属性
     *
     * @param authTransactionContextDTO 交易上下文
     * @return KUN和PAYX的记账属性
     */
    private void processingFiatCurrencyAccountingTransaction(AuthTransactionContextDTO authTransactionContextDTO) {
        BigDecimal fxRate = BigDecimal.ONE;
        OrganizationAccountingDetail merchantAccountingDetail = authTransactionContextDTO.getMerchantAccountingDetail();
        if (!StringUtils.equals(merchantAccountingDetail.getAccountingCurrencyCode(),
            merchantAccountingDetail.getBizCurrencyCode())) {
            fxRate = payxFiatCurrencyService.askPrice(authTransactionContextDTO.getOrganizationBasicInfo(),
                merchantAccountingDetail.getBizAmount(), merchantAccountingDetail.getBizCurrencyCode(),
                merchantAccountingDetail.getAccountingCurrencyCode());
        }
        merchantAccountingDetail.setFxRate(fxRate);
        log.info("处理卡交易商户法币记账法币转法币汇率计算结束,交易ID:{}, 汇率:{}",
            authTransactionContextDTO.getTransactionId(), merchantAccountingDetail.getFxRate());
        // 计算数币记账金额
        merchantAccountingDetail.setAccountingAmount(merchantAccountingDetail.getBizAmount().multiply(fxRate)
            .setScale(merchantAccountingDetail.getAccountingCurrencyPrecision(), RoundingMode.UP));
        log.info("处理卡交易商户数币记账法币转法币汇率计算结束,交易ID:{}, 扣除金额:{}",
            authTransactionContextDTO.getTransactionId(), merchantAccountingDetail.getAccountingAmount());
    }

    /**
     * 处理卡交易商户数币记账冲账
     *
     * @param detail 记账明细
     */
    public void processingDigitalCurrencyAccountingReversal(OrganizationAccountingDetail detail) {
        if (StringUtils.isBlank(detail.getExternalRequestAttr())) {
            log.error("记账请求扩展属性为空, 无法冲账");
            return;
        }
        OrganizationAccountingDetailExternalRequestAttr kunAccountingAttr =
            JSON.parseObject(detail.getExternalRequestAttr(), OrganizationAccountingDetailExternalRequestAttr.class);
        log.info("处理卡交易商户数币记账冲账开始, 记账ID: {}, 请求流水号: {}, 组织号: {}, 交易ID: {}", detail.getId(),
            detail.getRequestNo(), detail.getOrganizationNo(), detail.getBizId());
        // 调用KUN的冲账接口
        Result<KunDebitSubRefundRsp> result =
            kunDigitalCurrencyService.kunDebitSubRefund(kunAccountingAttr.getMpcToken(),
                kunAccountingAttr.getMpcGroupCode(), detail.getOrganizationNo(), detail.getRequestNo());
        // TODO 数据不存在的情况
        if (result != null && StringUtils.equals(result.getCode(),
            String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
            if (StringUtils.equals(result.getData().getStatus(),
                KunAndPayXDebitSubRefundStatusEnum.SUCCESS.getStatus()) || StringUtils.equals(
                result.getData().getStatus(), KunAndPayXDebitSubRefundStatusEnum.FAIL_PROHIBIT.getStatus())) {
                log.info("处理卡交易商户数币记账冲账成功, 记账ID: {}, 请求流水号: {}, 组织号: {}, 交易ID: {}",
                    detail.getId(), detail.getRequestNo(), detail.getOrganizationNo(), detail.getBizId());
                detail.setAccountingReversalStatus(OperationStatusEnum.SUCCESS.getStatus());
            } else {
                log.error(
                    "处理卡交易商户数币记账冲账失败, 记账ID: {}, 请求流水号: {}, 组织号: {}, 交易ID: {}, 错误信息: {}",
                    detail.getId(), detail.getRequestNo(), detail.getOrganizationNo(), detail.getBizId(),
                    result.getMessage());
                detail.setAccountingReversalStatus(OperationStatusEnum.FAIL.getStatus());
                throw new BusinessException(KunLinkageAuthResponseCodeConstant.CALL_KUN_FAIL.getCode());
            }
        } else {
            log.error(
                "处理卡交易商户数币记账冲账异常, 记账ID: {}, 请求流水号: {}, 组织号: {}, 交易ID: {}, 错误信息: {}",
                detail.getId(), detail.getRequestNo(), detail.getOrganizationNo(), detail.getBizId(),
                result != null ? result.getMessage() : "Unknown error");
            detail.setAccountingReversalStatus(OperationStatusEnum.FAIL.getStatus());
            throw new BusinessException(KunLinkageAuthResponseCodeConstant.CALL_KUN_FAIL.getCode());
        }
    }

    /**
     * 处理卡交易商户法币记账冲账
     *
     * @param detail 记账明细
     */
    public void processingFiatCurrencyAccountingReversal(OrganizationAccountingDetail detail) {
        if (StringUtils.isBlank(detail.getExternalRequestAttr())) {
            log.error("记账请求扩展属性为空, 无法冲账");
            return;
        }
        OrganizationAccountingDetailExternalRequestAttr kunAccountingAttr =
            JSON.parseObject(detail.getExternalRequestAttr(), OrganizationAccountingDetailExternalRequestAttr.class);
        log.info("处理卡交易商户数币记账冲账开始, 记账ID: {}, 请求流水号: {}, 组织号: {}, 交易ID: {}", detail.getId(),
            detail.getRequestNo(), detail.getOrganizationNo(), detail.getBizId());
        Result<PayXDebitSubRefundRsp> result =
            payxFiatCurrencyService.payXDebitSubRefund(kunAccountingAttr.getMpcToken(),
                kunAccountingAttr.getMpcGroupCode(), detail.getOrganizationNo(), detail.getRequestNo());
        if (result != null && StringUtils.equals(result.getCode(),
            String.valueOf(HttpStatus.SC_OK)) && result.getData() != null) {
            if (StringUtils.equals(result.getData().getStatus(),
                KunAndPayXDebitSubRefundStatusEnum.SUCCESS.getStatus()) || StringUtils.equals(
                result.getData().getStatus(), KunAndPayXDebitSubRefundStatusEnum.FAIL_PROHIBIT.getStatus())) {
                // 撤销完成和失败禁止撤销都算冲账成功
                log.info("处理卡交易商户法币冲账成功");
                detail.setAccountingReversalStatus(OperationStatusEnum.SUCCESS.getStatus());
            } else {
                log.error("处理卡交易商户法币冲账失败");
                detail.setAccountingReversalStatus(OperationStatusEnum.FAIL.getStatus());
                throw new BusinessException(KunLinkageAuthResponseCodeConstant.CALL_PAYX_FAIL.getCode());
            }
        } else {
            log.error("处理卡交易商户法币冲账状态未知");
            detail.setAccountingReversalStatus(OperationStatusEnum.FAIL.getStatus());
            throw new BusinessException(KunLinkageAuthResponseCodeConstant.CALL_PAYX_FAIL.getCode());
        }
    }
}
