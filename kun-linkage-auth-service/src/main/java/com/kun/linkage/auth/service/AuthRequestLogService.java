package com.kun.linkage.auth.service;

import com.alibaba.fastjson.JSON;
import com.kun.common.util.uid.UidGenerator;
import com.kun.linkage.auth.dto.AuthTransactionContextDTO;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.AuthRequestLog;
import com.kun.linkage.common.db.mapper.AuthRequestLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Slf4j
@Service
public class AuthRequestLogService {

    @Resource
    protected AuthRequestLogMapper authRequestLogMapper;

    /** 唯一ID生成器 */
    @Resource
    protected UidGenerator uidGenerator;

    /**
     * 保存请求日志
     * <p>
     * 生成唯一交易ID，保存请求上下文到数据库日志表。 使用雪花算法生成唯一ID，确保分布式环境下的ID唯一性。
     *
     * @param authTransactionContextDTO 交易上下文，包含请求信息和响应信息
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void saveRequestLog(AuthTransactionContextDTO authTransactionContextDTO) {
        long snowId = uidGenerator.getUID();
        authTransactionContextDTO.setTransactionId(String.valueOf(snowId));
        AuthRequestLog authRequestLog = new AuthRequestLog();
        authRequestLog.setAuthFlowId(snowId);
        authRequestLog.setRequestJson(JSON.toJSONString(authTransactionContextDTO.getBaseAuthRequestVO()));
        authRequestLog.setCreateTime(DateTimeUtils.getCurrentDateTime());
        int insert = authRequestLogMapper.insert(authRequestLog);
        log.info("保存请求日志: {}, 影响行数: {}", authRequestLog, insert);
    }
}
