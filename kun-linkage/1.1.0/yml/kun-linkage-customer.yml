lark:
  config:
    item-map:
      # 扣款重试通知
      DEBITSUB-RETRY:
        enabled: true
        webhook: https://open.feishu.cn/open-apis/bot/v2/hook/5c0f5c0c-c5c5-4c5c-9c5c-5c5c5c5c5c5c
        secret: 5c0f5c0c-c5c5-4c5c-9c5c-5c5c5c5c5c5c

spring:
  rules:
    sharding:
      tables:
        kl_organization_fee_detail:
          actual-data-nodes: ds0.kl_organization_fee_detail_$->{202505..203512}
          table-strategy:
            standard:
              sharding-column: transaction_datetime
              sharding-algorithm-name: local-date-time-dynamic-year-month
        kl_account_info:
          actual-data-nodes: ds0.kl_account_info_$->{0..7}
          table-strategy:
            standard:
              sharding-column: business_organization_no
              sharding-algorithm-name: kl-account-info-table-inline
        kl_organization_fee_detail:
          actual-data-nodes: ds0.kl_organization_fee_detail_$->{202505..203512}
          table-strategy:
            standard:
              sharding-column: transaction_datetime
              sharding-algorithm-name: local-date-time-dynamic-year-month
      sharding-algorithms:
        local-date-time-dynamic-year-month:
          type: CLASS_BASED
          props:
            strategy: standard
            algorithmClassName: com.kun.linkage.common.db.sharding.algorithm.LocalDateTimeDynamicYearMonthShardingAlgorithm
            sharding-suffix-pattern: yyyyMM
        kl-account-info-table-inline:
          type: INLINE
          props:
            algorithm-expression: kl_account_info_${Long.parseLong(business_organization_no.replaceAll("[^0-9]", "")) % 8}