2025-07-28 16:13:40.967 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-28 16:13:41.038 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-28 16:13:41.729 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-28 16:13:41.729 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-28 16:13:43.581 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth,DEFAULT_GROUP'}]
2025-07-28 16:13:43.632 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-28 16:13:44.869 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-28 16:13:44.874 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-28 16:13:44.904 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-28 16:13:45.106 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-28 16:13:45.440 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=0b0ad61f-03b2-3c7a-8dd1-ec5169fb1ae1
2025-07-28 16:13:45.570 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 16:13:45.571 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 16:13:45.571 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/177589009] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 16:13:45.572 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 16:13:45.574 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 16:13:45.579 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 16:13:46.103 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$4fcbfb54] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 16:13:47.037 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-28 16:13:47.037 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3387 ms
2025-07-28 16:13:57.746 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-28 16:14:06.865 [main] INFO  [  ,  ] com.kun.linkage.auth.config.ApproveCodeProperties.initPrefixIfNeeded:22 - 自动生成 instancePrefix: ZO
2025-07-28 16:14:07.517 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-28 16:14:09.344 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-28 16:14:11.783 [redisson-netty-2-20] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-28 16:14:12.569 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:49
2025-07-28 16:14:13.435 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-28 16:14:13.435 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-28 16:14:13.435 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-28 16:14:20.348 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 16:14:21.494 [main] INFO  [  ,  ] com.kun.linkage.auth.config.HttpClientConfig.restTemplate:60 - HTTP客户端配置完成 - 最大连接数: 200, 每路由最大连接数: 50, 连接超时: 5000ms, 读取超时: 30000ms
2025-07-28 16:14:21.705 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-07-28 16:14:27.880 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-28 16:14:27.881 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-28 16:14:33.895 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:10911] result: true
2025-07-28 16:14:33.895 [NettyClientSelector_1] INFO  [  ,  ] RocketmqRemoting.info:95 - closeChannel: close the connection to remote address[**********:9876] result: true
2025-07-28 16:14:34.031 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:143 - 开始安全销毁外部API异步调用线程池...
2025-07-28 16:14:34.032 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:151 - 线程池销毁前状态: Active=0, QueueSize=0, CompletedTasks=0
2025-07-28 16:14:34.032 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:161 - 外部API异步调用线程池已安全销毁，所有任务已完成
2025-07-28 16:14:34.032 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:143 - 开始安全销毁外部API异步调用线程池...
2025-07-28 16:14:34.032 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:151 - 线程池销毁前状态: Active=0, QueueSize=0, CompletedTasks=0
2025-07-28 16:14:34.033 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.destroy:161 - 外部API异步调用线程池已安全销毁，所有任务已完成
2025-07-28 16:14:34.035 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2138 - {dataSource-1} closing ...
2025-07-28 16:14:34.053 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.close:2211 - {dataSource-1} closed
2025-07-28 16:14:34.073 [main] INFO  [  ,  ] o.s.b.a.l.ConditionEvaluationReportLoggingListener.logMessage:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-28 16:24:22.954 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-28 16:24:23.024 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-28 16:24:23.626 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-28 16:24:23.626 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-28 16:24:25.400 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth,DEFAULT_GROUP'}]
2025-07-28 16:24:25.445 [com.alibaba.nacos.client.remote.worker] INFO  [  ,  ] com.alibaba.nacos.common.remote.client.printIfInfoEnabled:60 - [5a4f9d7c-3d87-40b9-bf98-441a52c8494d_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
2025-07-28 16:24:25.446 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-07-28 16:24:26.631 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-28 16:24:26.636 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-28 16:24:26.666 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-28 16:24:26.879 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-07-28 16:24:27.192 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=9f51b75c-075e-3d99-bef0-2371c16158f0
2025-07-28 16:24:27.323 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 16:24:27.324 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 16:24:27.324 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 16:24:27.325 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 16:24:27.327 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 16:24:27.332 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 16:24:27.844 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$4b70f628] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 16:24:28.789 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-07-28 16:24:28.789 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3326 ms
2025-07-28 16:24:39.273 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-07-28 16:24:48.113 [main] INFO  [  ,  ] com.kun.linkage.auth.config.ApproveCodeProperties.initPrefixIfNeeded:22 - 自动生成 instancePrefix: ZO
2025-07-28 16:24:48.744 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-07-28 16:24:49.950 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-28 16:24:52.625 [redisson-netty-2-19] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.0.69:6379
2025-07-28 16:24:53.319 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:52
2025-07-28 16:24:54.198 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-07-28 16:24:54.198 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-28 16:24:54.198 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-07-28 16:25:01.267 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 16:25:02.243 [main] INFO  [  ,  ] com.kun.linkage.auth.config.HttpClientConfig.restTemplate:60 - HTTP客户端配置完成 - 最大连接数: 200, 每路由最大连接数: 50, 连接超时: 5000ms, 读取超时: 30000ms
2025-07-28 16:25:02.443 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-07-28 16:25:02.667 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 16:25:02.682 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-07-28 16:25:03.099 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-28 16:25:04.421 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-07-28 16:25:04.468 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 16:25:04.469 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-07-28 16:25:04.479 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-07-28 16:25:04.481 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 16:25:04.481 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 16:25:04.481 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-07-28 16:25:04.482 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@31201bf6
2025-07-28 16:25:07.567 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-28 16:25:20.219 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_TRANSACTION_FEE_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_TRANSACTION_FEE_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-28 16:25:20.220 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:transactionFeeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-28 16:25:31.393 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_ACCOUNTING_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-07-28 16:25:31.394 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:reversalAccountingListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-28 16:25:31.515 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-07-28 16:25:31.551 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-07-28 16:25:31.567 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-07-28 16:25:31.651 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-07-28 16:25:31.744 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 9012 (http)
2025-07-28 16:25:31.781 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-28 16:25:31.782 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-28 16:25:32.341 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-auth 172.19.151.145:9012 register finished
2025-07-28 16:25:32.345 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-07-28 16:25:32.346 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 16:25:32.375 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStarted:61 - Started KunLinkageAuthServiceApplication in 69.759 seconds (JVM running for 76.237)
2025-07-28 16:25:32.407 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-auth.properties, group=DEFAULT_GROUP
2025-07-28 16:25:32.407 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-auth-local.properties, group=DEFAULT_GROUP
2025-07-28 16:25:32.407 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-auth, group=DEFAULT_GROUP
2025-07-28 16:25:32.703 [RMI TCP Connection(6)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-28 16:25:32.703 [RMI TCP Connection(6)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-07-28 16:25:32.710 [RMI TCP Connection(6)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 6 ms
2025-07-28 16:31:08.704 [SpringApplicationShutdownHook] INFO  [  ,  ] org.quartz.core.QuartzScheduler.standby:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
