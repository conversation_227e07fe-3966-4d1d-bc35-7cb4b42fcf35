2025-08-04 10:32:41.592 [main] INFO  [  ,  ] com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-04 10:32:41.694 [background-preinit] INFO  [  ,  ] org.hibernate.validator.internal.util.Version.<clinit>:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-04 10:32:42.671 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 10:32:42.671 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 10:32:44.774 [main] INFO  [  ,  ] o.s.c.b.c.PropertySourceBootstrapConfiguration.doInitialize:134 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth-local.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-kun-linkage-auth,DEFAULT_GROUP'}]
2025-08-04 10:32:44.818 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStartupProfileInfo:638 - The following 1 profile is active: "local"
2025-08-04 10:32:46.010 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-04 10:32:46.014 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 10:32:46.048 [main] INFO  [  ,  ] o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:201 - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-08-04 10:32:46.265 [main] INFO  [  ,  ] com.kun.linkage.common.db.config.MapperConfig.mapperScannerConfigurer:14 - ==============MapperScannerConfigurer==============
2025-08-04 10:32:46.586 [main] INFO  [  ,  ] o.springframework.cloud.context.scope.GenericScope.setSerializationId:283 - BeanFactory id=79bb6a58-5c06-3e7f-b96e-d601fda07e91
2025-08-04 10:32:46.718 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:32:46.720 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:32:46.720 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$541/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:32:46.721 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:32:46.724 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:32:46.729 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:32:47.278 [main] INFO  [  ,  ] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$22b4d80b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:32:48.216 [main] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring embedded WebApplicationContext
2025-08-04 10:32:48.216 [main] INFO  [  ,  ] o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292 - Root WebApplicationContext: initialization completed in 3381 ms
2025-08-04 10:32:59.033 [main] INFO  [  ,  ] com.alibaba.druid.pool.DruidDataSource.init:996 - {dataSource-1} inited
2025-08-04 10:33:09.175 [main] INFO  [  ,  ] com.kun.linkage.auth.config.ApproveCodeProperties.initPrefixIfNeeded:22 - 自动生成 instancePrefix: ZO
2025-08-04 10:33:10.491 [main] INFO  [  ,  ] org.redisson.Version.logVersion:41 - Redisson 3.17.4
2025-08-04 10:33:11.494 [redisson-netty-2-10] INFO  [  ,  ] o.r.connection.pool.MasterPubSubConnectionPool.lambda$createConnection$1:158 - 1 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 10:33:14.285 [redisson-netty-2-19] INFO  [  ,  ] org.redisson.connection.pool.MasterConnectionPool.lambda$createConnection$1:158 - 24 connections initialized for redis.qa.kun/30.19.1.105:6379
2025-08-04 10:33:15.093 [main] INFO  [  ,  ] com.kun.common.util.uid.DefaultUidGenerator.afterPropertiesSet:99 - Initialized bits(1, 28, 22, 13) for workerID:8
2025-08-04 10:33:16.266 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Logic SQL: select 1
2025-08-04 10:33:16.267 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 10:33:16.267 [main] INFO  [  ,  ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: select 1
2025-08-04 10:33:23.391 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kun-linkage-account' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 10:33:24.120 [main] INFO  [  ,  ] com.kun.linkage.auth.config.HttpClientConfig.restTemplate:60 - HTTP客户端配置完成 - 最大连接数: 200, 每路由最大连接数: 50, 连接超时: 5000ms, 读取超时: 30000ms
2025-08-04 10:33:24.259 [main] INFO  [  ,  ] com.kun.linkage.auth.config.AsyncConfig.externalApiAsyncExecutor:77 - 外部API异步调用线程池初始化完成: corePoolSize=8, maxPoolSize=16, queueCapacity=100
2025-08-04 10:33:24.370 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 10:33:24.601 [main] INFO  [  ,  ] o.s.cloud.openfeign.FeignClientFactoryBean.getTarget:418 - For 'kcard-gateway' URL not provided. Will try picking an instance via load-balancing.
2025-08-04 10:33:25.283 [main] INFO  [  ,  ] c.alibaba.cloud.sentinel.SentinelWebMvcConfigurer.addInterceptors:52 - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-04 10:33:27.041 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1220 - Using default implementation for ThreadExecutor
2025-08-04 10:33:27.094 [main] INFO  [  ,  ] org.quartz.core.SchedulerSignalerImpl.<init>:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 10:33:27.094 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.<init>:229 - Quartz Scheduler v.2.3.2 created.
2025-08-04 10:33:27.105 [main] INFO  [  ,  ] org.quartz.simpl.RAMJobStore.initialize:155 - RAMJobStore initialized.
2025-08-04 10:33:27.108 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.initialize:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 10:33:27.108 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-04 10:33:27.108 [main] INFO  [  ,  ] org.quartz.impl.StdSchedulerFactory.instantiate:1378 - Quartz scheduler version: 2.3.2
2025-08-04 10:33:27.109 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.setJobFactory:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@270b527f
2025-08-04 10:33:30.605 [main] INFO  [  ,  ] o.s.b.actuate.endpoint.web.EndpointLinksResolver.<init>:58 - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-04 10:33:43.385 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_TRANSACTION_FEE_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_TRANSACTION_FEE_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 10:33:43.386 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:transactionFeeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-08-04 10:33:54.558 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='AUTH_ACCOUNTING_REVERSAL_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 10:33:54.559 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:reversalAccountingListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-08-04 10:34:05.758 [main] INFO  [  ,  ] o.a.r.s.support.DefaultRocketMQListenerContainer.start:285 - running container: DefaultRocketMQListenerContainer{consumerGroup='KL_AUTH_ORGANIZATION_ACCOUNTING_REVERSAL_GROUP', nameServer='mq.dev.kun:9876', topic='ORGANIZATION_TRANS_ACCOUNTING_EVENT_TOPIC', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING}
2025-08-04 10:34:05.758 [main] INFO  [  ,  ] o.a.r.s.a.ListenerContainerConfiguration.registerContainer:127 - Register the listener to container, listenerBeanName:organizationTransAccountingReversalListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-08-04 10:34:05.878 [main] INFO  [  ,  ] io.undertow.start:120 - starting server: Undertow - 2.2.28.Final
2025-08-04 10:34:05.919 [main] INFO  [  ,  ] org.xnio.<clinit>:95 - XNIO version 3.8.7.Final
2025-08-04 10:34:05.938 [main] INFO  [  ,  ] org.xnio.nio.<clinit>:58 - XNIO NIO Implementation Version 3.8.7.Final
2025-08-04 10:34:06.025 [main] INFO  [  ,  ] org.jboss.threads.<clinit>:52 - JBoss Threads version 3.1.0.Final
2025-08-04 10:34:06.119 [main] INFO  [  ,  ] o.s.boot.web.embedded.undertow.UndertowWebServer.start:119 - Undertow started on port(s) 9012 (http)
2025-08-04 10:34:06.156 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-04 10:34:06.156 [main] INFO  [  ,  ] c.a.n.p.auth.spi.client.ClientAuthPluginManager.init:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-04 10:34:06.723 [main] INFO  [  ,  ] c.a.cloud.nacos.registry.NacosServiceRegistry.register:76 - nacos registry, dev kun-linkage-auth 172.19.151.145:9012 register finished
2025-08-04 10:34:06.728 [main] INFO  [  ,  ] o.s.scheduling.quartz.SchedulerFactoryBean.startScheduler:729 - Starting Quartz Scheduler now
2025-08-04 10:34:06.729 [main] INFO  [  ,  ] org.quartz.core.QuartzScheduler.start:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-04 10:34:06.758 [main] INFO  [  ,  ] c.k.linkage.auth.KunLinkageAuthServiceApplication.logStarted:61 - Started KunLinkageAuthServiceApplication in 85.583 seconds (JVM running for 92.44)
2025-08-04 10:34:06.790 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-auth.properties, group=DEFAULT_GROUP
2025-08-04 10:34:06.790 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-auth-local.properties, group=DEFAULT_GROUP
2025-08-04 10:34:06.790 [main] INFO  [  ,  ] c.a.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:141 - [Nacos Config] Listening config: dataId=kun-linkage-auth, group=DEFAULT_GROUP
2025-08-04 10:34:06.867 [RMI TCP Connection(4)-172.19.151.145] INFO  [  ,  ] io.undertow.servlet.log:389 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 10:34:06.867 [RMI TCP Connection(4)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:525 - Initializing Servlet 'dispatcherServlet'
2025-08-04 10:34:06.878 [RMI TCP Connection(4)-172.19.151.145] INFO  [  ,  ] org.springframework.web.servlet.DispatcherServlet.initServletBean:547 - Completed initialization in 10 ms
2025-08-04 10:37:47.825 [XNIO-1 task-2] INFO  [ f0a2ce6bb2ea0926 , f0a2ce6bb2ea0926 ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs/swagger-config | Status: 200 | Body: {"configUrl":"/... | Total Length: 197
2025-08-04 10:37:48.528 [XNIO-1 task-2] INFO  [ 2e006f69dcb8ecbf , 2e006f69dcb8ecbf ] org.springdoc.api.AbstractOpenApiResource.getOpenApi:355 - Init duration for springdoc-openapi is: 656 ms
2025-08-04 10:37:48.582 [XNIO-1 task-2] INFO  [ 2e006f69dcb8ecbf , 2e006f69dcb8ecbf ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] GET /linkage-auth/v3/api-docs | Status: 200 | Body: {"openapi":"3.0... | Total Length: 31416
2025-08-04 10:43:36.788 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT count(0) FROM kl_auth_flow WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?)
2025-08-04 10:43:36.788 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-08-04 10:43:36.788 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT count(0) FROM kl_auth_flow_202505 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) UNION ALL SELECT count(0) FROM kl_auth_flow_202506 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) UNION ALL SELECT count(0) FROM kl_auth_flow_202507 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) UNION ALL SELECT count(0) FROM kl_auth_flow_202508 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) UNION ALL SELECT count(0) FROM kl_auth_flow_202509 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) UNION ALL SELECT count(0) FROM kl_auth_flow_202510 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ::: [2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********]
2025-08-04 10:43:37.218 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Logic SQL: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ? 
2025-08-04 10:43:37.218 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional[org.apache.shardingsphere.sql.parser.sql.common.segment.dml.pagination.limit.LimitSegment@83a4ec4], lock=Optional.empty, window=Optional.empty)
2025-08-04 10:43:37.218 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202505 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 100]
2025-08-04 10:43:37.218 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202506 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 100]
2025-08-04 10:43:37.219 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202507 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 100]
2025-08-04 10:43:37.219 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202508 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 100]
2025-08-04 10:43:37.219 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202509 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 100]
2025-08-04 10:43:37.219 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] ShardingSphere-SQL.log:74 - Actual SQL: ds0 ::: SELECT  id,processor,processor_request_id,processor_trans_id,original_processor_trans_id,merchant_no,merchant_name,customer_id,status,mti,processing_code,systems_trace_audit_number,card_id,gateway_card_id,processor_card_id,issuer_card_id,masked_card_no,trans_type,card_product_code,trans_currency,trans_currency_exponent,trans_amount,trans_fee,cardholder_billing_currency,cardholder_currency_exponent,cardholder_billing_amount,cardholder_markup_billing_amount,markup_rate,markup_amount,pos_entry_mode,point_pin_code,pos_condition_code,transaction_local_datetime,conversion_rate_cardholder_billing,approve_code,acquire_reference_no,card_acceptor_name,card_acceptor_id,card_acceptor_tid,card_acceptor_country_code,card_acceptor_city,mcc,processor_ext1,remaining_trans_amount,remaining_billing_amount,remaining_markup_billing_amount,response_code,response_msg,original_id,original_processor_request_id,original_trans_time,clear_flag,release_flag,release_time,trans_accounting_date,clear_accounting_date,trans_done_time,clear_amount,clear_bill_amount,clear_bill_amount_with_markup,release_trans_amount,release_markup_billing_amount,third_party_authorization_flag,create_time,update_time  FROM kl_auth_flow_202510 
 
 WHERE (create_time >= ? AND create_time <= ? AND merchant_no = ?) ORDER BY create_time DESC
 LIMIT ?  ::: [2025-01-01 00:00:00.0, 2025-10-01 23:59:59.0, ********, 100]
2025-08-04 10:43:38.316 [XNIO-1 task-2] INFO  [ 27d8bc1d4daef147 , 2a92743d52333d5e ] c.kun.linkage.auth.filter.GlobalResponseLogFilter.doFilter:50 - [HTTP Response] POST /linkage-auth/org/authorization/pageList | Status: 200 | Body: {"code":"0000",... | Total Length: 47500
